/**
 * Test Script for 7-Day Free Trial System
 * Run this in the browser console to test trial functionality
 */

(function() {
    'use strict';

    console.log('🧪 Starting Trial System Test...');

    // Test 1: Check if premium manager is available
    function testPremiumManagerAvailability() {
        console.log('\n📋 Test 1: Premium Manager Availability');

        if (typeof window.StashyPremium === 'undefined') {
            console.error('❌ Premium manager not available');
            console.log('💡 Try running this test in the popup or dashboard where premium manager is loaded');
            return false;
        }

        console.log('✅ Premium manager is available');

        // Check available methods
        const methods = [
            'isPremium', 'isTrialActive', 'getTrialStatus', 'isFeatureAvailable',
            'safeFeatureCheck', 'activateTrial', 'initialize'
        ];

        console.log('🔍 Available methods:');
        methods.forEach(method => {
            const available = typeof window.StashyPremium[method] === 'function';
            console.log(`  ${method}: ${available ? '✅' : '❌'}`);
        });

        return true;
    }

    // Test 2: Check trial status
    function testTrialStatus() {
        console.log('\n📋 Test 2: Trial Status Check');
        
        try {
            const trialStatus = window.StashyPremium.getTrialStatus();
            console.log('Trial Status:', trialStatus);
            
            if (trialStatus.isActive) {
                console.log('✅ Trial is active');
                console.log(`⏰ Days remaining: ${trialStatus.daysRemaining}`);
                console.log(`⏰ Hours remaining: ${trialStatus.hoursRemaining}`);
            } else if (trialStatus.trialUsed) {
                console.log('⚠️ Trial has been used but is not active');
            } else {
                console.log('ℹ️ Trial has not been used yet');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Error checking trial status:', error);
            return false;
        }
    }

    // Test 3: Check premium status (including trial)
    function testPremiumStatus() {
        console.log('\n📋 Test 3: Premium Status Check');
        
        try {
            const isPremium = window.StashyPremium.isPremium();
            const isTrialActive = window.StashyPremium.isTrialActive();
            
            console.log(`Premium status: ${isPremium}`);
            console.log(`Trial active: ${isTrialActive}`);
            
            if (isPremium || isTrialActive) {
                console.log('✅ User has premium access (premium or trial)');
            } else {
                console.log('⚠️ User is on free tier');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Error checking premium status:', error);
            return false;
        }
    }

    // Test 4: Check feature availability
    function testFeatureAvailability() {
        console.log('\n📋 Test 4: Feature Availability Check');

        const features = [
            'ai_features',
            'ai_analysis',
            'ai_summarization',
            'ai_academic_solver',
            'ai_transcript_analysis',
            'ai_shopping_assistant',
            'drive_sync',
            'export_pdf',
            'advanced_highlights'
        ];

        try {
            console.log('🔍 Detailed feature availability check:');

            // Get current status for debugging
            const isPremium = window.StashyPremium.isPremium();
            const isTrialActive = window.StashyPremium.isTrialActive();
            const trialStatus = window.StashyPremium.getTrialStatus();

            console.log(`📊 Current Status: Premium=${isPremium}, Trial=${isTrialActive}`);
            console.log(`📊 Trial Details:`, trialStatus);

            features.forEach(feature => {
                const isAvailable = window.StashyPremium.isFeatureAvailable(feature);
                const safeCheck = window.StashyPremium.safeFeatureCheck ? window.StashyPremium.safeFeatureCheck(feature) : 'N/A';
                console.log(`${feature}: isFeatureAvailable=${isAvailable ? '✅' : '❌'}, safeFeatureCheck=${safeCheck ? '✅' : '❌'}`);
            });

            return true;
        } catch (error) {
            console.error('❌ Error checking feature availability:', error);
            return false;
        }
    }

    // Test 5: Check usage stats
    function testUsageStats() {
        console.log('\n📋 Test 5: Usage Statistics Check');
        
        try {
            const usageStats = window.StashyPremium.getUsageStats();
            console.log('Usage Stats:', usageStats);
            
            if (usageStats.notes && usageStats.notes.unlimited) {
                console.log('✅ Notes: Unlimited (Premium/Trial)');
            } else if (usageStats.notes) {
                console.log(`📊 Notes: ${usageStats.notes.used}/${usageStats.notes.limit}`);
            }
            
            if (usageStats.notebooks && usageStats.notebooks.unlimited) {
                console.log('✅ Notebooks: Unlimited (Premium/Trial)');
            } else if (usageStats.notebooks) {
                console.log(`📊 Notebooks: ${usageStats.notebooks.used}/${usageStats.notebooks.limit}`);
            }
            
            if (usageStats.highlights && usageStats.highlights.unlimited) {
                console.log('✅ Highlights: Unlimited (Premium/Trial)');
            } else if (usageStats.highlights) {
                console.log(`📊 Highlights: ${usageStats.highlights.used}/${usageStats.highlights.limit}`);
            }
            
            return true;
        } catch (error) {
            console.error('❌ Error checking usage stats:', error);
            return false;
        }
    }

    // Test 6: Test trial activation (if available)
    function testTrialActivation() {
        console.log('\n📋 Test 6: Trial Activation Test');
        
        try {
            const trialStatus = window.StashyPremium.getTrialStatus();
            
            if (trialStatus.canActivate) {
                console.log('✅ Trial can be activated');
                console.log('⚠️ To activate trial, run: window.StashyPremium.activateTrial()');
            } else if (trialStatus.trialUsed) {
                console.log('ℹ️ Trial has already been used');
            } else {
                console.log('⚠️ Trial cannot be activated (unknown reason)');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Error testing trial activation:', error);
            return false;
        }
    }

    // Test 7: Check AI snippet buttons
    function testAiSnippetButtons() {
        console.log('\n📋 Test 7: AI Snippet Buttons Check');
        
        const aiButtons = [
            'Stashy-snippet-ai-webpage-btn',
            'Stashy-snippet-ai-video-btn',
            'Stashy-snippet-ai-note-btn',
            'Stashy-snippet-ai-academic-btn',
            'Stashy-snippet-ai-search-btn'
        ];
        
        let foundButtons = 0;
        
        aiButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                console.log(`✅ Found: ${buttonId}`);
                foundButtons++;
            } else {
                console.log(`❌ Missing: ${buttonId}`);
            }
        });
        
        console.log(`📊 Found ${foundButtons}/${aiButtons.length} AI snippet buttons`);
        
        return foundButtons > 0;
    }

    // Test 8: Check Google Drive sync access
    function testDriveSyncAccess() {
        console.log('\n📋 Test 8: Google Drive Sync Access Check');
        
        try {
            const isPremium = window.StashyPremium.isPremium();
            const isTrialActive = window.StashyPremium.isTrialActive();
            const hasDriveAccess = window.StashyPremium.isFeatureAvailable('drive_sync');
            
            console.log(`Drive sync feature available: ${hasDriveAccess}`);
            
            if (hasDriveAccess) {
                console.log('✅ Google Drive sync should be accessible');
            } else {
                console.log('❌ Google Drive sync is locked');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Error checking Drive sync access:', error);
            return false;
        }
    }

    // Run all tests
    function runAllTests() {
        console.log('🚀 Running Trial System Tests...\n');
        
        const tests = [
            testPremiumManagerAvailability,
            testTrialStatus,
            testPremiumStatus,
            testFeatureAvailability,
            testUsageStats,
            testTrialActivation,
            testAiSnippetButtons,
            testDriveSyncAccess
        ];
        
        let passedTests = 0;
        
        tests.forEach((test, index) => {
            try {
                if (test()) {
                    passedTests++;
                }
            } catch (error) {
                console.error(`❌ Test ${index + 1} failed with error:`, error);
            }
        });
        
        console.log(`\n📊 Test Results: ${passedTests}/${tests.length} tests passed`);
        
        if (passedTests === tests.length) {
            console.log('🎉 All tests passed! Trial system appears to be working correctly.');
        } else {
            console.log('⚠️ Some tests failed. Check the issues above.');
        }
    }

    // Expose test functions to global scope
    window.StashyTrialTests = {
        runAllTests,
        testPremiumManagerAvailability,
        testTrialStatus,
        testPremiumStatus,
        testFeatureAvailability,
        testUsageStats,
        testTrialActivation,
        testAiSnippetButtons,
        testDriveSyncAccess
    };

    // Auto-run tests
    runAllTests();

})();

console.log('🧪 Trial system test script loaded. Run StashyTrialTests.runAllTests() to test again.');
