// --- START OF FILE content-highlighting.js ---

// Constants HIGHLIGHT_KEY_PREFIX, HIGHLIGHT_CLASS, HIGHLIGHT_COLORS, DEFAULT_HIGHLIGHT_COLOR
// are defined in content-state.js
const HIGHLIGHT_CONTEXT_LENGTH = 30; // Characters of context

// Enhanced highlighting constants for dynamic content support
const ENHANCED_HIGHLIGHT_VERSION = "2.0"; // Version for enhanced highlight format
const MAX_SELECTOR_DEPTH = 10; // Maximum depth for CSS selector generation
const ELEMENT_FINGERPRINT_ATTRIBUTES = ['id', 'class', 'data-*', 'role', 'aria-label']; // Attributes for element fingerprinting

/**
 * Gets text content from a range, handling potential errors and cross-element selections.
 * @param {Range} range
 * @returns {string}
 */
function getTextFromRange(range) {
    try {
        // First try the standard method
        const standardText = range.toString();

        // For cross-element selections, also try extracting text manually
        // to ensure we get all the text content
        if (range.startContainer !== range.endContainer) {
            const textNodes = getTextNodesInRange(range);
            if (textNodes.length > 0) {
                let fullText = '';
                textNodes.forEach(nodeInfo => {
                    const nodeText = nodeInfo.node.textContent;
                    const extractedText = nodeText.substring(nodeInfo.startOffset, nodeInfo.endOffset);
                    fullText += extractedText;
                });

                // Use the longer text (more complete)
                if (fullText.length > standardText.length) {
                    return fullText;
                }
            }
        }

        return standardText;
    } catch (e) {
        console.warn("Stashy: Error getting text from range", range, e);
        return "";
    }
}

/**
 * Generates a robust CSS selector for an element
 * @param {Element} element - The target element
 * @returns {string} - CSS selector string
 */
function generateRobustCSSSelector(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
        return null;
    }

    const path = [];
    let current = element;
    let depth = 0;

    while (current && current !== document.body && depth < MAX_SELECTOR_DEPTH) {
        let selector = current.tagName.toLowerCase();

        // Add ID if available (most specific)
        if (current.id) {
            selector += `#${CSS.escape(current.id)}`;
            path.unshift(selector);
            break; // ID is unique, we can stop here
        }

        // Add classes if available
        if (current.className && typeof current.className === 'string') {
            const classes = current.className.trim().split(/\s+/)
                .filter(cls => cls && !cls.startsWith('Stashy-')) // Exclude our own classes
                .slice(0, 3); // Limit to first 3 classes to avoid overly long selectors

            if (classes.length > 0) {
                selector += '.' + classes.map(cls => CSS.escape(cls)).join('.');
            }
        }

        // Add nth-child if needed for specificity
        if (current.parentElement) {
            const siblings = Array.from(current.parentElement.children)
                .filter(sibling => sibling.tagName === current.tagName);

            if (siblings.length > 1) {
                const index = siblings.indexOf(current) + 1;
                selector += `:nth-child(${index})`;
            }
        }

        path.unshift(selector);
        current = current.parentElement;
        depth++;
    }

    return path.length > 0 ? path.join(' > ') : null;
}

/**
 * Generates an XPath expression for an element
 * @param {Element} element - The target element
 * @returns {string} - XPath expression
 */
function generateElementXPath(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
        return null;
    }

    const path = [];
    let current = element;
    let depth = 0;

    while (current && current !== document.documentElement && depth < MAX_SELECTOR_DEPTH) {
        let index = 1;
        let sibling = current.previousElementSibling;

        while (sibling) {
            if (sibling.tagName === current.tagName) {
                index++;
            }
            sibling = sibling.previousElementSibling;
        }

        const tagName = current.tagName.toLowerCase();
        const pathSegment = index > 1 ? `${tagName}[${index}]` : tagName;
        path.unshift(pathSegment);

        current = current.parentElement;
        depth++;
    }

    return path.length > 0 ? '//' + path.join('/') : null;
}

/**
 * Creates an element fingerprint for better matching
 * @param {Element} element - The target element
 * @returns {Object} - Element fingerprint object
 */
function createElementFingerprint(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
        return null;
    }

    const fingerprint = {
        tagName: element.tagName.toLowerCase(),
        textContent: element.textContent ? element.textContent.substring(0, 100) : '',
        attributes: {}
    };

    // Collect relevant attributes
    ELEMENT_FINGERPRINT_ATTRIBUTES.forEach(attr => {
        if (attr === 'data-*') {
            // Collect all data attributes
            Array.from(element.attributes).forEach(attribute => {
                if (attribute.name.startsWith('data-') && !attribute.name.startsWith('data-highlight')) {
                    fingerprint.attributes[attribute.name] = attribute.value;
                }
            });
        } else if (element.hasAttribute(attr)) {
            fingerprint.attributes[attr] = element.getAttribute(attr);
        }
    });

    // Add position information relative to parent
    if (element.parentElement) {
        const siblings = Array.from(element.parentElement.children);
        fingerprint.siblingIndex = siblings.indexOf(element);
        fingerprint.siblingCount = siblings.length;
    }

    return fingerprint;
}

/**
 * Serializes a Range object using enhanced context including element anchoring.
 * Enhanced to handle cross-paragraph highlights with better context extraction.
 * @param {Range} range
 * @param {boolean} isCrossParagraph - Whether this is a cross-paragraph highlight
 * @returns {object|null} Object with enhanced serialization data or null on error.
 */
function serializeRangeWithContext(range, isCrossParagraph = false) {
    if (!range || range.collapsed) return null;
    try {
        const text = getTextFromRange(range);
        if (!text.trim()) {
            // console.warn("Stashy: Skipping serialization of empty/whitespace range.");
            return null;
        }

        // For cross-paragraph highlights, use enhanced context extraction
        if (isCrossParagraph) {
            return serializeCrossParagraphContext(range, text);
        }

        // Original single-paragraph logic for backward compatibility
        const prefixRange = document.createRange();
        prefixRange.setStart(document.body, 0);
        prefixRange.setEnd(range.startContainer, range.startOffset);
        const prefix = getTextFromRange(prefixRange).replace(/\s+/g, ' ').slice(-HIGHLIGHT_CONTEXT_LENGTH);

        const suffixRange = document.createRange();
        suffixRange.setStart(range.endContainer, range.endOffset);
        suffixRange.setEnd(document.body, document.body.childNodes.length);
        const suffix = getTextFromRange(suffixRange).replace(/\s+/g, ' ').slice(0, HIGHLIGHT_CONTEXT_LENGTH);

        // Enhanced serialization with element anchoring
        const serialized = {
            // Legacy fields for backward compatibility
            prefix,
            text,
            suffix,
            isCrossParagraph: false,

            // Enhanced fields for dynamic content support
            version: ENHANCED_HIGHLIGHT_VERSION,
            enhancedAnchoring: generateEnhancedAnchoring(range)
        };

        return serialized;
    } catch (e) {
        console.error("Stashy: Error serializing range with context", range, e);
        return null;
    }
}

/**
 * Generates enhanced anchoring information for a range
 * @param {Range} range - The range to analyze
 * @returns {Object} - Enhanced anchoring data
 */
function generateEnhancedAnchoring(range) {
    const anchoring = {
        startElement: null,
        endElement: null,
        startOffset: range.startOffset,
        endOffset: range.endOffset,
        textLength: range.toString().length
    };

    try {
        // Find the closest element containers for start and end
        let startElement = range.startContainer;
        let endElement = range.endContainer;

        // If the container is a text node, get its parent element
        if (startElement.nodeType === Node.TEXT_NODE) {
            startElement = startElement.parentElement;
        }
        if (endElement.nodeType === Node.TEXT_NODE) {
            endElement = endElement.parentElement;
        }

        // Generate anchoring data for start element
        if (startElement) {
            anchoring.startElement = {
                cssSelector: generateRobustCSSSelector(startElement),
                xpath: generateElementXPath(startElement),
                fingerprint: createElementFingerprint(startElement),
                textContent: startElement.textContent ? startElement.textContent.substring(0, 200) : ''
            };
        }

        // Generate anchoring data for end element (if different from start)
        if (endElement && endElement !== startElement) {
            anchoring.endElement = {
                cssSelector: generateRobustCSSSelector(endElement),
                xpath: generateElementXPath(endElement),
                fingerprint: createElementFingerprint(endElement),
                textContent: endElement.textContent ? endElement.textContent.substring(0, 200) : ''
            };
        }

        // Add relative positioning information
        anchoring.relativePosition = calculateRelativePosition(range);

    } catch (error) {
        console.warn("Stashy: Error generating enhanced anchoring:", error);
    }

    return anchoring;
}

/**
 * Calculates relative position information for a range
 * @param {Range} range - The range to analyze
 * @returns {Object} - Relative position data
 */
function calculateRelativePosition(range) {
    const position = {
        startPercentage: 0,
        endPercentage: 0,
        documentLength: 0
    };

    try {
        // Calculate position as percentage of document text
        const documentText = document.body.textContent || '';
        position.documentLength = documentText.length;

        if (position.documentLength > 0) {
            // Create ranges to calculate text position
            const beforeRange = document.createRange();
            beforeRange.setStart(document.body, 0);
            beforeRange.setEnd(range.startContainer, range.startOffset);

            const beforeText = getTextFromRange(beforeRange);
            position.startPercentage = beforeText.length / position.documentLength;

            const beforeEndRange = document.createRange();
            beforeEndRange.setStart(document.body, 0);
            beforeEndRange.setEnd(range.endContainer, range.endOffset);

            const beforeEndText = getTextFromRange(beforeEndRange);
            position.endPercentage = beforeEndText.length / position.documentLength;
        }
    } catch (error) {
        console.warn("Stashy: Error calculating relative position:", error);
    }

    return position;
}

/**
 * Enhanced serialization for cross-paragraph highlights
 * @param {Range} range - The range to serialize
 * @param {string} text - The text content of the range
 * @returns {Object} - Enhanced serialization data
 */
function serializeCrossParagraphContext(range, text) {
    try {
        // Use shorter context for cross-paragraph to avoid confusion
        const CROSS_PARA_CONTEXT_LENGTH = Math.min(30, HIGHLIGHT_CONTEXT_LENGTH);

        // Get text nodes in the range for better analysis
        const textNodes = getTextNodesInRange(range);

        if (textNodes.length === 0) {
            // Fallback to original method
            return serializeOriginalContext(range, text);
        }

        // Extract context from first and last nodes for better accuracy
        const firstNode = textNodes[0];
        const lastNode = textNodes[textNodes.length - 1];

        // Get prefix from before the first node
        let prefix = "";
        try {
            // Get text before selection start in the same node
            if (firstNode.startOffset > 0) {
                prefix = firstNode.node.textContent.substring(
                    Math.max(0, firstNode.startOffset - CROSS_PARA_CONTEXT_LENGTH),
                    firstNode.startOffset
                );
            }

            // If we need more context, look at previous nodes
            if (prefix.length < CROSS_PARA_CONTEXT_LENGTH) {
                const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT);
                walker.currentNode = firstNode.node;

                let prevNode;
                while ((prevNode = walker.previousNode()) && prefix.length < CROSS_PARA_CONTEXT_LENGTH) {
                    const nodeText = prevNode.textContent.trim();
                    if (nodeText) {
                        const needed = CROSS_PARA_CONTEXT_LENGTH - prefix.length;
                        prefix = nodeText.substring(Math.max(0, nodeText.length - needed)) + prefix;
                    }
                }
            }
        } catch (e) {
            console.warn("Stashy: Error getting cross-paragraph prefix:", e);
        }

        // Get suffix from after the last node
        let suffix = "";
        try {
            // Get text after selection end in the same node
            if (lastNode.endOffset < lastNode.node.textContent.length) {
                suffix = lastNode.node.textContent.substring(
                    lastNode.endOffset,
                    Math.min(lastNode.node.textContent.length, lastNode.endOffset + CROSS_PARA_CONTEXT_LENGTH)
                );
            }

            // If we need more context, look at next nodes
            if (suffix.length < CROSS_PARA_CONTEXT_LENGTH) {
                const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT);
                walker.currentNode = lastNode.node;

                let nextNode;
                while ((nextNode = walker.nextNode()) && suffix.length < CROSS_PARA_CONTEXT_LENGTH) {
                    const nodeText = nextNode.textContent.trim();
                    if (nodeText) {
                        const needed = CROSS_PARA_CONTEXT_LENGTH - suffix.length;
                        suffix = suffix + nodeText.substring(0, Math.min(nodeText.length, needed));
                    }
                }
            }
        } catch (e) {
            console.warn("Stashy: Error getting cross-paragraph suffix:", e);
        }

        // Ensure we have the complete text by reconstructing it from text nodes
        let reconstructedText = '';
        textNodes.forEach(nodeInfo => {
            const nodeText = nodeInfo.node.textContent;
            const extractedText = nodeText.substring(nodeInfo.startOffset, nodeInfo.endOffset);
            reconstructedText += extractedText;
        });

        // Use the longer/more complete text
        const finalText = reconstructedText.length > text.length ? reconstructedText : text;



        // Store additional metadata for cross-paragraph highlights
        const serialized = {
            prefix: prefix.replace(/\s+/g, ' ').trim(),
            text: finalText,
            suffix: suffix.replace(/\s+/g, ' ').trim(),
            isCrossParagraph: true,
            segmentCount: textNodes.length,
            // Store first and last segment text for additional validation
            firstSegmentText: textNodes[0].node.textContent.substring(
                textNodes[0].startOffset,
                textNodes[0].endOffset
            ).trim(),
            lastSegmentText: textNodes[textNodes.length - 1].node.textContent.substring(
                textNodes[textNodes.length - 1].startOffset,
                textNodes[textNodes.length - 1].endOffset
            ).trim(),

            // Enhanced fields for dynamic content support
            version: ENHANCED_HIGHLIGHT_VERSION,
            enhancedAnchoring: generateEnhancedAnchoring(range)
        };

        console.log(`Stashy: Serialized cross-paragraph highlight with ${textNodes.length} segments`);
        return serialized;

    } catch (error) {
        console.warn("Stashy: Error in cross-paragraph serialization, falling back:", error);
        return serializeOriginalContext(range, text);
    }
}

/**
 * Original serialization method as fallback
 * @param {Range} range - The range to serialize
 * @param {string} text - The text content
 * @returns {Object} - Original serialization format
 */
function serializeOriginalContext(range, text) {
    const prefixRange = document.createRange();
    prefixRange.setStart(document.body, 0);
    prefixRange.setEnd(range.startContainer, range.startOffset);
    const prefix = getTextFromRange(prefixRange).replace(/\s+/g, ' ').slice(-HIGHLIGHT_CONTEXT_LENGTH);

    const suffixRange = document.createRange();
    suffixRange.setStart(range.endContainer, range.endOffset);
    suffixRange.setEnd(document.body, document.body.childNodes.length);
    const suffix = getTextFromRange(suffixRange).replace(/\s+/g, ' ').slice(0, HIGHLIGHT_CONTEXT_LENGTH);

    return { prefix, text, suffix, isCrossParagraph: false };
}

/**
 * Enhanced deserialization for cross-paragraph highlights
 * @param {Object} data - Serialized cross-paragraph highlight data
 * @returns {Range|null} - Reconstructed range or null on failure
 */
function deserializeCrossParagraphRange(data) {
    const localHighlightId = data?.id || '(no id)';

    try {
        // Use enhanced search strategy for cross-paragraph highlights
        // First, try to find the text using segment-based approach
        if (data.firstSegmentText && data.lastSegmentText) {
            const range = findCrossParagraphRangeBySegments(data);
            if (range) {
                console.log(`[SUCCESS Cross-Paragraph Segments] Range deserialized for ID ${localHighlightId}!`);
                return range;
            }
        }

        // Fallback to enhanced text search with relaxed context matching
        const range = findCrossParagraphRangeByText(data);
        if (range) {
            console.log(`[SUCCESS Cross-Paragraph Text] Range deserialized for ID ${localHighlightId}!`);
            return range;
        }

        // Final fallback to original deserialization method
        console.log(`Stashy: Cross-paragraph deserialization failed, trying original method for ID ${localHighlightId}`);
        return deserializeOriginalRange(data);

    } catch (error) {
        console.warn(`Stashy: Error in cross-paragraph deserialization for ID ${localHighlightId}:`, error);
        return deserializeOriginalRange(data);
    }
}

/**
 * Find cross-paragraph range using first and last segment text
 * @param {Object} data - Serialized data with segment information
 * @returns {Range|null} - Found range or null
 */
function findCrossParagraphRangeBySegments(data) {
    try {
        const firstSegmentText = data.firstSegmentText.trim();
        const lastSegmentText = data.lastSegmentText.trim();
        const fullText = data.text;

        if (!firstSegmentText || !lastSegmentText) {
            return null;
        }

        // Find first segment
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let startNode = null, startOffset = -1;
        let endNode = null, endOffset = -1;

        // Find start position
        let currentNode;
        while (currentNode = walker.nextNode()) {
            const nodeText = currentNode.textContent;
            const firstSegmentIndex = nodeText.indexOf(firstSegmentText);

            if (firstSegmentIndex !== -1) {
                startNode = currentNode;
                startOffset = firstSegmentIndex;
                break;
            }
        }

        if (!startNode) {
            return null;
        }

        // Reset walker to find end position
        walker.currentNode = document.body;
        let foundStart = false;

        while (currentNode = walker.nextNode()) {
            if (currentNode === startNode) {
                foundStart = true;
                continue;
            }

            if (foundStart) {
                const nodeText = currentNode.textContent;
                const lastSegmentIndex = nodeText.indexOf(lastSegmentText);

                if (lastSegmentIndex !== -1) {
                    endNode = currentNode;
                    endOffset = lastSegmentIndex + lastSegmentText.length;
                    break;
                }
            }
        }

        // If last segment is in the same node as first segment
        if (!endNode && startNode) {
            const nodeText = startNode.textContent;
            const lastSegmentIndex = nodeText.indexOf(lastSegmentText, startOffset);

            if (lastSegmentIndex !== -1) {
                endNode = startNode;
                endOffset = lastSegmentIndex + lastSegmentText.length;
            }
        }

        if (startNode && endNode && startOffset !== -1 && endOffset !== -1) {
            const range = document.createRange();
            range.setStart(startNode, startOffset);
            range.setEnd(endNode, endOffset);

            // Validate the reconstructed text
            const reconstructedText = getTextFromRange(range);
            const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();
            const targetNorm = fullText.replace(/\s+/g, ' ').trim();

            if (reconstructedNorm === targetNorm || isFuzzyTextMatch(reconstructedNorm, targetNorm)) {
                return range;
            }
        }

        return null;

    } catch (error) {
        console.warn("Stashy: Error in segment-based cross-paragraph search:", error);
        return null;
    }
}

/**
 * Find cross-paragraph range using enhanced text search
 * @param {Object} data - Serialized data
 * @returns {Range|null} - Found range or null
 */
function findCrossParagraphRangeByText(data) {
    try {
        const targetText = data.text;
        const targetTextNorm = targetText.replace(/\s+/g, ' ').trim();
        const prefixNorm = data.prefix.replace(/\s+/g, ' ').trim();
        const suffixNorm = data.suffix.replace(/\s+/g, ' ').trim();

        // Use relaxed context matching for cross-paragraph highlights
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let accumulatedText = "";
        let nodesVisited = [];
        let currentNode;

        while (currentNode = walker.nextNode()) {
            const nodeTextOrig = currentNode.nodeValue;
            const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
            if (!nodeTextNorm) continue;

            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeTextNorm;
            nodesVisited.push({ node: currentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

            // Look for target text with very relaxed context matching
            const targetMatchIndex = accumulatedText.indexOf(targetTextNorm);
            if (targetMatchIndex !== -1) {
                // For cross-paragraph, use very lenient context checking
                let contextMatch = true;

                // Check prefix if available (very lenient)
                if (prefixNorm.length > 0) {
                    const actualPrefixStart = Math.max(0, targetMatchIndex - prefixNorm.length - 50);
                    const actualPrefix = accumulatedText.substring(actualPrefixStart, targetMatchIndex);
                    contextMatch = actualPrefix.includes(prefixNorm.slice(-Math.min(15, prefixNorm.length)));
                }

                // Check suffix if available (very lenient)
                if (contextMatch && suffixNorm.length > 0) {
                    const actualSuffixStart = targetMatchIndex + targetTextNorm.length;
                    const actualSuffix = accumulatedText.substring(actualSuffixStart, actualSuffixStart + suffixNorm.length + 50);
                    contextMatch = actualSuffix.includes(suffixNorm.slice(0, Math.min(15, suffixNorm.length)));
                }

                if (contextMatch) {
                    // Try to create range
                    const range = createRangeFromAccumulatedText(targetMatchIndex, targetTextNorm.length, nodesVisited);
                    if (range) {
                        return range;
                    }
                }
            }
        }

        return null;

    } catch (error) {
        console.warn("Stashy: Error in text-based cross-paragraph search:", error);
        return null;
    }
}

/**
 * Create range from accumulated text and node positions
 * @param {number} startIndex - Start index in accumulated text
 * @param {number} length - Length of target text
 * @param {Array} nodesVisited - Array of visited nodes with positions
 * @returns {Range|null} - Created range or null
 */
function createRangeFromAccumulatedText(startIndex, length, nodesVisited) {
    try {
        const endIndex = startIndex + length;
        let startNode = null, startOffset = -1;
        let endNode = null, endOffset = -1;
        let foundStart = false, foundEnd = false;

        for (const visited of nodesVisited) {
            if (!foundStart && startIndex >= visited.start && startIndex < visited.start + visited.normLength) {
                startNode = visited.node;
                startOffset = mapNormalizedOffsetToOriginal(startNode.nodeValue, startIndex - visited.start);
                if (startOffset !== -1) foundStart = true;
            }
            if (!foundEnd && endIndex > visited.start && endIndex <= visited.start + visited.normLength) {
                endNode = visited.node;
                const normEndOffsetInNode = endIndex - visited.start;
                endOffset = mapNormalizedOffsetToOriginal(endNode.nodeValue, normEndOffsetInNode);
                if (endOffset !== -1) foundEnd = true;
            }
            if (foundStart && foundEnd) break;
        }

        if (foundStart && foundEnd && startNode && endNode) {
            const range = document.createRange();
            const maxStartOffset = startNode.nodeValue.length;
            const maxEndOffset = endNode.nodeValue.length;

            const clampedStartOffset = Math.min(startOffset, maxStartOffset);
            const clampedEndOffset = Math.min(endOffset, maxEndOffset);

            if (startNode === endNode && clampedStartOffset > clampedEndOffset) {
                return null;
            }

            range.setStart(startNode, clampedStartOffset);
            range.setEnd(endNode, clampedEndOffset);

            return range;
        }

        return null;

    } catch (error) {
        console.warn("Stashy: Error creating range from accumulated text:", error);
        return null;
    }
}

/**
 * Fallback to original deserialization method
 * @param {Object} data - Serialized data
 * @returns {Range|null} - Range or null
 */
function deserializeOriginalRange(data) {
    // Remove the isCrossParagraph flag and try original method
    const originalData = { ...data };
    delete originalData.isCrossParagraph;
    delete originalData.segmentCount;
    delete originalData.firstSegmentText;
    delete originalData.lastSegmentText;

    // Continue with original deserialization logic...
    const targetTextOriginal = originalData.text;
    const targetTextNorm = targetTextOriginal.replace(/\s+/g, ' ').trim();

    // Use the existing fallback logic from the original function
    if (targetTextNorm.length > 10) {
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let accumulatedText = "";
        let nodesVisited = [];
        let currentNode;

        while (currentNode = walker.nextNode()) {
            const nodeTextOrig = currentNode.nodeValue;
            const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
            if (!nodeTextNorm) continue;

            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeTextNorm;
            nodesVisited.push({ node: currentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

            const textMatchIndex = accumulatedText.indexOf(targetTextNorm);
            if (textMatchIndex !== -1) {
                const range = createRangeFromAccumulatedText(textMatchIndex, targetTextNorm.length, nodesVisited);
                if (range) {
                    const reconstructedText = getTextFromRange(range);
                    const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();

                    if (reconstructedNorm === targetTextNorm || isFuzzyTextMatch(reconstructedNorm, targetTextNorm)) {
                        return range;
                    }
                }
                break;
            }
        }
    }

    return null;
}

/**
 * Maps an offset from a normalized string back to the original string offset.
 * NEW APPROACH: Builds a direct mapping. Includes enhanced logging and STRICTER error return.
 * @param {string} originalString
 * @param {number} normalizedTargetOffset - The target offset in the conceptual "normalized" string.
 * @returns {number} The corresponding offset in the original string, or -1 on failure.
 */
function mapNormalizedOffsetToOriginal(originalString, normalizedTargetOffset) {
    if (normalizedTargetOffset < 0) return -1; // Invalid target
    if (normalizedTargetOffset === 0) return 0; // Target is start

    let originalIndex = 0;
    let normalizedIndex = 0;
    let inWhitespaceSequence = false;
    const normToOrigMap = { 0: 0 }; // Map: normalized index -> original index *after* char/sequence

    while (originalIndex < originalString.length) {
        const originalChar = originalString[originalIndex];
        const isWhitespace = /\s/.test(originalChar);

        if (!isWhitespace) {
            normalizedIndex++;
            normToOrigMap[normalizedIndex] = originalIndex + 1;
            inWhitespaceSequence = false;
        } else {
            if (!inWhitespaceSequence) {
                normalizedIndex++;
                normToOrigMap[normalizedIndex] = originalIndex + 1;
                inWhitespaceSequence = true;
            }
            // If already in whitespace, normToOrigMap[normalizedIndex] remains pointing
            // to the end of the *first* whitespace char in the sequence.
        }

        // Check if we just created the map entry for our target
        if (normalizedIndex === normalizedTargetOffset && normToOrigMap.hasOwnProperty(normalizedTargetOffset)) {
            // console.log(`---> mapNormalizedOffsetToOriginal v6: Target ${normalizedTargetOffset} mapped. Returning Original Idx: ${normToOrigMap[normalizedTargetOffset]}`); // Debug v6
            return normToOrigMap[normalizedTargetOffset];
        }

        originalIndex++;
    }

    // Loop finished. Did we ever reach the target normalized offset?
    const maxMappedNormIndex = Math.max(0, ...Object.keys(normToOrigMap).map(Number));

    if (normalizedTargetOffset > maxMappedNormIndex) {
        // Target offset is genuinely beyond the normalized content of this node.
        // This is NOT an error in mapping itself, but the target isn't here.
        // Let the calling function handle this (it means the range spans multiple nodes).
        // However, if the *calling function* expected the offset to be within *this node*
        // based on the accumulated text length, it indicates a mismatch.
        console.warn(`---> mapNormalizedOffsetToOriginal v6 INFO: Target offset (${normalizedTargetOffset}) is beyond this node's max mapped normalized index (${maxMappedNormIndex}). Returning -1.`); // Debug v6
        return -1; // Indicate target not found within this node's normalized length
    } else {
         // This means the target offset *should* have been reachable within the loop,
         // but we exited without finding it. This implies a potential logic issue
         // or that the target *perfectly* aligns with the very end.
         // Check the map one last time.
         if (normToOrigMap.hasOwnProperty(normalizedTargetOffset)) {
             console.log(`---> mapNormalizedOffsetToOriginal v6 NOTE: Target ${normalizedTargetOffset} found in map after loop finished. Returning mapped value: ${normToOrigMap[normalizedTargetOffset]}`); // Debug v6
             return normToOrigMap[normalizedTargetOffset];
         } else {
             // The target offset was within the theoretical range but wasn't mapped. Error.
             console.error(`---> mapNormalizedOffsetToOriginal v6 ERROR: Target ${normalizedTargetOffset} (<= max ${maxMappedNormIndex}) was NOT found in map. Logic error or unexpected structure. Returning -1.`); // Debug v6
             return -1; // Indicate mapping failure
        }
    }
}

/**
 * Performs fuzzy text matching to handle cases where content has minor changes
 * @param {string} text1 - First text to compare
 * @param {string} text2 - Second text to compare
 * @returns {boolean} - True if texts are similar enough
 */
function isFuzzyTextMatch(text1, text2) {
    if (!text1 || !text2) return false;
    if (text1 === text2) return true;

    // Calculate similarity threshold based on text length
    const minLength = Math.min(text1.length, text2.length);
    const maxLength = Math.max(text1.length, text2.length);

    // If length difference is too large, not a match
    if (maxLength - minLength > Math.max(10, minLength * 0.3)) {
        return false;
    }

    // Simple similarity check - count matching characters in order
    let matches = 0;
    let i = 0, j = 0;

    while (i < text1.length && j < text2.length) {
        if (text1[i] === text2[j]) {
            matches++;
            i++;
            j++;
        } else {
            // Try to skip one character in either string
            if (i + 1 < text1.length && text1[i + 1] === text2[j]) {
                i += 2;
                j++;
            } else if (j + 1 < text2.length && text1[i] === text2[j + 1]) {
                i++;
                j += 2;
            } else {
                i++;
                j++;
            }
        }
    }

    // Calculate similarity ratio
    const similarity = matches / maxLength;
    const threshold = 0.8; // 80% similarity required

    return similarity >= threshold;
}

/**
 * Finds an element by its fingerprint
 * @param {Object} fingerprint - Element fingerprint to match
 * @returns {Element|null} Matching element or null
 */
function findElementByFingerprint(fingerprint) {
    if (!fingerprint || !fingerprint.tagName) {
        return null;
    }

    // Get all elements of the same tag type
    const candidates = document.getElementsByTagName(fingerprint.tagName);
    let bestMatch = null;
    let bestScore = 0;

    for (const element of candidates) {
        const score = calculateFingerprintScore(element, fingerprint);
        if (score > bestScore && score > 0.7) { // Require at least 70% match
            bestScore = score;
            bestMatch = element;
        }
    }

    return bestMatch;
}

/**
 * Calculates similarity score between an element and a fingerprint
 * @param {Element} element - Element to score
 * @param {Object} fingerprint - Target fingerprint
 * @returns {number} Similarity score between 0 and 1
 */
function calculateFingerprintScore(element, fingerprint) {
    let score = 0;
    let totalWeight = 0;

    // Tag name match (required)
    if (element.tagName.toLowerCase() !== fingerprint.tagName) {
        return 0;
    }

    // Attribute matching
    const attributeWeight = 0.4;
    let attributeScore = 0;
    let attributeCount = 0;

    for (const [attrName, attrValue] of Object.entries(fingerprint.attributes)) {
        attributeCount++;
        if (element.hasAttribute(attrName) && element.getAttribute(attrName) === attrValue) {
            attributeScore += 1;
        }
    }

    if (attributeCount > 0) {
        score += (attributeScore / attributeCount) * attributeWeight;
        totalWeight += attributeWeight;
    }

    // Text content similarity
    const textWeight = 0.3;
    if (fingerprint.textContent) {
        const elementText = element.textContent ? element.textContent.substring(0, 100) : '';
        if (isFuzzyTextMatch(elementText, fingerprint.textContent)) {
            score += textWeight;
        }
        totalWeight += textWeight;
    }

    // Position similarity
    const positionWeight = 0.3;
    if (fingerprint.siblingIndex !== undefined && element.parentElement) {
        const siblings = Array.from(element.parentElement.children);
        const currentIndex = siblings.indexOf(element);
        const indexDiff = Math.abs(currentIndex - fingerprint.siblingIndex);
        const maxDiff = Math.max(siblings.length, fingerprint.siblingCount || 1);
        const positionScore = Math.max(0, 1 - (indexDiff / maxDiff));
        score += positionScore * positionWeight;
        totalWeight += positionWeight;
    }

    return totalWeight > 0 ? score / totalWeight : 0;
}

/**
 * Finds text range within specified elements
 * @param {Element} startElement - Start element container
 * @param {Element} endElement - End element container
 * @param {string} targetText - Text to find
 * @param {Object} anchoring - Anchoring data for additional context
 * @returns {Range|null} Found range or null
 */
function findTextRangeInElements(startElement, endElement, targetText, anchoring) {
    try {
        const targetNorm = targetText.replace(/\s+/g, ' ').trim();

        // First try: Look within the specific elements
        const directRange = findTextInSpecificElements(startElement, endElement, targetNorm);
        if (directRange) {
            return directRange;
        }

        // Second try: Expand search to common ancestor
        const commonAncestor = findCommonAncestor(startElement, endElement);
        if (!commonAncestor) {
            return null;
        }

        const walker = document.createTreeWalker(
            commonAncestor,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    const parent = node.parentElement;
                    if (parent && (
                        parent.closest('script, style') ||
                        parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                        parent.closest('[style*="display: none"], [hidden]') ||
                        parent.offsetParent === null
                    )) return NodeFilter.FILTER_REJECT;
                    if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;

                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let accumulatedText = "";
        let nodesVisited = [];
        let node;

        while (node = walker.nextNode()) {
            const nodeText = node.textContent.replace(/\s+/g, ' ');
            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeText;
            nodesVisited.push({ node, start: nodeStartPos, length: nodeText.length });
        }

        // Look for the target text
        const textIndex = accumulatedText.indexOf(targetNorm);
        if (textIndex === -1) {
            // Try fuzzy matching
            return tryFuzzyTextSearch(nodesVisited, accumulatedText, targetNorm);
        }

        // Create range from found text
        return createRangeFromAccumulatedText(textIndex, targetNorm.length, nodesVisited);

    } catch (error) {
        console.warn("Stashy: Error finding text range in elements:", error);
        return null;
    }
}

/**
 * Finds text within specific elements more directly
 * @param {Element} startElement - Start element
 * @param {Element} endElement - End element
 * @param {string} targetText - Text to find
 * @returns {Range|null} Found range or null
 */
function findTextInSpecificElements(startElement, endElement, targetText) {
    try {
        // Check if the text exists in the start element
        const startElementText = startElement.textContent;
        if (startElementText && startElementText.includes(targetText)) {
            return createRangeFromElementText(startElement, targetText);
        }

        // If different elements, check end element too
        if (endElement !== startElement) {
            const endElementText = endElement.textContent;
            if (endElementText && endElementText.includes(targetText)) {
                return createRangeFromElementText(endElement, targetText);
            }
        }

        return null;
    } catch (error) {
        console.warn("Stashy: Error in direct element text search:", error);
        return null;
    }
}

/**
 * Creates a range from text within a specific element
 * @param {Element} element - The element containing the text
 * @param {string} targetText - The text to find
 * @returns {Range|null} Created range or null
 */
function createRangeFromElementText(element, targetText) {
    try {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let accumulatedText = "";
        let nodesVisited = [];
        let node;

        while (node = walker.nextNode()) {
            const nodeText = node.textContent;
            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeText;
            nodesVisited.push({ node, start: nodeStartPos, length: nodeText.length });
        }

        const normalizedAccumulated = accumulatedText.replace(/\s+/g, ' ');
        const normalizedTarget = targetText.replace(/\s+/g, ' ');
        const textIndex = normalizedAccumulated.indexOf(normalizedTarget);

        if (textIndex !== -1) {
            return createRangeFromAccumulatedText(textIndex, normalizedTarget.length, nodesVisited);
        }

        return null;
    } catch (error) {
        console.warn("Stashy: Error creating range from element text:", error);
        return null;
    }
}

/**
 * Finds the common ancestor of two elements
 * @param {Element} element1 - First element
 * @param {Element} element2 - Second element
 * @returns {Element|null} Common ancestor or null
 */
function findCommonAncestor(element1, element2) {
    if (!element1 || !element2) {
        return null;
    }

    if (element1 === element2) {
        return element1;
    }

    // Get all ancestors of element1
    const ancestors1 = [];
    let current = element1;
    while (current) {
        ancestors1.push(current);
        current = current.parentElement;
    }

    // Find first common ancestor
    current = element2;
    while (current) {
        if (ancestors1.includes(current)) {
            return current;
        }
        current = current.parentElement;
    }

    return document.body; // Fallback to body
}

/**
 * Tries fuzzy text search when exact match fails
 * @param {Array} nodesVisited - Array of visited nodes with position info
 * @param {string} accumulatedText - Full accumulated text
 * @param {string} targetText - Target text to find
 * @returns {Range|null} Found range or null
 */
function tryFuzzyTextSearch(nodesVisited, accumulatedText, targetText) {
    // Try to find the best partial match
    const words = targetText.split(' ').filter(word => word.length > 2);
    if (words.length === 0) {
        return null;
    }

    let bestMatch = null;
    let bestScore = 0;

    // Try different combinations of words
    for (let i = 0; i < words.length; i++) {
        for (let j = i + 1; j <= words.length; j++) {
            const searchText = words.slice(i, j).join(' ');
            const index = accumulatedText.indexOf(searchText);

            if (index !== -1) {
                const score = searchText.length / targetText.length;
                if (score > bestScore) {
                    bestScore = score;
                    bestMatch = { index, length: searchText.length };
                }
            }
        }
    }

    if (bestMatch && bestScore > 0.5) { // Require at least 50% match
        return createRangeFromAccumulatedText(bestMatch.index, bestMatch.length, nodesVisited);
    }

    return null;
}

/**
 * Creates a range from accumulated text position
 * @param {number} startIndex - Start index in accumulated text
 * @param {number} length - Length of text
 * @param {Array} nodesVisited - Array of visited nodes with position info
 * @returns {Range|null} Created range or null
 */
function createRangeFromAccumulatedText(startIndex, length, nodesVisited) {
    try {
        const endIndex = startIndex + length;
        let startNode = null, startOffset = 0;
        let endNode = null, endOffset = 0;
        let foundStart = false, foundEnd = false;

        for (const visited of nodesVisited) {
            if (!foundStart && startIndex >= visited.start && startIndex < visited.start + visited.length) {
                startNode = visited.node;
                startOffset = mapNormalizedOffsetToOriginal(startNode.nodeValue, startIndex - visited.start);
                if (startOffset !== -1) foundStart = true;
            }

            if (!foundEnd && endIndex > visited.start && endIndex <= visited.start + visited.length) {
                endNode = visited.node;
                const normEndOffsetInNode = endIndex - visited.start;
                endOffset = mapNormalizedOffsetToOriginal(endNode.nodeValue, normEndOffsetInNode);
                if (endOffset !== -1) foundEnd = true;
            }

            if (foundStart && foundEnd) break;
        }

        if (foundStart && foundEnd && startNode && endNode) {
            const range = document.createRange();
            const maxStartOffset = startNode.nodeValue.length;
            const maxEndOffset = endNode.nodeValue.length;

            const clampedStartOffset = Math.min(startOffset, maxStartOffset);
            const clampedEndOffset = Math.min(endOffset, maxEndOffset);

            if (startNode === endNode && clampedStartOffset > clampedEndOffset) {
                return null; // Invalid range
            }

            range.setStart(startNode, clampedStartOffset);
            range.setEnd(endNode, clampedEndOffset);
            return range;
        }

        return null;
    } catch (error) {
        console.warn("Stashy: Error creating range from accumulated text:", error);
        return null;
    }
}

/**
 * Enhanced deserialization that tries multiple strategies for robust highlight restoration
 * @param {object} data - The serialized range data object
 * @returns {Range|null} The reconstructed Range object or null on failure
 */
function deserializeRangeByContext(data) {
    // Try enhanced deserialization first if available
    if (data.version === ENHANCED_HIGHLIGHT_VERSION && data.enhancedAnchoring) {
        const enhancedRange = deserializeRangeByEnhancedAnchoring(data);
        if (enhancedRange) {
            console.log(`[SUCCESS Enhanced] Range deserialized using enhanced anchoring for ID ${data.id || '(no id)'}`);
            return enhancedRange;
        }
    }

    // Fall back to original deserialization
    return deserializeRangeByContextOriginal(data);
}

/**
 * Enhanced deserialization using element anchoring and multiple fallback strategies
 * @param {object} data - The serialized range data with enhanced anchoring
 * @returns {Range|null} The reconstructed Range object or null on failure
 */
function deserializeRangeByEnhancedAnchoring(data) {
    const localHighlightId = data?.id || '(no id)';
    const anchoring = data.enhancedAnchoring;

    if (!anchoring) {
        return null;
    }

    // Strategy 1: Try CSS selector-based restoration
    const cssSelectorRange = tryCSSSelectorRestoration(data, anchoring);
    if (cssSelectorRange) {
        console.log(`[SUCCESS CSS Selector] Range restored for ID ${localHighlightId}`);
        return cssSelectorRange;
    }

    // Strategy 2: Try XPath-based restoration
    const xpathRange = tryXPathRestoration(data, anchoring);
    if (xpathRange) {
        console.log(`[SUCCESS XPath] Range restored for ID ${localHighlightId}`);
        return xpathRange;
    }

    // Strategy 3: Try element fingerprint matching
    const fingerprintRange = tryFingerprintRestoration(data, anchoring);
    if (fingerprintRange) {
        console.log(`[SUCCESS Fingerprint] Range restored for ID ${localHighlightId}`);
        return fingerprintRange;
    }

    // Strategy 4: Try position-based restoration
    const positionRange = tryPositionBasedRestoration(data, anchoring);
    if (positionRange) {
        console.log(`[SUCCESS Position] Range restored for ID ${localHighlightId}`);
        return positionRange;
    }

    // Strategy 5: Try partial text matching for large highlights
    const partialRange = tryPartialTextMatching(data, anchoring);
    if (partialRange) {
        console.log(`[SUCCESS Partial Text] Range restored for ID ${localHighlightId}`);
        return partialRange;
    }

    // Strategy 6: Try semantic text matching
    const semanticRange = trySemanticTextMatching(data, anchoring);
    if (semanticRange) {
        console.log(`[SUCCESS Semantic] Range restored for ID ${localHighlightId}`);
        return semanticRange;
    }

    console.log(`[FAIL Enhanced] All enhanced strategies failed for ID ${localHighlightId}`);
    return null;
}

/**
 * Tries partial text matching for large highlights that may have changed (CONSERVATIVE VERSION)
 * @param {object} data - Serialized highlight data
 * @param {object} anchoring - Enhanced anchoring data
 * @returns {Range|null} Restored range or null
 */
function tryPartialTextMatching(data, anchoring) {
    try {
        const targetText = data.text;
        if (!targetText || targetText.length < 100) { // Increased minimum from 50 to 100
            return null; // Only for substantial text
        }

        // Only use for very large highlights where other methods failed
        if (targetText.length < 300) {
            return null; // Let other methods handle smaller highlights
        }

        // Split text into meaningful chunks
        const sentences = targetText.split(/[.!?]+/).filter(s => s.trim().length > 15); // Increased from 10 to 15
        if (sentences.length < 2) { // Need at least 2 sentences
            return null;
        }

        // Use more specific sentence matching
        const firstSentence = sentences[0].trim();
        const lastSentence = sentences[sentences.length - 1].trim();

        // Skip if sentences are too short or too generic
        if (firstSentence.length < 20 || lastSentence.length < 20) {
            return null;
        }

        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    const parent = node.parentElement;
                    if (parent && (
                        parent.closest('script, style') ||
                        parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                        parent.closest('[style*="display: none"], [hidden]') ||
                        parent.offsetParent === null
                    )) return NodeFilter.FILTER_REJECT;
                    if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let accumulatedText = "";
        let nodesVisited = [];
        let node;

        while (node = walker.nextNode()) {
            const nodeText = node.textContent.replace(/\s+/g, ' ');
            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeText;
            nodesVisited.push({ node, start: nodeStartPos, length: nodeText.length });
        }

        // Find start and end positions with more precise matching
        const startIndex = accumulatedText.indexOf(firstSentence);
        const endIndex = accumulatedText.lastIndexOf(lastSentence);

        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
            const endPos = endIndex + lastSentence.length;
            const matchLength = endPos - startIndex;

            // Sanity checks - be more conservative
            if (matchLength < targetText.length * 0.7) { // Must be at least 70% of original
                console.log(`Stashy: Partial match too short (${matchLength} vs ${targetText.length}), skipping`);
                return null;
            }

            if (matchLength > targetText.length * 2) { // Must not be more than 2x original
                console.log(`Stashy: Partial match too long (${matchLength} vs ${targetText.length}), skipping`);
                return null;
            }

            console.log(`Stashy: Partial text match found - Length: ${matchLength} (original: ${targetText.length})`);
            return createRangeFromAccumulatedText(startIndex, matchLength, nodesVisited);
        }

        return null;
    } catch (error) {
        console.warn("Stashy: Partial text matching failed:", error);
        return null;
    }
}

/**
 * Tries semantic text matching using keywords and phrases (CONSERVATIVE VERSION)
 * @param {object} data - Serialized highlight data
 * @param {object} anchoring - Enhanced anchoring data
 * @returns {Range|null} Restored range or null
 */
function trySemanticTextMatching(data, anchoring) {
    try {
        const targetText = data.text;
        if (!targetText || targetText.length < 20) {
            return null;
        }

        // Only use semantic matching for reasonably sized highlights (not huge ones)
        if (targetText.length > 500) {
            console.log("Stashy: Skipping semantic matching for large text (>500 chars)");
            return null;
        }

        // Extract meaningful keywords (words longer than 4 characters, more selective)
        const words = targetText.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 4) // Increased from 3 to 4 for more selectivity
            .slice(0, 5); // Reduced from 10 to 5 for more precision

        if (words.length < 2) { // Reduced from 3 to 2
            return null;
        }

        // Get first and last few words for boundary detection
        const firstWords = words.slice(0, 2);
        const lastWords = words.slice(-2);

        // Find text that contains most of these keywords
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    const parent = node.parentElement;
                    if (parent && (
                        parent.closest('script, style') ||
                        parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                        parent.closest('[style*="display: none"], [hidden]') ||
                        parent.offsetParent === null
                    )) return NodeFilter.FILTER_REJECT;
                    if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let accumulatedText = "";
        let nodesVisited = [];
        let node;

        while (node = walker.nextNode()) {
            const nodeText = node.textContent.replace(/\s+/g, ' ');
            const nodeStartPos = accumulatedText.length;
            accumulatedText += nodeText;
            nodesVisited.push({ node, start: nodeStartPos, length: nodeText.length });
        }

        const textLower = accumulatedText.toLowerCase();

        // Find start position using first words
        let startPos = -1;
        for (const word of firstWords) {
            const pos = textLower.indexOf(word);
            if (pos !== -1) {
                startPos = pos;
                break;
            }
        }

        if (startPos === -1) {
            return null;
        }

        // Find end position using last words, searching from start position
        let endPos = -1;
        const searchStart = Math.max(startPos, startPos + targetText.length - 200); // Look near expected end
        const searchEnd = Math.min(textLower.length, startPos + targetText.length * 2); // Don't search too far

        for (const word of lastWords) {
            const pos = textLower.indexOf(word, searchStart);
            if (pos !== -1 && pos < searchEnd) {
                endPos = pos + word.length;
                break;
            }
        }

        if (endPos === -1 || endPos <= startPos) {
            // Fallback: use target length from start position
            endPos = Math.min(startPos + targetText.length * 1.2, textLower.length);
        }

        const matchLength = endPos - startPos;

        // Sanity checks
        if (matchLength < targetText.length * 0.5) { // Too short
            return null;
        }

        if (matchLength > targetText.length * 3) { // Too long
            console.log(`Stashy: Semantic match too long (${matchLength} vs ${targetText.length}), skipping`);
            return null;
        }

        // Verify keyword density in the matched segment
        const matchedSegment = textLower.substring(startPos, endPos);
        let keywordCount = 0;
        for (const word of words) {
            if (matchedSegment.includes(word)) {
                keywordCount++;
            }
        }

        const keywordDensity = keywordCount / words.length;
        if (keywordDensity < 0.8) { // Require at least 80% keyword match
            console.log(`Stashy: Semantic match keyword density too low (${keywordDensity}), skipping`);
            return null;
        }

        console.log(`Stashy: Semantic match found - Length: ${matchLength}, Density: ${keywordDensity}`);
        return createRangeFromAccumulatedText(startPos, matchLength, nodesVisited);

    } catch (error) {
        console.warn("Stashy: Semantic text matching failed:", error);
        return null;
    }
}

/**
 * Attempts to restore range using CSS selectors
 * @param {object} data - Serialized highlight data
 * @param {object} anchoring - Enhanced anchoring data
 * @returns {Range|null} Restored range or null
 */
function tryCSSSelectorRestoration(data, anchoring) {
    try {
        const startSelector = anchoring.startElement?.cssSelector;
        const endSelector = anchoring.endElement?.cssSelector || startSelector;

        if (!startSelector) {
            return null;
        }

        const startElement = document.querySelector(startSelector);
        const endElement = endSelector !== startSelector ? document.querySelector(endSelector) : startElement;

        if (!startElement || !endElement) {
            // Try fallback selectors if primary ones fail
            return tryFallbackSelectors(data, anchoring);
        }

        // Try to find the text within these elements
        return findTextRangeInElements(startElement, endElement, data.text, anchoring);

    } catch (error) {
        console.warn("Stashy: CSS selector restoration failed:", error);
        return null;
    }
}

/**
 * Tries fallback CSS selectors when primary selectors fail
 * @param {object} data - Serialized highlight data
 * @param {object} anchoring - Enhanced anchoring data
 * @returns {Range|null} Restored range or null
 */
function tryFallbackSelectors(data, anchoring) {
    try {
        const fingerprint = anchoring.startElement?.fingerprint;
        if (!fingerprint) {
            return null;
        }

        // Try to find elements by tag name and attributes
        const tagName = fingerprint.tagName;
        const candidates = document.getElementsByTagName(tagName);

        for (const element of candidates) {
            // Check if element matches some of the fingerprint attributes
            let matchScore = 0;
            let totalAttributes = 0;

            for (const [attrName, attrValue] of Object.entries(fingerprint.attributes)) {
                totalAttributes++;
                if (element.hasAttribute(attrName) && element.getAttribute(attrName) === attrValue) {
                    matchScore++;
                }
            }

            // If we have a reasonable match (at least 50% of attributes)
            if (totalAttributes > 0 && matchScore / totalAttributes >= 0.5) {
                const range = findTextRangeInElements(element, element, data.text, anchoring);
                if (range) {
                    return range;
                }
            }
        }

        return null;
    } catch (error) {
        console.warn("Stashy: Fallback selector restoration failed:", error);
        return null;
    }
}

/**
 * Attempts to restore range using XPath
 * @param {object} data - Serialized highlight data
 * @param {object} anchoring - Enhanced anchoring data
 * @returns {Range|null} Restored range or null
 */
function tryXPathRestoration(data, anchoring) {
    try {
        const startXPath = anchoring.startElement?.xpath;
        const endXPath = anchoring.endElement?.xpath || startXPath;

        if (!startXPath) {
            return null;
        }

        const startResult = document.evaluate(startXPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        const endResult = endXPath !== startXPath ?
            document.evaluate(endXPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null) :
            startResult;

        const startElement = startResult.singleNodeValue;
        const endElement = endResult.singleNodeValue;

        if (!startElement || !endElement) {
            return null;
        }

        // Try to find the text within these elements
        return findTextRangeInElements(startElement, endElement, data.text, anchoring);

    } catch (error) {
        console.warn("Stashy: XPath restoration failed:", error);
        return null;
    }
}

/**
 * Attempts to restore range using element fingerprints
 * @param {object} data - Serialized highlight data
 * @param {object} anchoring - Enhanced anchoring data
 * @returns {Range|null} Restored range or null
 */
function tryFingerprintRestoration(data, anchoring) {
    try {
        const startFingerprint = anchoring.startElement?.fingerprint;
        const endFingerprint = anchoring.endElement?.fingerprint || startFingerprint;

        if (!startFingerprint) {
            return null;
        }

        const startElement = findElementByFingerprint(startFingerprint);
        const endElement = endFingerprint !== startFingerprint ?
            findElementByFingerprint(endFingerprint) : startElement;

        if (!startElement || !endElement) {
            return null;
        }

        // Try to find the text within these elements
        return findTextRangeInElements(startElement, endElement, data.text, anchoring);

    } catch (error) {
        console.warn("Stashy: Fingerprint restoration failed:", error);
        return null;
    }
}

/**
 * Attempts to restore range using relative position
 * @param {object} data - Serialized highlight data
 * @param {object} anchoring - Enhanced anchoring data
 * @returns {Range|null} Restored range or null
 */
function tryPositionBasedRestoration(data, anchoring) {
    try {
        const position = anchoring.relativePosition;
        if (!position || position.documentLength === 0) {
            return null;
        }

        const currentDocumentText = document.body.textContent || '';
        const startCharIndex = Math.floor(position.startPercentage * currentDocumentText.length);
        const endCharIndex = Math.floor(position.endPercentage * currentDocumentText.length);

        // Find text nodes at these positions
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    const parent = node.parentElement;
                    if (parent && (
                        parent.closest('script, style') ||
                        parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                        parent.closest('[style*="display: none"], [hidden]') ||
                        parent.offsetParent === null
                    )) return NodeFilter.FILTER_REJECT;
                    if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let currentCharCount = 0;
        let startNode = null, startOffset = 0;
        let endNode = null, endOffset = 0;
        let node;

        while (node = walker.nextNode()) {
            const nodeText = node.textContent;
            const nodeLength = nodeText.length;

            if (!startNode && currentCharCount + nodeLength > startCharIndex) {
                startNode = node;
                startOffset = startCharIndex - currentCharCount;
            }

            if (!endNode && currentCharCount + nodeLength >= endCharIndex) {
                endNode = node;
                endOffset = endCharIndex - currentCharCount;
                break;
            }

            currentCharCount += nodeLength;
        }

        if (startNode && endNode) {
            const range = document.createRange();
            range.setStart(startNode, Math.min(startOffset, startNode.textContent.length));
            range.setEnd(endNode, Math.min(endOffset, endNode.textContent.length));

            // Validate the reconstructed text
            const reconstructedText = getTextFromRange(range);
            const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();
            const targetNorm = data.text.replace(/\s+/g, ' ').trim();

            if (reconstructedNorm === targetNorm || isFuzzyTextMatch(reconstructedNorm, targetNorm)) {
                return range;
            }
        }

        return null;

    } catch (error) {
        console.warn("Stashy: Position-based restoration failed:", error);
        return null;
    }
}

/**
 * Attempts to reconstruct a Range object from serialized context data (original implementation).
 * Uses TreeWalker and the revised offset mapping. Logs specific failure reasons.
 * Includes modification to use normalized text comparison for final validation.
 * Includes enhanced failure logging AND FUZZIER CONTEXT MATCHING + ELLIPSIS HANDLING.
 * V2: Adds more validation steps before range creation.
 * @param {object} data - The serialized range data object { prefix, text, suffix, id?, color? }.
 * @returns {Range|null} The reconstructed Range object or null on failure reason logged.
 */
function deserializeRangeByContextOriginal(data) {
    let failureReason = "Unknown";
    let localHighlightId = data?.id || '(no id)';

    // --- Initial Data Validation ---
    if (!data || typeof data.prefix !== 'string' || typeof data.text !== 'string' || typeof data.suffix !== 'string') {
        failureReason = "Invalid data structure"; console.warn(`Stashy Deserialize: ${failureReason} provided for highlight ID ${localHighlightId}.`, data); return null;
    }
    if (!data.text.trim()) {
        failureReason = "Empty text in data"; console.warn(`Stashy Deserialize: ${failureReason}. Skipping highlight ID ${localHighlightId}.`, data); return null;
    }

    // Check if this is a cross-paragraph highlight
    if (data.isCrossParagraph) {
        console.log(`Stashy: Attempting to deserialize cross-paragraph highlight ID ${localHighlightId} with ${data.segmentCount || 'unknown'} segments`);
        return deserializeCrossParagraphRange(data);
    }
    const targetTextOriginal = data.text;
    const prefixNorm = data.prefix.replace(/\s+/g, ' ').trim();
    const suffixNorm = data.suffix.replace(/\s+/g, ' ').trim();
    const targetTextNorm = targetTextOriginal.replace(/\s+/g, ' ').trim();
    if (!targetTextNorm) {
        failureReason = "Normalized target text is empty"; console.warn(`Stashy Deserialize: ${failureReason}. Skipping highlight ID ${localHighlightId}. Original text: "${data.text}"`); return null;
    }

    // --- Enhanced Ellipsis and Fuzzy Matching Handling ---
    const ellipsisChar = '…'; // Or use the actual character if different (e.g., three dots ...)
    const ellipsisInTarget = targetTextNorm.includes(ellipsisChar);
    let searchTextNorm = targetTextNorm;
    let targetLengthNorm = targetTextNorm.length; // Use this for calculating end offset
    let useFuzzyMatching = false;

    if (ellipsisInTarget) {
        // Use only the part *before* the ellipsis for the initial search
        searchTextNorm = targetTextNorm.split(ellipsisChar)[0].trimEnd(); // Search for text before ellipsis
        if (!searchTextNorm) {
             failureReason = "Text before ellipsis is empty";
             console.warn(`Stashy Deserialize: ${failureReason}. Skipping highlight ID ${localHighlightId}. Original text: "${data.text}"`);
             return null;
        }
        useFuzzyMatching = true;
        // console.log(`   Ellipsis detected in target ID ${localHighlightId}. Searching for start: "${searchTextNorm}"`);
    }

    // For dynamic content, also try fuzzy matching if exact match fails
    const shouldTryFuzzyMatching = useFuzzyMatching || searchTextNorm.length > 20; // Try fuzzy for longer texts

    // --- End Enhanced Ellipsis and Fuzzy Matching Handling ---


    // --- TreeWalker Setup ---
    const walker = document.createTreeWalker( document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                // Exclude script, style, Stashy UI, hidden elements
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null // Exclude non-rendered elements
                )) return NodeFilter.FILTER_REJECT;
                // Skip nodes containing only whitespace
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
    });

    let accumulatedText = "";
    let nodesVisited = [];
    let currentMatchAttempt = 0;
    let currentNode;
    let mappingFailureReason = null; // Hoisted for use across attempts

    while (currentNode = walker.nextNode()) {
        const nodeTextOrig = currentNode.nodeValue;
        const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
        if (!nodeTextNorm) continue;

        const nodeStartPos = accumulatedText.length;
        accumulatedText += nodeTextNorm;
        nodesVisited.push({ node: currentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

        // --- Search and Context Check Loop ---
        let searchStartIndex = 0;
        while (searchStartIndex < accumulatedText.length) {
            // 1. Find the core search text
            const targetMatchIndex = accumulatedText.indexOf(searchTextNorm, searchStartIndex);
            if (targetMatchIndex === -1) break; // Target not found in remaining accumulated text

            currentMatchAttempt++;
            // console.log(`   Attempt #${currentMatchAttempt}: Found potential search text "${searchTextNorm}" at index ${targetMatchIndex}.`);

            // 2. Check Prefix (more lenient for dynamic content)
            const actualPrefixStart = Math.max(0, targetMatchIndex - prefixNorm.length - 20); // Increased tolerance
            const actualPrefixFull = accumulatedText.substring(actualPrefixStart, targetMatchIndex);
            const actualPrefixTrimmed = actualPrefixFull.trimEnd();

            // More lenient prefix matching
            let prefixMatch = (prefixNorm.length === 0) ||
                             (actualPrefixTrimmed.endsWith(prefixNorm)) ||
                             (prefixNorm.length > 10 && actualPrefixTrimmed.includes(prefixNorm.slice(-10))); // Partial match for long prefixes

            if (!prefixMatch) {
                // console.warn(`     Attempt #${currentMatchAttempt}: Prefix mismatch for ID ${localHighlightId}. Expected end: "${prefixNorm}", Got End: "${actualPrefixTrimmed.slice(-prefixNorm.length)}"`);
                searchStartIndex = targetMatchIndex + 1; // Start next search *after* current potential match
                continue; // Try next potential match
            }
            // console.log(`     Attempt #${currentMatchAttempt}: Prefix check passed.`);

            // 3. Check Suffix (more lenient for dynamic content)
            const actualSuffixStartIndex = targetMatchIndex + targetLengthNorm; // Use original target length here
            const actualSuffixLookahead = accumulatedText.substring(actualSuffixStartIndex, actualSuffixStartIndex + suffixNorm.length + 20); // Increased tolerance
            const actualSuffixTrimmed = actualSuffixLookahead.trimStart();

            // More lenient suffix matching
            let suffixMatch = (suffixNorm.length === 0) ||
                             (actualSuffixTrimmed.startsWith(suffixNorm)) ||
                             (suffixNorm.length > 10 && actualSuffixTrimmed.includes(suffixNorm.slice(0, 10))); // Partial match for long suffixes

            if (!suffixMatch) {
                 // console.warn(`     Attempt #${currentMatchAttempt}: Suffix mismatch for ID ${localHighlightId}. Expected start: "${suffixNorm}", Got Start: "${actualSuffixTrimmed.substring(0, suffixNorm.length)}"`);
                searchStartIndex = targetMatchIndex + 1; // Start next search *after* current potential match
                continue; // Try next potential match
            }
             // console.log(`     Attempt #${currentMatchAttempt}: Suffix check passed.`);
            // --- END Context Check ---


            // --- If Context Checks Pass, Proceed with Offset Mapping ---
            const textStartIndexAcc = targetMatchIndex;
            const textEndIndexAcc = textStartIndexAcc + targetLengthNorm; // Use original target length

            let startNode = null, startOffsetOrig = -1;
            let endNode = null, endOffsetOrig = -1;
            let foundStart = false, foundEnd = false;
            mappingFailureReason = null; // Reset reason for this attempt

            // Iterate over nodes involved in the current accumulated text scope
            for (const visited of nodesVisited) {
                // Map Start Offset
                 if (!foundStart && textStartIndexAcc >= visited.start && textStartIndexAcc < visited.start + visited.normLength) {
                    startNode = visited.node;
                    startOffsetOrig = mapNormalizedOffsetToOriginal(startNode.nodeValue, textStartIndexAcc - visited.start); // Use updated mapping function
                    if (startOffsetOrig === -1) { mappingFailureReason = "Start offset mapping failed (returned -1)"; break; }
                    foundStart = true;
                }
                // Map End Offset - Use end index based on original target length
                 if (!foundEnd && textEndIndexAcc > visited.start && textEndIndexAcc <= visited.start + visited.normLength) {
                    endNode = visited.node;
                    const normEndOffsetInNode = textEndIndexAcc - visited.start;
                    endOffsetOrig = mapNormalizedOffsetToOriginal(endNode.nodeValue, normEndOffsetInNode); // Use updated mapping function
                    if (endOffsetOrig === -1) { mappingFailureReason = "End offset mapping failed (returned -1)"; break; }
                    foundEnd = true;
                }
                // Optimization: if end node is found *before* start node in list, mapping is likely impossible for this combo
                if (foundEnd && !foundStart && nodesVisited.indexOf(visited) < nodesVisited.findIndex(n => n.node === startNode)) {
                     mappingFailureReason = "End node found before start node in visited list"; break;
                 }
                 if (foundStart && foundEnd) break; // Exit loop once both are found
            }

            // --- Validation Step 1: Check if mapping succeeded ---
            if (mappingFailureReason) {
                failureReason = mappingFailureReason; // Update overall failure reason if needed
                console.warn(`   Stashy Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping this context match.`);
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
            }
            if (!startNode || !endNode || startOffsetOrig === -1 || endOffsetOrig === -1) {
                failureReason = "Node/Offset mapping failed (nodes/offsets invalid)";
                console.warn(`   Stashy Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping context match.`);
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
            }

            // --- Validation Step 2: Check Node Types and if still in DOM ---
            if (startNode.nodeType !== Node.TEXT_NODE || endNode.nodeType !== Node.TEXT_NODE ||
                !document.body.contains(startNode) || !document.body.contains(endNode)) {
                 failureReason = "Mapped start/end node is invalid or not in DOM";
                 console.warn(`   Stashy Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping context match.`);
                 searchStartIndex = targetMatchIndex + 1; // Try next context match
                 continue;
            }

            // --- Create Range with Robust Error Handling ---
            try {
                const range = document.createRange();
                const maxStartOffset = startNode.nodeValue.length;
                const maxEndOffset = endNode.nodeValue.length;

                // --- Validation Step 3: Check offset bounds ---
                if (startOffsetOrig > maxStartOffset || endOffsetOrig > maxEndOffset) {
                     failureReason = `Offset out of bounds (Start: ${startOffsetOrig}/${maxStartOffset}, End: ${endOffsetOrig}/${maxEndOffset})`;
                     console.warn(`     Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Clamping offsets.`);
                     // Allow clamping for now, but log it clearly. Could choose to fail here too.
                }
                const clampedStartOffset = Math.min(startOffsetOrig, maxStartOffset);
                const clampedEndOffset = Math.min(endOffsetOrig, maxEndOffset);

                // --- Validation Step 4: Check for invalid start/end in same node ---
                if (startNode === endNode && clampedStartOffset > clampedEndOffset) {
                     failureReason = `Invalid offsets in same node (start ${clampedStartOffset} > end ${clampedEndOffset})`;
                     console.error(`     Attempt #${currentMatchAttempt}: FATAL ${failureReason} for ID ${localHighlightId}. Skipping.`);
                     searchStartIndex = targetMatchIndex + 1; // Move to next potential match
                     continue; // IMPORTANT: Skip this specific invalid match attempt
                }

                range.setStart(startNode, clampedStartOffset);
                range.setEnd(endNode, clampedEndOffset);

                // --- Enhanced Final Text Validation with Fuzzy Matching ---
                const reconstructedText = getTextFromRange(range);
                const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();

                // Try exact match first
                if (ellipsisInTarget || reconstructedNorm === targetTextNorm) {
                    const reason = ellipsisInTarget ? "Ellipsis Relaxed" : "Exact Match";
                    console.log(`[SUCCESS ${reason}] Range deserialized for ID ${localHighlightId} on attempt #${currentMatchAttempt}!`);
                    return range; // SUCCESS! Found a valid range
                }

                // Try fuzzy matching for dynamic content scenarios
                if (shouldTryFuzzyMatching && isFuzzyTextMatch(reconstructedNorm, targetTextNorm)) {
                    console.log(`[SUCCESS Fuzzy Match] Range deserialized for ID ${localHighlightId} on attempt #${currentMatchAttempt}!`);
                    return range; // SUCCESS! Found a valid range with fuzzy matching
                }

                // If all validation fails
                failureReason = "Final text validation failed (normalized and fuzzy)";
                console.warn(`   Stashy Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}. Skipping context match.`);
                console.warn(`      > Expected (Norm): "${targetTextNorm}" (Len: ${targetTextNorm.length})`);
                console.warn(`      > Got (Norm):      "${reconstructedNorm}" (Len: ${reconstructedNorm.length})`);
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
                // --- END Validation ---

            } catch (e) {
                failureReason = "Error creating/setting Range object";
                console.error(`   Stashy Deserialize Attempt #${currentMatchAttempt}: ${failureReason} for ID ${localHighlightId}`, { error: e });
                searchStartIndex = targetMatchIndex + 1; // Try next context match
                continue;
            }
        } // End of inner while loop (searching for target text in accumulatedText)
    } // End of outer while loop (iterating through text nodes)

    // --- Fallback: Try simple text matching without context ---
    if (targetTextNorm.length > 10) { // Only for substantial text
        console.log(`   Attempting fallback text-only matching for ID ${localHighlightId}...`);

        // Reset walker for fallback attempt
        const fallbackWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
            acceptNode: (node) => {
                const parent = node.parentElement;
                if (parent && (
                    parent.closest('script, style') ||
                    parent.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) ||
                    parent.closest('[style*="display: none"], [hidden]') ||
                    parent.offsetParent === null
                )) return NodeFilter.FILTER_REJECT;
                if (!node.textContent.trim()) return NodeFilter.FILTER_SKIP;
                return NodeFilter.FILTER_ACCEPT;
            }
        });

        let fallbackAccumulatedText = "";
        let fallbackNodesVisited = [];
        let fallbackCurrentNode;

        while (fallbackCurrentNode = fallbackWalker.nextNode()) {
            const nodeTextOrig = fallbackCurrentNode.nodeValue;
            const nodeTextNorm = nodeTextOrig.replace(/\s+/g, ' ');
            if (!nodeTextNorm) continue;

            const nodeStartPos = fallbackAccumulatedText.length;
            fallbackAccumulatedText += nodeTextNorm;
            fallbackNodesVisited.push({ node: fallbackCurrentNode, start: nodeStartPos, normLength: nodeTextNorm.length });

            // Look for exact text match
            const textMatchIndex = fallbackAccumulatedText.indexOf(targetTextNorm);
            if (textMatchIndex !== -1) {
                console.log(`   Found fallback text match at index ${textMatchIndex}`);

                // Try to create range using the same logic
                const textStartIndexAcc = textMatchIndex;
                const textEndIndexAcc = textStartIndexAcc + targetLengthNorm;

                let startNode = null, startOffsetOrig = -1;
                let endNode = null, endOffsetOrig = -1;
                let foundStart = false, foundEnd = false;

                for (const visited of fallbackNodesVisited) {
                    if (!foundStart && textStartIndexAcc >= visited.start && textStartIndexAcc < visited.start + visited.normLength) {
                        startNode = visited.node;
                        startOffsetOrig = mapNormalizedOffsetToOriginal(startNode.nodeValue, textStartIndexAcc - visited.start);
                        if (startOffsetOrig !== -1) foundStart = true;
                    }
                    if (!foundEnd && textEndIndexAcc > visited.start && textEndIndexAcc <= visited.start + visited.normLength) {
                        endNode = visited.node;
                        const normEndOffsetInNode = textEndIndexAcc - visited.start;
                        endOffsetOrig = mapNormalizedOffsetToOriginal(endNode.nodeValue, normEndOffsetInNode);
                        if (endOffsetOrig !== -1) foundEnd = true;
                    }
                    if (foundStart && foundEnd) break;
                }

                if (foundStart && foundEnd && startNode && endNode) {
                    try {
                        const range = document.createRange();
                        const maxStartOffset = startNode.nodeValue.length;
                        const maxEndOffset = endNode.nodeValue.length;

                        const clampedStartOffset = Math.min(startOffsetOrig, maxStartOffset);
                        const clampedEndOffset = Math.min(endOffsetOrig, maxEndOffset);

                        if (startNode === endNode && clampedStartOffset > clampedEndOffset) {
                            continue; // Invalid range
                        }

                        range.setStart(startNode, clampedStartOffset);
                        range.setEnd(endNode, clampedEndOffset);

                        const reconstructedText = getTextFromRange(range);
                        const reconstructedNorm = reconstructedText.replace(/\s+/g, ' ').trim();

                        if (reconstructedNorm === targetTextNorm || isFuzzyTextMatch(reconstructedNorm, targetTextNorm)) {
                            console.log(`[SUCCESS Fallback] Range deserialized for ID ${localHighlightId} using text-only matching!`);
                            return range;
                        }
                    } catch (e) {
                        console.warn(`   Fallback range creation failed for ID ${localHighlightId}:`, e);
                    }
                }
                break; // Only try first match in fallback
            }
        }
    }

    // --- Final Failure Logging ---
    if (failureReason === "Unknown") {
         failureReason = "Search text with matching context not found"; // More specific final reason
    }

    // Only log detailed errors for initial attempts or when debugging
    if (typeof dynamicHighlightState === 'undefined' || dynamicHighlightState.restorationAttempts <= 1) {
        console.warn(`   [FAIL DESERIALIZE Reason: ${failureReason}] Failed to deserialize highlight ID ${localHighlightId}.`);
        console.warn(`      Text: "${data.text?.substring(0, 50)}${data.text?.length > 50 ? '...' : ''}"`);
    } else {
        // Less verbose logging for automatic restoration attempts
        console.log(`   [FAIL DESERIALIZE] ${failureReason} for highlight ${localHighlightId}`);
    }

    return null; // Return null if no valid range found after all attempts
} // End of deserializeRangeByContext


/**
 * Enhanced cross-paragraph highlighting that properly handles selections spanning multiple elements
 * @param {Range} range - The range to highlight
 * @param {string} highlightId - Unique identifier for the highlight
 * @param {string|null} color - The color name or null
 * @param {string} style - The style name
 * @param {boolean} hasLinkedNote - Whether this highlight has a linked note
 * @returns {Object|null} - Object with mark element or null on failure
 */
function applyCrossParagraphHighlight(range, highlightId, color, style, hasLinkedNote) {
    if (!range || range.collapsed) {
        return null;
    }

    try {
        // Check if the range spans multiple block elements
        const startContainer = range.startContainer;
        const endContainer = range.endContainer;

        // If start and end are in the same text node, use simple approach
        if (startContainer === endContainer && startContainer.nodeType === Node.TEXT_NODE) {
            return applySimpleHighlight(range, highlightId, color, style, hasLinkedNote);
        }

        // For cross-element selections, we need to handle each text node separately
        // but group them under a common highlight ID
        const textNodes = getTextNodesInRange(range);

        if (textNodes.length === 0) {
            console.warn("Stashy: No text nodes found in range for cross-paragraph highlight");
            return null;
        }

        // Create individual highlights for each text node segment
        const highlightElements = [];
        let firstMark = null;

        for (let i = 0; i < textNodes.length; i++) {
            const nodeInfo = textNodes[i];
            const { node, startOffset, endOffset } = nodeInfo;

            // Create a range for this specific text node segment
            const nodeRange = document.createRange();
            nodeRange.setStart(node, startOffset);
            nodeRange.setEnd(node, endOffset);

            // Create the mark element
            const mark = document.createElement("mark");
            let classList = `${HIGHLIGHT_CLASS}`;

            if (style === 'color') {
                classList += ` color-${color}`;
                mark.dataset.style = 'color';
                mark.dataset.color = color;
            } else {
                classList += ` style-${style}`;
                mark.dataset.style = style;
            }

            // Add class for highlights with linked notes
            if (hasLinkedNote) {
                classList += ' has-linked-note';
                mark.dataset.hasLinkedNote = 'true';
            }

            // Add cross-paragraph class and segment info
            classList += ' cross-paragraph-segment';
            mark.dataset.segmentIndex = i.toString();
            mark.dataset.totalSegments = textNodes.length.toString();

            mark.className = classList;
            mark.dataset.highlightId = highlightId;

            // Extract and wrap the content
            const contents = nodeRange.extractContents();
            mark.appendChild(contents);
            nodeRange.insertNode(mark);

            highlightElements.push(mark);
            if (i === 0) {
                firstMark = mark; // Return the first mark as the primary element
            }
        }

        // Store references to all segments in each mark for easier management
        highlightElements.forEach(mark => {
            mark.dataset.segmentElements = JSON.stringify(
                highlightElements.map(el => el.dataset.segmentIndex)
            );
        });

        console.log(`Stashy: Applied cross-paragraph highlight with ${highlightElements.length} segments for ID ${highlightId}`);

        return {
            mark: firstMark,
            segments: highlightElements,
            isCrossParagraph: true
        };

    } catch (error) {
        console.error("Stashy: Error applying cross-paragraph highlight:", error);
        return null;
    }
}

/**
 * Simple highlight application for single text node ranges
 * @param {Range} range - The range to highlight
 * @param {string} highlightId - Unique identifier for the highlight
 * @param {string|null} color - The color name or null
 * @param {string} style - The style name
 * @param {boolean} hasLinkedNote - Whether this highlight has a linked note
 * @returns {Object|null} - Object with mark element or null on failure
 */
function applySimpleHighlight(range, highlightId, color, style, hasLinkedNote) {
    try {
        const mark = document.createElement("mark");
        let classList = `${HIGHLIGHT_CLASS}`;

        if (style === 'color') {
            classList += ` color-${color}`;
            mark.dataset.style = 'color';
            mark.dataset.color = color;
        } else {
            classList += ` style-${style}`;
            mark.dataset.style = style;
        }

        // Add class for highlights with linked notes
        if (hasLinkedNote) {
            classList += ' has-linked-note';
            mark.dataset.hasLinkedNote = 'true';
        }

        mark.className = classList;
        mark.dataset.highlightId = highlightId;

        // Use extractContents and insertNode for simple case
        const contents = range.extractContents();
        mark.appendChild(contents);
        range.insertNode(mark);

        return {
            mark: mark,
            segments: [mark],
            isCrossParagraph: false
        };

    } catch (error) {
        console.error("Stashy: Error applying simple highlight:", error);
        return null;
    }
}

/**
 * Gets all text nodes within a range and calculates their start/end offsets
 * @param {Range} range - The range to analyze
 * @returns {Array} - Array of objects with node, startOffset, endOffset
 */
function getTextNodesInRange(range) {
    const textNodes = [];
    const walker = document.createTreeWalker(
        range.commonAncestorContainer,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode: function(node) {
                // Check if this text node intersects with our range
                if (range.intersectsNode && range.intersectsNode(node)) {
                    return NodeFilter.FILTER_ACCEPT;
                }

                // Fallback for browsers that don't support intersectsNode
                const nodeRange = document.createRange();
                nodeRange.selectNodeContents(node);

                // Check if ranges overlap
                if (range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0 &&
                    range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0) {
                    return NodeFilter.FILTER_ACCEPT;
                }

                return NodeFilter.FILTER_REJECT;
            }
        }
    );

    let node;
    while (node = walker.nextNode()) {
        // Skip empty text nodes
        if (!node.textContent.trim()) {
            continue;
        }

        // Calculate the actual start and end offsets for this node
        let startOffset = 0;
        let endOffset = node.textContent.length;

        // If this is the start container, use the range's start offset
        if (node === range.startContainer) {
            startOffset = range.startOffset;
        }

        // If this is the end container, use the range's end offset
        if (node === range.endContainer) {
            endOffset = range.endOffset;
        }

        // Only include if there's actual content to highlight
        if (startOffset < endOffset) {
            textNodes.push({
                node: node,
                startOffset: startOffset,
                endOffset: endOffset
            });
        }
    }

    return textNodes;
}

/**
 * Applies the visual highlight style (using a <mark> element) to a given Range.
 * ADDS the delete button with a click listener.
 * @param {Range} range
 * @param {string} highlightId
 * @param {string|null} [color] - The color name (e.g., 'yellow') or null/undefined.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 * @param {boolean} [hasLinkedNote=false] - Whether this highlight has a linked note.
 */
function applyHighlight(range, highlightId, color, style, hasLinkedNote = false) {
    if (!range || !highlightId || range.collapsed || !style) {
        return;
    }
    // Basic validation
    const validStyles = ['color', 'underline', 'wavy', 'border-thick', 'strikethrough', 'blur'];
    if (!validStyles.includes(style)) {
        console.warn(`Stashy: Invalid highlight style '${style}' provided. Cannot apply.`);
        return; // Stop if style is invalid
    }
    if (style === 'color' && (!color || typeof HIGHLIGHT_COLORS === 'undefined' || !Object.keys(HIGHLIGHT_COLORS).includes(color))) {
        console.warn(`Stashy: Style is 'color' but invalid/missing color '${color}' provided. Defaulting.`);
        color = DEFAULT_HIGHLIGHT_COLOR;
    }

    // Check if this highlight has a linked note from the highlightsData
    if (!hasLinkedNote && Array.isArray(highlightsData)) {
        const highlight = highlightsData.find(h => h.id === highlightId);
        if (highlight && highlight.hasLinkedNote) {
            hasLinkedNote = true;
        }
    }

    try {
        // Check if the range is already completely within an existing mark for this ID
        let existingMark = null;
        if (range.startContainer.nodeType === Node.ELEMENT_NODE && range.startContainer.matches(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`)) {
            existingMark = range.startContainer;
        } else {
            existingMark = range.startContainer.parentElement?.closest(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`);
        }
        if (existingMark) {
            let endMark = null;
            if (range.endContainer.nodeType === Node.ELEMENT_NODE && range.endContainer.matches(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`)) {
               endMark = range.endContainer;
            } else {
               endMark = range.endContainer.parentElement?.closest(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`);
            }
            if (endMark && existingMark === endMark) {
                // Already marked. Apply new style exclusively.
                let classList = `${HIGHLIGHT_CLASS}`;
                // Remove old style/color classes before adding new one
                existingMark.classList.remove(...Array.from(existingMark.classList).filter(c => c.startsWith('color-') || c.startsWith('style-')));

                if (style === 'color') {
                    classList += ` color-${color}`; // Assumes color is valid/defaulted above
                    existingMark.dataset.style = 'color';
                    existingMark.dataset.color = color;
                } else { // Apply style class only
                    classList += ` style-${style}`;
                    existingMark.dataset.style = style;
                    existingMark.dataset.color = ''; // Clear color data
                }

                // Add class for highlights with linked notes
                if (hasLinkedNote) {
                    classList += ' has-linked-note';
                    existingMark.dataset.hasLinkedNote = 'true';
                } else {
                    // Remove linked note class if it exists
                    existingMark.classList.remove('has-linked-note');
                    delete existingMark.dataset.hasLinkedNote;
                }

                // Apply new classes
                existingMark.classList.add(...classList.split(' ').filter(c => c !== HIGHLIGHT_CLASS));

                // --- Add delete button if it doesn't exist ---
                if (!existingMark.querySelector('.Stashy-delete-highlight-btn')) {
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'Stashy-delete-highlight-btn';
                    deleteBtn.innerHTML = '×'; // Use × symbol
                    deleteBtn.title = 'Remove highlight';
                    deleteBtn.setAttribute('aria-label', 'Remove highlight');
                    deleteBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeHighlight(highlightId, existingMark); // Call removeHighlight directly
                    });
                    existingMark.appendChild(deleteBtn);
                }

                // --- Add note button if it doesn't exist ---
                if (!existingMark.querySelector('.Stashy-note-highlight-btn')) {
                    const noteBtn = document.createElement('button');
                    noteBtn.className = 'Stashy-note-highlight-btn';
                    noteBtn.innerHTML = '📝'; // Use 📝 symbol
                    noteBtn.title = 'Add note to highlight';
                    noteBtn.setAttribute('aria-label', 'Add note to highlight');
                    noteBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        // Get the complete original text from stored highlight data
                        const highlightData = highlightsData.find(h => h.id === highlightId);
                        const completeText = highlightData ? highlightData.text : existingMark.textContent;
                        showNoteEditor(highlightId, completeText);
                    });
                    existingMark.appendChild(noteBtn);
                }

                // --- Check if this highlight has a note and display it ---
                const highlight = highlightsData.find(h => h.id === highlightId);
                if (highlight && highlight.noteText) {
                    // Update or create note display
                    let noteDisplay = existingMark.querySelector('.Stashy-highlight-note-display');
                    if (!noteDisplay) {
                        noteDisplay = document.createElement('div');
                        noteDisplay.className = 'Stashy-highlight-note-display';

                        // Add drag handle
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stashy-note-drag-handle';
                        noteDisplay.appendChild(dragHandle);

                        // Apply stored position if available
                        if (highlight.notePosition) {
                            noteDisplay.style.top = highlight.notePosition.top;
                            noteDisplay.style.left = highlight.notePosition.left;
                            if (highlight.notePosition.marginTop) {
                                noteDisplay.style.marginTop = highlight.notePosition.marginTop;
                            }
                        }

                        // Make the note draggable
                        makeDraggable(noteDisplay, existingMark, highlightId);

                        existingMark.appendChild(noteDisplay);
                    }

                    // Clear existing content
                    while (noteDisplay.firstChild) {
                        if (noteDisplay.firstChild.classList &&
                            noteDisplay.firstChild.classList.contains('Stashy-note-drag-handle')) {
                            // Keep the drag handle
                            const dragHandle = noteDisplay.firstChild;
                            noteDisplay.innerHTML = '';
                            noteDisplay.appendChild(dragHandle);
                            break;
                        } else {
                            noteDisplay.removeChild(noteDisplay.firstChild);
                        }
                    }

                    // Add the text
                    noteDisplay.appendChild(document.createTextNode(highlight.noteText));

                    // Make sure we have a drag handle
                    if (!noteDisplay.querySelector('.Stashy-note-drag-handle')) {
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stashy-note-drag-handle';
                        noteDisplay.insertBefore(dragHandle, noteDisplay.firstChild);
                    }
                }
                return; // Don't re-wrap
            }
        }

        // Not already marked or partially marked, proceed to create new mark
        // Use enhanced cross-paragraph highlighting approach
        const highlightResult = applyCrossParagraphHighlight(range, highlightId, color, style, hasLinkedNote);
        if (!highlightResult) {
            console.warn(`Stashy: Failed to apply cross-paragraph highlight for ID ${highlightId}`);
            return;
        }

        const mark = highlightResult.mark;

        // --- Add the delete button to the newly created mark ---
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'Stashy-delete-highlight-btn';
        deleteBtn.innerHTML = '×'; // Use × symbol
        deleteBtn.title = 'Remove highlight';
        deleteBtn.setAttribute('aria-label', 'Remove highlight');
        deleteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            removeHighlight(highlightId, mark); // Call removeHighlight directly
        });
        mark.appendChild(deleteBtn);

        // --- Add the note button to the newly created mark ---
        const noteBtn = document.createElement('button');
        noteBtn.className = 'Stashy-note-highlight-btn';
        noteBtn.innerHTML = '📝'; // Use 📝 symbol
        noteBtn.title = 'Add note to highlight';
        noteBtn.setAttribute('aria-label', 'Add note to highlight');
        noteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            // Get the complete original text from stored highlight data
            const highlightData = highlightsData.find(h => h.id === highlightId);
            const completeText = highlightData ? highlightData.text : mark.textContent;
            showNoteEditor(highlightId, completeText);
        });
        mark.appendChild(noteBtn);

        // --- Check if this highlight has a note and display it ---
        const highlight = highlightsData.find(h => h.id === highlightId);
        if (highlight && highlight.noteText) {
            const noteDisplay = document.createElement('div');
            noteDisplay.className = 'Stashy-highlight-note-display';

            // Add drag handle
            const dragHandle = document.createElement('div');
            dragHandle.className = 'Stashy-note-drag-handle';
            noteDisplay.appendChild(dragHandle);

            // Set the note text - Use createTextNode to prevent XSS
            noteDisplay.appendChild(document.createTextNode(highlight.noteText));

            // Apply stored position if available
            if (highlight.notePosition) {
                noteDisplay.style.top = highlight.notePosition.top;
                noteDisplay.style.left = highlight.notePosition.left;
                if (highlight.notePosition.marginTop) {
                    noteDisplay.style.marginTop = highlight.notePosition.marginTop;
                }
            }

            // Make the note draggable
            makeDraggable(noteDisplay, mark, highlightId);

            mark.appendChild(noteDisplay);
        }
        // -----------------------------------------------------

    } catch (e) {
        console.error(`ApplyHighlight Error (ID: ${highlightId}, Style: ${style}):`, { error: e });
    }
}


/**
 * Removes a highlight from data storage and the DOM.
 * Enhanced to handle cross-paragraph highlights with multiple segments.
 * @param {string} highlightId - The ID of the highlight to remove.
 * @param {HTMLElement} markElement - The <mark> element in the DOM.
 */
function removeHighlight(highlightId, markElement) {
    if (!highlightId || !markElement) return;

    // 1. Remove from data array
    const index = highlightsData.findIndex(h => h.id === highlightId);
    if (index !== -1) {
        highlightsData.splice(index, 1);
        saveHighlights(); // Save the change
        console.log(`Stashy: Removed highlight ${highlightId} from data.`);
    } else {
        console.warn(`Stashy: Highlight ID ${highlightId} not found in data for removal.`);
        // Proceed with DOM removal anyway, might be an orphaned mark
    }

    // 2. Remove from DOM - handle both single and cross-paragraph highlights
    try {
        // Check if this is a cross-paragraph highlight
        if (markElement.classList.contains('cross-paragraph-segment')) {
            // Find all segments of this cross-paragraph highlight
            const allSegments = document.querySelectorAll(`mark.${HIGHLIGHT_CLASS}[data-highlight-id="${highlightId}"]`);

            console.log(`Stashy: Removing cross-paragraph highlight with ${allSegments.length} segments for ID ${highlightId}`);

            // Remove all segments
            allSegments.forEach(segment => {
                removeSingleHighlightSegment(segment);
            });
        } else {
            // Single highlight segment
            removeSingleHighlightSegment(markElement);
        }

        console.log(`Stashy: Removed highlight ${highlightId} mark(s) from DOM.`);
    } catch (err) {
        console.error(`Stashy: Error removing highlight ${highlightId} mark from DOM:`, err);
        // Data was likely removed, but DOM state might be inconsistent.
    }
}

/**
 * Removes a single highlight segment from the DOM
 * @param {HTMLElement} markElement - The mark element to remove
 */
function removeSingleHighlightSegment(markElement) {
    if (!markElement || !markElement.parentNode) {
        return;
    }

    try {
        // Replace the mark element with its child nodes (the original content)
        // Filter out the delete button, note button, and note display before replacing
        const childNodes = Array.from(markElement.childNodes).filter(node =>
            !node.classList ||
            (!node.classList.contains('Stashy-delete-highlight-btn') &&
             !node.classList.contains('Stashy-note-highlight-btn') &&
             !node.classList.contains('Stashy-highlight-note-display'))
        );

        const parent = markElement.parentNode;
        markElement.replaceWith(...childNodes);

        // Normalize adjacent text nodes
        if (parent) {
            parent.normalize();
        }
    } catch (err) {
        console.error("Stashy: Error removing single highlight segment:", err);
        // Fallback: just remove the element
        markElement.remove();
    }
}


/**
 * Saves the current `highlightsData` array to chrome.storage.local.
 */
function saveHighlights() {
    // Check if highlightKey is defined (should be by the time this runs)
    if (typeof highlightKey === 'undefined') {
        console.error("saveHighlights Error: highlightKey is not defined. Cannot save."); return;
    }
    if (!Array.isArray(highlightsData)) {
        console.error("!!! saveHighlights FATAL: highlightsData is NOT an array!", highlightsData);
        highlightsData = []; // Attempt to recover by resetting
    }
    // Create a deep copy to avoid potential issues with ongoing modifications
    const dataToSave = JSON.parse(JSON.stringify(highlightsData));
    // console.log(`>>> saveHighlights: Saving ${dataToSave.length} highlights to key '${highlightKey}'.`); // Less noisy
    chrome.storage.local.set({ [highlightKey]: dataToSave }, () => {
        if (chrome.runtime.lastError) {
            console.error("saveHighlights: Error saving highlights locally:", highlightKey, chrome.runtime.lastError.message);
        }
        // else console.log("saveHighlights: Highlights saved successfully."); // Less noisy
    });
}

// Global flags to prevent multiple simultaneous highlight loading
let isLoadingHighlights = false;
let lastHighlightLoadTime = 0;
const HIGHLIGHT_LOAD_COOLDOWN = 1000; // 1 second cooldown between loads

/**
 * Loads highlight data from storage and applies the highlights to the page.
 * Calls the modified applyHighlight which adds the delete button.
 * Enhanced with cooldown mechanism to prevent rapid successive loads.
 */
function loadAndApplyHighlights(forceReload = false) {
    console.log("Stashy: loadAndApplyHighlights Starting...");

    const now = Date.now();

    // Prevent multiple simultaneous loads
    if (isLoadingHighlights && !forceReload) {
        console.log("Stashy: Highlights already loading, skipping...");
        return;
    }

    // Prevent rapid successive loads (cooldown period)
    if (!forceReload && (now - lastHighlightLoadTime) < HIGHLIGHT_LOAD_COOLDOWN) {
        console.log(`Stashy: Highlight load cooldown active (${HIGHLIGHT_LOAD_COOLDOWN}ms), skipping...`);
        return;
    }

    isLoadingHighlights = true;
    lastHighlightLoadTime = now;

    // Temporarily disable threat detection during highlight loading to prevent false positives
    const threatDetectionWasActive = window.StashyThreatDetection && window.StashyThreatDetection.isActive();
    if (threatDetectionWasActive) {
        window.StashyThreatDetection.pause();
        console.log("Stashy: Temporarily paused threat detection for highlight loading");
    }

    // Also pause threat detection for cross-paragraph deserialization
    if (!threatDetectionWasActive && window.StashyThreatDetection) {
        window.StashyThreatDetection.pause();
        console.log("Stashy: Paused threat detection for cross-paragraph highlight restoration");
    }

    // 1. Clear existing marks from *this extension* only (including cross-paragraph segments)
    let clearedCount = 0;
    document.querySelectorAll(`mark.${HIGHLIGHT_CLASS}`).forEach(mark => {
        removeSingleHighlightSegment(mark);
        clearedCount++;
    });
    console.log(`Stashy: Cleared ${clearedCount} existing Stashy marks.`);

    if (typeof highlightKey === 'undefined') {
        console.error("Stashy: highlightKey not defined. Cannot load highlights.");
        highlightsData = []; return;
    }

    // 2. Load data from local storage
    chrome.storage.local.get([highlightKey], (result) => {
        if (chrome.runtime.lastError) {
            console.error("Stashy: Error loading highlights:", highlightKey, chrome.runtime.lastError.message);
            highlightsData = []; return;
        }

        let loadedData = result[highlightKey];
        // Validate loaded data structure
        if (typeof loadedData === 'undefined' || loadedData === null) {
            console.log("Stashy: No highlights found in storage for this page.");
            highlightsData = [];
        } else if (!Array.isArray(loadedData)) {
            console.warn("Stashy: Loaded highlight data is NOT an array! Resetting.", loadedData);
            highlightsData = [];
            chrome.storage.local.remove(highlightKey); // Clear invalid data from storage
        } else {
            highlightsData = loadedData; // Assign loaded data to cache
        }

        console.log(`Stashy: Processing ${highlightsData.length} highlights loaded from storage for key ${highlightKey}.`);

        // 3. Deserialize and apply highlights
        let appliedCount = 0;
        let failedCount = 0;
        let needsSave = false; // Flag if data was modified (ID generated, color/style defaulted, restoration status updated)
        const currentTime = Date.now();

        highlightsData.forEach((data, index) => {
            // Basic data validation
            if (!data || typeof data.text !== 'string' || typeof data.prefix !== 'string' || typeof data.suffix !== 'string') {
                console.warn(`   [INVALID DATA] Skipping highlight index ${index}, invalid data structure:`, data);
                failedCount++;
                return; // Skip this item
            }

            // Ensure each highlight has a unique ID, generate if missing
            if (!data.id) {
                 data.id = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
                 console.warn(`   Highlight index ${index} was missing an ID. Generated: ${data.id}`);
                 needsSave = true; // Mark that data needs saving
             }

             // Initialize restoration status fields if missing
             if (!data.restorationStatus) {
                 data.restorationStatus = {
                     lastRestoreAttempt: null,
                     restorationFailures: 0,
                     isTemporarilyUnrestorable: false,
                     lastSuccessfulRestore: null
                 };
                 needsSave = true;
             }

             const highlightStyle = data.style || 'color'; // Default old data to 'color'
             let highlightColor = data.color;

             // --- Data migration/correction ---
             if (highlightStyle === 'color' && !highlightColor) {
                 highlightColor = DEFAULT_HIGHLIGHT_COLOR; // Default missing color if style is color
                 data.color = highlightColor; // Update data object
                 needsSave = true;
             }
             if (highlightStyle !== 'color' && highlightColor) {
                 highlightColor = null; // Ensure color is null if style isn't color
                 data.color = null; // Update data object
                 needsSave = true;
             }
             if (!data.style) {
                 needsSave = true; // Mark for save if style was missing
             }
             data.style = highlightStyle; // Ensure style is set in data object
             // --- End migration ---

            // Update restoration attempt timestamp
            data.restorationStatus.lastRestoreAttempt = currentTime;

            // console.log(`--- Processing Highlight ${index + 1} / ${highlightsData.length} (ID: ${data.id}) ---`); // Less noisy
            const range = deserializeRangeByContext(data);

            if (range) {
                try {
                    // Pass style, color, and hasLinkedNote flag
                    applyHighlight(range, data.id, highlightColor, highlightStyle, data.hasLinkedNote);

                    // Handle snippet prefixes if this is a snippet highlight
                    if (data.snippetType) {
                        const highlightElement = document.querySelector(`mark.Stashy-highlight[data-highlight-id="${data.id}"]`);
                        if (highlightElement) {
                            // Create a prefix element
                            const prefixElement = document.createElement('span');
                            prefixElement.className = 'Stashy-snippet-prefix';

                            // Set prefix text and color based on snippet type
                            if (data.snippetType === 'review') {
                                const reviewType = data.reviewType || 'pro'; // Default to pro if not specified
                                const prefix = reviewType.toLowerCase() === 'pro' ? '[Review Pro]: ' : '[Review Con]: ';
                                prefixElement.textContent = prefix;
                                prefixElement.style.fontWeight = 'bold';
                                prefixElement.style.color = reviewType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'; // Green for pro, red for con
                            } else if (data.snippetType === 'spec') {
                                prefixElement.textContent = '[Spec]: ';
                                prefixElement.style.fontWeight = 'bold';
                                prefixElement.style.color = '#2196F3'; // Blue for spec
                            }

                            // Insert the prefix at the beginning of the highlight
                            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
                        }
                    }

                    // Update restoration success status
                    data.restorationStatus.lastSuccessfulRestore = currentTime;
                    data.restorationStatus.isTemporarilyUnrestorable = false;
                    // Reset failure count on successful restoration
                    if (data.restorationStatus.restorationFailures > 0) {
                        data.restorationStatus.restorationFailures = 0;
                        needsSave = true;
                    }

                    appliedCount++;
                } catch (applyError) {
                    console.error(`   [FAILED APPLY] Error applying highlight ID ${data.id}:`, applyError);
                    // Update restoration failure status
                    data.restorationStatus.restorationFailures++;
                    data.restorationStatus.isTemporarilyUnrestorable = data.restorationStatus.restorationFailures >= 3;
                    needsSave = true;
                    failedCount++;
                }
            } else {
                // Update restoration failure status
                data.restorationStatus.restorationFailures++;
                data.restorationStatus.isTemporarilyUnrestorable = data.restorationStatus.restorationFailures >= 3;
                needsSave = true;
                failedCount++;
                // Failure reason is logged inside deserializeRangeByContext now
            }
        });
        console.log(`Stashy: Finished Applying Highlights. Applied=${appliedCount}, Failed/Skipped=${failedCount}.`);

        // Show UI feedback if any highlights failed to restore
        if (failedCount > 0) {
            // Create a more detailed warning message at the top of the page
            showHighlightRestorationWarning(failedCount, appliedCount, highlightKey);
        }

        // 4. Save data if modifications were made (restoration status updates, migrations, etc.)
        // IMPORTANT: We no longer remove highlights from storage when they fail to restore
        // All highlights are preserved to maintain data integrity and user trust
        if (needsSave) {
            console.log(`Stashy: Saving highlight data with restoration status updates and migrations.`);
            saveHighlights(); // Save the updated data (with restoration status) back to storage
        }

        // Re-enable threat detection after highlight loading is complete
        if (window.StashyThreatDetection) {
            setTimeout(() => {
                window.StashyThreatDetection.resume();
                console.log("Stashy: Resumed threat detection after highlight loading");
            }, 2000); // Wait 2 seconds to ensure all highlights are applied and DOM is stable
        }

        // Reset loading flag
        isLoadingHighlights = false;
    });
}


/**
 * Handles context menu request OR palette button click to highlight selection.
 * Calls the modified applyHighlight.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightSelection(color, style) {
    if (!style) {
        console.error("Stashy: handleHighlightSelection called without a style.");
        return;
    }

    // --- Premium Check for Highlight Creation ---
    if (typeof window.StashyPremium !== 'undefined') {
        // Check if user can create a new highlight
        if (!window.StashyPremium.canPerformAction('highlight')) {
            console.log('Stashy: Highlight creation blocked - free tier limit reached');
            if (typeof showStatus === 'function') {
                showStatus('Free tier limit reached! Upgrade to Stashy Pro for unlimited highlights.', 'warning', 5000);
            }

            // Show upgrade prompt
            if (window.StashyPremium.showUpgradePrompt) {
                window.StashyPremium.showUpgradePrompt('Unlimited Highlights', 'highlight-creation');
            }

            // Clear selection
            const selection = window.getSelection();
            if (selection) {
                selection.collapseToEnd();
            }
            return;
        }
    }

    // Pause threat detection immediately to prevent false positives during highlighting
    if (window.StashyThreatDetection) {
        window.StashyThreatDetection.pause();
        console.log("Stashy: Paused threat detection for highlighting operation");
    }

    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
         console.log("Stashy: Highlight cancelled - no selection.");
         // Resume threat detection before returning
         if (window.StashyThreatDetection) {
             setTimeout(() => {
                 window.StashyThreatDetection.resume();
                 console.log("Stashy: Resumed threat detection after cancelled highlight");
             }, 500);
         }
         return;
     }
     // Prevent highlighting within Stashy's own UI elements
     const container = selection.anchorNode?.parentElement;
     if (container?.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
         // Highlight cancelled - selection is inside Stashy UI
         selection.collapseToEnd(); // Clear selection visually
         // Resume threat detection before returning
         if (window.StashyThreatDetection) {
             setTimeout(() => {
                 window.StashyThreatDetection.resume();
                 // Resumed threat detection after cancelled highlight
             }, 500);
         }
         return;
     }

    const range = selection.getRangeAt(0);

    // Detect if this is a cross-paragraph highlight
    const startContainer = range.startContainer;
    const endContainer = range.endContainer;
    const isCrossParagraph = startContainer !== endContainer ||
                           (startContainer.nodeType !== Node.TEXT_NODE || endContainer.nodeType !== Node.TEXT_NODE);

    const serialized = serializeRangeWithContext(range, isCrossParagraph); // Use context serialization

    if (serialized) {
        serialized.id = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
        serialized.style = style;
        // Store color ONLY if style is 'color', otherwise null
        serialized.color = (style === 'color' && color) ? color : null;

        if (!Array.isArray(highlightsData)) {
             console.warn("Stashy: highlightsData was not an array, resetting.");
             highlightsData = [];
         }
        // Simple check for exact duplicate text+context
        const isDuplicate = highlightsData.some(h =>
            h.text === serialized.text &&
            h.prefix === serialized.prefix &&
            h.suffix === serialized.suffix
        );

        if (!isDuplicate) {
            highlightsData.push(serialized);
            saveHighlights();

            // Record usage for premium tracking
            if (typeof window.StashyPremium !== 'undefined') {
                window.StashyPremium.recordUsage('highlight').catch(error => {
                    console.error('Stashy: Error recording highlight usage:', error);
                });
            }

            // Pass potentially null color
            applyHighlight(range, serialized.id, serialized.color, serialized.style);
            selection.collapseToEnd();
            console.log(`Stashy: Highlight added (Style: ${style}${serialized.color ? ', Color: ' + serialized.color : ''}). ID:`, serialized.id);
        } else {
             console.log("Stashy: Highlight text/context already exists.");
             // Find existing highlight ID to apply the *new* style
             const existing = highlightsData.find(h => h.text === serialized.text && h.prefix === serialized.prefix && h.suffix === serialized.suffix);
             if (existing) {
                 // Update existing highlight's style and clear color if needed
                 const newColor = (style === 'color' && color) ? color : null;
                 if (existing.style !== style || existing.color !== newColor) {
                     existing.style = style;
                     existing.color = newColor;
                     needsSave = true; // Mark for saving
                     saveHighlights(); // Save the updated style/color
                 }
                 // Pass potentially null color
                 applyHighlight(range, existing.id, newColor, style);
             } else {
                // This case shouldn't ideally happen if isDuplicate is true, but handle defensively
                highlightsData.push(serialized); // Add as new if somehow not found
                saveHighlights();
                applyHighlight(range, serialized.id, newColor, style);
             }
             selection.collapseToEnd(); // Clear selection visually
        }
    } else {
        console.warn("Stashy: Failed to serialize range for highlighting (Context). Range was:", range.toString());
        selection.collapseToEnd(); // Clear selection visually
    }

    // Resume threat detection after highlighting operation is complete
    if (window.StashyThreatDetection) {
        setTimeout(() => {
            window.StashyThreatDetection.resume();
            console.log("Stashy: Resumed threat detection after highlighting operation");
        }, 1000); // Wait 1 second to ensure DOM operations are complete
    }
}

/**
 * Shows a detailed warning at the top of the page when highlights cannot be restored.
 * @param {number} failedCount - Number of highlights that failed to restore
 * @param {number} appliedCount - Number of highlights that were successfully applied
 * @param {string} highlightKey - The storage key for the highlights
 */
function showHighlightRestorationWarning(failedCount, appliedCount, highlightKey) {
    // Remove any existing warning
    const existingWarning = document.getElementById('Stashy-highlight-warning');
    if (existingWarning) {
        existingWarning.remove();
    }

    // Create the warning container
    const warningContainer = document.createElement('div');
    warningContainer.id = 'Stashy-highlight-warning';
    warningContainer.style.position = 'fixed';
    warningContainer.style.top = '0';
    warningContainer.style.left = '0';
    warningContainer.style.right = '0';
    warningContainer.style.backgroundColor = 'rgba(255, 204, 0, 0.95)';
    warningContainer.style.color = '#333';
    warningContainer.style.padding = '10px 20px';
    warningContainer.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    warningContainer.style.zIndex = '2147483646'; // Very high but below max
    warningContainer.style.fontSize = '14px';
    warningContainer.style.fontFamily = 'Arial, sans-serif';
    warningContainer.style.display = 'flex';
    warningContainer.style.justifyContent = 'space-between';
    warningContainer.style.alignItems = 'center';
    warningContainer.style.opacity = '0'; // Start invisible for fade-in
    warningContainer.style.transition = 'opacity 0.3s ease';
    // Add some margin to push page content down
    document.body.style.marginTop = document.body.style.marginTop ?
        (parseInt(document.body.style.marginTop) + 80) + 'px' :
        '80px';

    // Create the warning message
    const messageDiv = document.createElement('div');

    // Create the title with icon
    const titleSpan = document.createElement('span');
    titleSpan.style.fontWeight = 'bold';
    titleSpan.style.fontSize = '16px';
    titleSpan.style.display = 'block';
    titleSpan.style.marginBottom = '5px';
    titleSpan.innerHTML = '💾 Highlights Safely Stored';
    messageDiv.appendChild(titleSpan);

    // Create the main message
    const totalHighlights = failedCount + appliedCount;
    const messageSpan = document.createElement('span');
    messageSpan.innerHTML = `${failedCount} of ${totalHighlights} highlight${totalHighlights > 1 ? 's' : ''} could not be restored to this page due to content changes.<br>`;

    // Add details about the page
    let pageUrl = highlightKey.replace('Stashy_highlights_', '');
    try {
        // Only decode if it looks like valid percent-encoding
        if (pageUrl.includes('%')) {
            const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
            if (validPercentPattern.test(pageUrl)) {
                pageUrl = decodeURIComponent(pageUrl);
            } else {
                console.warn(`Invalid percent-encoding pattern in highlight key: ${pageUrl}`);
                // Keep original if invalid pattern
            }
        }
    } catch (e) {
        console.warn(`Failed to decode highlight key URL: ${e.message}`);
        // Keep original if decoding fails
    }
    const urlSpan = document.createElement('span');
    urlSpan.style.fontSize = '12px';
    urlSpan.style.color = '#555';
    urlSpan.innerHTML = `<strong>Don't worry:</strong> Your highlights are safely stored and remain accessible in your dashboard.<br>`;
    urlSpan.innerHTML += `This may happen when page content has been updated since the highlights were created.<br>`;
    urlSpan.innerHTML += `Page URL: ${pageUrl.substring(0, 50)}${pageUrl.length > 50 ? '...' : ''}`;

    messageSpan.appendChild(urlSpan);
    messageDiv.appendChild(messageSpan);

    // Create action buttons container
    const actionsDiv = document.createElement('div');
    actionsDiv.style.marginTop = '12px';
    actionsDiv.style.display = 'flex';
    actionsDiv.style.gap = '8px';
    actionsDiv.style.flexWrap = 'wrap';

    // View Dashboard button
    const dashboardButton = document.createElement('button');
    dashboardButton.innerHTML = '📋 View Dashboard';
    dashboardButton.style.background = '#2196F3';
    dashboardButton.style.color = 'white';
    dashboardButton.style.border = 'none';
    dashboardButton.style.padding = '6px 12px';
    dashboardButton.style.borderRadius = '4px';
    dashboardButton.style.fontSize = '12px';
    dashboardButton.style.cursor = 'pointer';
    dashboardButton.style.fontWeight = '500';
    dashboardButton.title = 'Open dashboard to view all highlights';
    dashboardButton.addEventListener('click', () => {
        try {
            // Try multiple methods to open the dashboard
            if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
                chrome.runtime.sendMessage({ action: 'openDashboard' }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.log('Dashboard message failed, trying direct URL method');
                        // Fallback: open dashboard directly
                        window.open(chrome.runtime.getURL('dashboard.html'), '_blank');
                    }
                });
            } else {
                // Direct fallback
                const dashboardUrl = chrome.runtime.getURL('dashboard.html');
                window.open(dashboardUrl, '_blank');
            }
        } catch (error) {
            console.error('Error opening dashboard:', error);
            // Last resort: try to open relative URL
            window.open('/dashboard.html', '_blank');
        }
    });
    actionsDiv.appendChild(dashboardButton);

    // Retry Restoration button
    const retryButton = document.createElement('button');
    retryButton.innerHTML = '🔄 Retry';
    retryButton.style.background = '#4CAF50';
    retryButton.style.color = 'white';
    retryButton.style.border = 'none';
    retryButton.style.padding = '6px 12px';
    retryButton.style.borderRadius = '4px';
    retryButton.style.fontSize = '12px';
    retryButton.style.cursor = 'pointer';
    retryButton.style.fontWeight = '500';
    retryButton.title = 'Try to restore highlights again';
    retryButton.addEventListener('click', () => {
        try {
            console.log('Retry button clicked - checking available functions...');
            console.log('resetDynamicHighlightState available:', typeof resetDynamicHighlightState);
            console.log('window.StashyRestoreHighlights available:', typeof window.StashyRestoreHighlights);
            console.log('restoreHighlightsAfterContentChange available:', typeof restoreHighlightsAfterContentChange);

            // Reset restoration state and try again
            if (typeof resetDynamicHighlightState === 'function') {
                console.log('Resetting dynamic highlight state...');
                resetDynamicHighlightState();
            }

            // Try manual restoration
            if (typeof window.StashyRestoreHighlights === 'function') {
                console.log('Calling StashyRestoreHighlights...');
                const result = window.StashyRestoreHighlights();
                console.log('Manual restoration result:', result);

                // Show feedback
                retryButton.innerHTML = '✅ Retrying...';
                retryButton.style.background = '#FF9800';

                setTimeout(() => {
                    retryButton.innerHTML = '🔄 Retry';
                    retryButton.style.background = '#4CAF50';
                }, 2000);

            } else if (typeof restoreHighlightsAfterContentChange === 'function') {
                console.log('Calling restoreHighlightsAfterContentChange...');
                // Fallback to direct restoration function
                restoreHighlightsAfterContentChange(true);
                retryButton.innerHTML = '✅ Retrying...';
                retryButton.style.background = '#FF9800';

                setTimeout(() => {
                    retryButton.innerHTML = '🔄 Retry';
                    retryButton.style.background = '#4CAF50';
                }, 2000);
            } else {
                console.warn('No restoration function available - offering page reload');
                console.log('Available window functions:', Object.keys(window).filter(k => k.includes('Stashy')));

                // Offer to reload the page as a last resort
                if (confirm('Restoration functions not available. Would you like to reload the page to try restoring highlights?')) {
                    window.location.reload();
                } else {
                    retryButton.innerHTML = '❌ Unavailable';
                    retryButton.style.background = '#f44336';
                    setTimeout(() => {
                        retryButton.innerHTML = '🔄 Retry';
                        retryButton.style.background = '#4CAF50';
                    }, 2000);
                }
            }

            // Don't close the warning immediately, let user see the result
            setTimeout(() => {
                if (warningContainer && warningContainer.parentNode) {
                    warningContainer.style.opacity = '0';
                    setTimeout(() => {
                        if (warningContainer.parentNode) {
                            warningContainer.parentNode.removeChild(warningContainer);
                        }
                    }, 300);
                }
            }, 3000); // Wait 3 seconds before closing

        } catch (error) {
            console.error('Error during retry:', error);
            retryButton.innerHTML = '❌ Error';
            retryButton.style.background = '#f44336';
            setTimeout(() => {
                retryButton.innerHTML = '🔄 Retry';
                retryButton.style.background = '#4CAF50';
            }, 2000);
        }
    });
    actionsDiv.appendChild(retryButton);

    messageDiv.appendChild(actionsDiv);
    warningContainer.appendChild(messageDiv);

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.fontSize = '24px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.color = '#333';
    closeButton.style.marginLeft = '15px';
    closeButton.style.padding = '0 5px';
    closeButton.title = 'Dismiss';
    closeButton.setAttribute('aria-label', 'Dismiss highlight warning');
    closeButton.addEventListener('click', () => {
        warningContainer.style.opacity = '0';
        // Restore body margin
        if (document.body.style.marginTop) {
            document.body.style.marginTop = Math.max(0, parseInt(document.body.style.marginTop) - 80) + 'px';
        }
        setTimeout(() => {
            if (warningContainer.parentNode) {
                warningContainer.parentNode.removeChild(warningContainer);
            }
        }, 300);
    });
    warningContainer.appendChild(closeButton);

    // Add to the document
    document.body.appendChild(warningContainer);

    // Auto-dismiss after 10 seconds
    setTimeout(() => {
        warningContainer.style.opacity = '0';
        // Restore body margin
        if (document.body.style.marginTop) {
            document.body.style.marginTop = Math.max(0, parseInt(document.body.style.marginTop) - 80) + 'px';
        }
        setTimeout(() => {
            if (warningContainer.parentNode) {
                warningContainer.parentNode.removeChild(warningContainer);
            }
        }, 300);
    }, 10000);

    // Fade in the warning after a short delay
    setTimeout(() => {
        warningContainer.style.opacity = '1';
    }, 10);
}

/**
 * Handles context menu request to add selection to note.
 */
function handleNoteFromSelection() {
     const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;
    // Prevent adding from Stashy's own UI elements
     const container = selection.anchorNode?.parentElement;
     if (container?.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
          console.log("Stashy: Add to Note cancelled - selection is inside Stashy UI.");
         return;
     }

    // Get the complete selected text using enhanced extraction
    const range = selection.getRangeAt(0);
    const selectedText = getTextFromRange(range);
    if (!selectedText.trim()) return; // Ignore whitespace-only selections

    // Ensure note UI exists and is visible before adding text
    if (!noteContainer || !noteContainer.classList.contains('visible')) {
        showNote(); // Defined in interactions.js
        // Wait a short moment for the UI to become visible before adding text
        setTimeout(() => addTextToNote(selectedText), 150);
    } else {
         addTextToNote(selectedText); // Add immediately if already visible
    }
    selection.collapseToEnd(); // Clear selection visually
}

/**
 * Appends text to the note editor, wrapping it in a blockquote.
 * @param {string} text - The text to add to the note
 * @param {string|null} [highlightId=null] - Optional highlight ID to link the note to
 */
function addTextToNote(text, highlightId = null) {
    if (!noteText) return;
    // Escape HTML characters within the text itself to prevent XSS or broken HTML
    // Use the global escapeHtml function for consistent security
    const escapedText = window.escapeHtml(text);

    // Create the HTML to insert
    let htmlToInsert = '';

    // If this note is linked to a highlight, add a special class and data attribute
    // but don't add the "Linked to highlight" header
    if (highlightId) {
        htmlToInsert = `<blockquote class="Stashy-linked-note" data-highlight-id="${highlightId}">
            ${escapedText.replace(/\n/g, '<br>')}
        </blockquote><br>`;
    } else {
        // Regular blockquote for normal notes
        htmlToInsert = `<blockquote>${escapedText.replace(/\n/g, '<br>')}</blockquote><br>`;
    }

    insertHtmlAtCursor(htmlToInsert); // Utility function inserts and calls scheduleSave
    // Scroll to the bottom of the note text area after insertion
    noteText.scrollTop = noteText.scrollHeight;
    noteText.focus(); // Keep focus in the editor
}

/**
 * Appends formatted text to the note editor without wrapping in a blockquote.
 * This version allows HTML formatting to be preserved (for snippet prefixes).
 * @param {string} formattedText - The formatted HTML text to add to the note
 * @param {string|null} [highlightId=null] - Optional highlight ID to link the note to
 */
function addFormattedTextToNote(formattedText, highlightId = null) {
    if (!noteText) return;

    // Determine snippet type
    let snippetType = 'normal';
    if (formattedText.includes('👍 Pro:') || formattedText.includes('color: #4CAF50')) {
        snippetType = 'pro';
    } else if (formattedText.includes('👎 Con:') || formattedText.includes('color: #F44336')) {
        snippetType = 'con';
    } else if (formattedText.includes('📋 Spec:') || formattedText.includes('color: #2196F3')) {
        snippetType = 'spec';
    }

    // Create the HTML to insert - no blockquote, just a div with appropriate styling
    let htmlToInsert = '';

    // If this note is linked to a highlight, add a special class and data attribute
    if (highlightId) {
        htmlToInsert = `<div class="Stashy-snippet-note" data-highlight-id="${highlightId}" data-snippet-type="${snippetType}">
            ${formattedText.replace(/\n/g, '<br>')}
        </div><br>`;
    } else {
        // Regular div for normal notes with snippet type if applicable
        htmlToInsert = `<div class="Stashy-snippet-note" ${snippetType !== 'normal' ? `data-snippet-type="${snippetType}"` : ''}>
            ${formattedText.replace(/\n/g, '<br>')}
        </div><br>`;
    }

    insertHtmlAtCursor(htmlToInsert); // Utility function inserts and calls scheduleSave
    // Scroll to the bottom of the note text area after insertion
    noteText.scrollTop = noteText.scrollHeight;
    noteText.focus(); // Keep focus in the editor

    // Check if we need to create a Pro/Con comparison view
    if ((snippetType === 'pro' || snippetType === 'con') && typeof checkForProConSnippets === 'function') {
        console.log(`Stashy: Added a ${snippetType} snippet, checking for comparison view`);
        setTimeout(checkForProConSnippets, 300);
    }
}

/**
 * Handles the "Highlight with Note" context menu action.
 * This creates a highlight and then adds a linked note.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightWithNote(color, style) {
    // Pause threat detection immediately to prevent false positives during highlighting
    if (window.StashyThreatDetection) {
        window.StashyThreatDetection.pause();
        console.log("Stashy: Paused threat detection for highlight with note operation");
    }

    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) {
        // Resume threat detection before returning
        if (window.StashyThreatDetection) {
            setTimeout(() => {
                window.StashyThreatDetection.resume();
                console.log("Stashy: Resumed threat detection after cancelled highlight with note");
            }, 500);
        }
        return;
    }

    // Prevent highlighting from Stashy's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        // Highlight with Note cancelled - selection is inside Stashy UI
        // Resume threat detection before returning
        if (window.StashyThreatDetection) {
            setTimeout(() => {
                window.StashyThreatDetection.resume();
                // Resumed threat detection after cancelled highlight with note
            }, 500);
        }
        return;
    }

    // Get the complete selected text using enhanced extraction
    const range = selection.getRangeAt(0);
    if (!range) return;

    const selectedText = getTextFromRange(range);
    if (!selectedText.trim()) return; // Ignore whitespace-only selections

    // Detect if this is a cross-paragraph highlight
    const startContainer = range.startContainer;
    const endContainer = range.endContainer;
    const isCrossParagraph = startContainer !== endContainer ||
                           (startContainer.nodeType !== Node.TEXT_NODE || endContainer.nodeType !== Node.TEXT_NODE);

    // Serialize the range for storage and retrieval
    const serialized = serializeRangeWithContext(range, isCrossParagraph);
    if (!serialized) {
        console.warn("Stashy: Failed to serialize range for highlighting with note.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.hasLinkedNote = true; // Mark that this highlight has a linked note

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stashy: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page with the linked note flag
        applyHighlight(range, highlightId, serialized.color, serialized.style, true);

        // We no longer add the note to the Stashy as requested by the user
        // The note is only displayed on the highlight itself

        // Clear the selection
        selection.collapseToEnd();

        console.log(`Stashy: Highlight with Note added. ID: ${highlightId}`);
    } else {
        console.log("Stashy: Highlight text/context already exists. Adding note only.");
        // Find existing highlight to link the note to
        const existing = highlightsData.find(h =>
            h.text === serialized.text &&
            h.prefix === serialized.prefix &&
            h.suffix === serialized.suffix
        );

        if (existing) {
            // Mark the existing highlight as having a linked note
            existing.hasLinkedNote = true;
            saveHighlights();

            // We no longer add the note to the Stashy as requested by the user
            // The note is only displayed on the highlight itself

            // Clear the selection
            selection.collapseToEnd();
        }
    }

    // Resume threat detection after highlight with note operation is complete
    if (window.StashyThreatDetection) {
        setTimeout(() => {
            window.StashyThreatDetection.resume();
            console.log("Stashy: Resumed threat detection after highlight with note operation");
        }, 1000); // Wait 1 second to ensure DOM operations are complete
    }
}


/**
 * Shows a note editor for adding a note to a highlight
 * @param {string} highlightId - The ID of the highlight to add a note to
 * @param {string} highlightText - The text of the highlight
 */
function showNoteEditor(highlightId, highlightText) {
    if (!highlightId) {
        console.error("Stashy: Cannot show note editor - missing highlight ID");
        return;
    }

    // Create a modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'Stashy-note-editor-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '10001';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.opacity = '0';
    overlay.style.transition = 'opacity 0.2s ease';

    // Add click handler to close when clicking outside
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay);
        }
    });

    // Fade in the overlay after it's added to the DOM
    setTimeout(() => {
        overlay.style.opacity = '1';
    }, 10);

    // Create the editor container
    const editor = document.createElement('div');
    editor.className = 'Stashy-note-editor';
    editor.style.backgroundColor = '#fffef5'; // Slightly warmer background
    editor.style.borderRadius = '12px';
    editor.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2), 0 0 2px rgba(0, 0, 0, 0.3)';
    editor.style.width = '450px';
    editor.style.maxWidth = '90%';
    editor.style.maxHeight = '80%';
    editor.style.display = 'flex';
    editor.style.flexDirection = 'column';
    editor.style.overflow = 'hidden';
    editor.style.animation = 'Stashy-note-editor-fade-in 0.3s ease-out';
    editor.style.border = '1px solid #ffd54f';

    // Create the header
    const header = document.createElement('div');
    header.style.padding = '16px 20px';
    header.style.borderBottom = '1px solid #ffd54f';
    header.style.display = 'flex';
    header.style.justifyContent = 'space-between';
    header.style.alignItems = 'center';
    header.style.backgroundColor = '#fff9c4';

    const title = document.createElement('h3');

    // FIXED: Show different title for editing vs creating new note
    const existingHighlight = highlightsData.find(h => h.id === highlightId);
    const isEditing = existingHighlight && existingHighlight.noteText;
    title.innerHTML = isEditing ? '✏️ Edit Note' : '📝 Add Note to Highlight';

    title.style.margin = '0';
    title.style.fontSize = '18px';
    title.style.fontWeight = '600';
    title.style.color = '#111827';

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.style.background = 'none';
    closeBtn.style.border = 'none';
    closeBtn.style.fontSize = '24px';
    closeBtn.style.cursor = 'pointer';
    closeBtn.style.padding = '0';
    closeBtn.style.width = '32px';
    closeBtn.style.height = '32px';
    closeBtn.style.display = 'flex';
    closeBtn.style.alignItems = 'center';
    closeBtn.style.justifyContent = 'center';
    closeBtn.style.borderRadius = '50%';
    closeBtn.style.transition = 'background-color 0.2s ease';
    closeBtn.style.color = '#4b5563';

    // Add hover effect
    closeBtn.addEventListener('mouseover', () => {
        closeBtn.style.backgroundColor = '#f3f4f6';
        closeBtn.style.color = '#111827';
    });
    closeBtn.addEventListener('mouseout', () => {
        closeBtn.style.backgroundColor = 'transparent';
        closeBtn.style.color = '#4b5563';
    });

    closeBtn.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    header.appendChild(title);
    header.appendChild(closeBtn);

    // Create the content area
    const content = document.createElement('div');
    content.style.padding = '20px';
    content.style.overflowY = 'auto';

    // Add a label for the highlighted text
    const previewLabel = document.createElement('div');
    previewLabel.textContent = 'Highlighted Text:';
    previewLabel.style.fontSize = '14px';
    previewLabel.style.fontWeight = '500';
    previewLabel.style.color = '#4b5563';
    previewLabel.style.marginBottom = '6px';

    // Show the highlighted text
    const highlightPreview = document.createElement('div');
    highlightPreview.style.padding = '12px 16px';
    highlightPreview.style.backgroundColor = '#fff9c4';
    highlightPreview.style.border = '1px solid #ffd54f';
    highlightPreview.style.borderRadius = '6px';
    highlightPreview.style.marginBottom = '16px';
    highlightPreview.style.fontSize = '15px';
    highlightPreview.style.color = '#333';
    highlightPreview.style.fontStyle = 'normal';
    highlightPreview.style.lineHeight = '1.5';
    highlightPreview.style.maxHeight = '100px';
    highlightPreview.style.overflowY = 'auto';
    highlightPreview.textContent = highlightText || 'Selected text';

    // Add a label for the note textarea
    const noteLabel = document.createElement('div');
    noteLabel.textContent = 'Your Note:';
    noteLabel.style.fontSize = '14px';
    noteLabel.style.fontWeight = '500';
    noteLabel.style.color = '#4b5563';
    noteLabel.style.marginBottom = '6px';

    // Create the textarea for the note
    const textarea = document.createElement('textarea');
    textarea.placeholder = 'Write your note here...';
    textarea.style.width = '100%';
    textarea.style.minHeight = '150px';
    textarea.style.padding = '12px 16px';
    textarea.style.borderRadius = '6px';
    textarea.style.border = '1px solid #d1d5db';
    textarea.style.resize = 'vertical';
    textarea.style.fontSize = '15px';
    textarea.style.lineHeight = '1.5';
    textarea.style.boxSizing = 'border-box';
    textarea.style.fontFamily = 'inherit';
    textarea.style.transition = 'border-color 0.2s ease, box-shadow 0.2s ease';

    // FIXED: Pre-fill textarea with existing note text if editing
    if (existingHighlight && existingHighlight.noteText) {
        textarea.value = existingHighlight.noteText;
        console.log("Stashy: Pre-filled note editor with existing text:", existingHighlight.noteText);
    }

    // Create character counter
    const charCounter = document.createElement('div');
    charCounter.style.fontSize = '12px';
    charCounter.style.color = '#6b7280';
    charCounter.style.textAlign = 'right';
    charCounter.style.marginTop = '4px';
    charCounter.style.paddingRight = '4px';

    // FIXED: Initialize character counter with existing text length
    const initialCount = textarea.value.length;
    charCounter.textContent = `${initialCount} character${initialCount !== 1 ? 's' : ''}`;
    if (initialCount > 200) {
        charCounter.style.color = '#f59e0b';
    }

    // Update character counter on input
    textarea.addEventListener('input', () => {
        const count = textarea.value.length;
        charCounter.textContent = `${count} character${count !== 1 ? 's' : ''}`;

        // Change color if getting long
        if (count > 200) {
            charCounter.style.color = '#f59e0b';
        } else {
            charCounter.style.color = '#6b7280';
        }
    });

    // Add focus effect
    textarea.addEventListener('focus', () => {
        textarea.style.outline = 'none';
        textarea.style.borderColor = '#3b82f6';
        textarea.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.2)';
    });

    textarea.addEventListener('blur', () => {
        textarea.style.boxShadow = 'none';
        textarea.style.borderColor = '#d1d5db';
    });

    // Add all elements to content area
    content.appendChild(previewLabel);
    content.appendChild(highlightPreview);
    content.appendChild(noteLabel);
    content.appendChild(textarea);
    content.appendChild(charCounter);

    // Create the footer with action buttons
    const footer = document.createElement('div');
    footer.style.padding = '16px 20px';
    footer.style.borderTop = '1px solid #ffd54f';
    footer.style.display = 'flex';
    footer.style.justifyContent = 'flex-end';
    footer.style.gap = '12px';
    footer.style.backgroundColor = '#fff9c4';

    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = 'Cancel';
    cancelBtn.style.padding = '8px 16px';
    cancelBtn.style.borderRadius = '6px';
    cancelBtn.style.border = '1px solid #d1d5db';
    cancelBtn.style.backgroundColor = '#ffffff';
    cancelBtn.style.color = '#4b5563';
    cancelBtn.style.fontSize = '14px';
    cancelBtn.style.fontWeight = '500';
    cancelBtn.style.cursor = 'pointer';
    cancelBtn.style.transition = 'all 0.2s ease';

    // Add hover effect
    cancelBtn.addEventListener('mouseover', () => {
        cancelBtn.style.backgroundColor = '#f3f4f6';
        cancelBtn.style.borderColor = '#9ca3af';
    });

    cancelBtn.addEventListener('mouseout', () => {
        cancelBtn.style.backgroundColor = '#ffffff';
        cancelBtn.style.borderColor = '#d1d5db';
    });

    cancelBtn.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    const saveBtn = document.createElement('button');

    // FIXED: Show different button text for editing vs creating
    saveBtn.innerHTML = isEditing ? '✏️ Update Note' : '📝 Save Note';

    saveBtn.style.padding = '8px 16px';
    saveBtn.style.borderRadius = '6px';
    saveBtn.style.border = '1px solid #f57c00';
    saveBtn.style.backgroundColor = '#ffb74d';
    saveBtn.style.color = '#333';
    saveBtn.style.fontSize = '14px';
    saveBtn.style.fontWeight = '600';
    saveBtn.style.cursor = 'pointer';
    saveBtn.style.transition = 'all 0.2s ease';

    // Add hover effect
    saveBtn.addEventListener('mouseover', () => {
        saveBtn.style.backgroundColor = '#ffa726';
        saveBtn.style.transform = 'translateY(-1px)';
        saveBtn.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
    });

    saveBtn.addEventListener('mouseout', () => {
        saveBtn.style.backgroundColor = '#ffb74d';
        saveBtn.style.transform = 'translateY(0)';
        saveBtn.style.boxShadow = 'none';
    });
    saveBtn.addEventListener('click', () => {
        const noteText = textarea.value.trim();
        if (noteText) {
            // Find the highlight in the data
            const highlight = highlightsData.find(h => h.id === highlightId);
            if (highlight) {
                // Mark the highlight as having a linked note
                highlight.hasLinkedNote = true;
                saveHighlights();

                // Store the note text with the highlight
                highlight.noteText = noteText;

                // Store position if it exists
                if (highlight.notePosition) {
                    // Keep existing position
                } else {
                    // Default position (below the highlight)
                    highlight.notePosition = {
                        top: '100%',
                        left: '0',
                        marginTop: '8px'
                    };
                }

                saveHighlights();

                // Add a visible note to the highlight
                const highlightElement = document.querySelector(`mark.Stashy-highlight[data-highlight-id="${highlightId}"]`);
                if (highlightElement) {
                    // Add or update the note display
                    let noteDisplay = highlightElement.querySelector('.Stashy-highlight-note-display');
                    if (!noteDisplay) {
                        noteDisplay = document.createElement('div');
                        noteDisplay.className = 'Stashy-highlight-note-display';

                        // Add drag handle
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stashy-note-drag-handle';
                        noteDisplay.appendChild(dragHandle);

                        // Apply stored position if available
                        if (highlight.notePosition) {
                            noteDisplay.style.top = highlight.notePosition.top;
                            noteDisplay.style.left = highlight.notePosition.left;
                            if (highlight.notePosition.marginTop) {
                                noteDisplay.style.marginTop = highlight.notePosition.marginTop;
                            }
                        }

                        // Make the note draggable
                        makeDraggable(noteDisplay, highlightElement, highlightId);

                        highlightElement.appendChild(noteDisplay);
                    }
                    noteDisplay.textContent = noteText;

                    // Re-add the drag handle after setting text content
                    if (!noteDisplay.querySelector('.Stashy-note-drag-handle')) {
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'Stashy-note-drag-handle';
                        noteDisplay.insertBefore(dragHandle, noteDisplay.firstChild);
                    }
                }

                // We no longer add the note to the Stashy as requested by the user
                // The note is only displayed on the highlight itself
            }
        }
        document.body.removeChild(overlay);
    });

    footer.appendChild(cancelBtn);
    footer.appendChild(saveBtn);

    // Assemble the editor
    editor.appendChild(header);
    editor.appendChild(content);
    editor.appendChild(footer);

    overlay.appendChild(editor);
    document.body.appendChild(overlay);

    // Focus the textarea
    setTimeout(() => {
        textarea.focus();
    }, 100);
}



/**
 * Makes an element draggable
 * @param {HTMLElement} element - The element to make draggable
 * @param {HTMLElement} container - The container element (highlight)
 * @param {string} highlightId - The ID of the highlight
 */
function makeDraggable(element, container, highlightId) {
    let isDragging = false;
    let startX, startY;
    let startLeft, startTop;

    // Function to handle mouse down event
    function handleMouseDown(e) {
        // Only start drag on left mouse button
        if (e.button !== 0) return;

        e.preventDefault();
        e.stopPropagation();

        // Get initial positions
        startX = e.clientX;
        startY = e.clientY;

        // Get the current position of the element
        const rect = element.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // Calculate the position relative to the container
        startLeft = rect.left - containerRect.left;
        startTop = rect.top - containerRect.top;

        // Create a ghost element to show the original position
        const ghost = document.createElement('div');
        ghost.className = 'Stashy-highlight-note-ghost';
        ghost.style.position = 'absolute';
        ghost.style.top = element.style.top || `${startTop}px`;
        ghost.style.left = element.style.left || `${startLeft}px`;
        ghost.style.width = `${rect.width}px`;
        ghost.style.height = `${rect.height}px`;
        ghost.style.borderRadius = '8px';
        ghost.style.border = '1px dashed #ffd54f';
        ghost.style.backgroundColor = 'rgba(255, 249, 196, 0.3)';
        ghost.style.zIndex = '9998';
        ghost.style.pointerEvents = 'none';
        container.appendChild(ghost);

        // Store the ghost element for later removal
        element._ghost = ghost;

        // Add dragging class
        element.classList.add('dragging');

        // Add event listeners for mouse move and mouse up
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }

    // Function to handle mouse move event
    function handleMouseMove(e) {
        if (!isDragging) {
            isDragging = true;

            // Add a visual guide for dragging
            const guide = document.createElement('div');
            guide.className = 'Stashy-drag-guide';
            guide.style.position = 'fixed';
            guide.style.top = '10px';
            guide.style.left = '50%';
            guide.style.transform = 'translateX(-50%)';
            guide.style.padding = '8px 16px';
            guide.style.backgroundColor = 'rgba(255, 213, 79, 0.9)';
            guide.style.color = '#333';
            guide.style.borderRadius = '20px';
            guide.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
            guide.style.fontFamily = 'Arial, sans-serif';
            guide.style.fontSize = '14px';
            guide.style.fontWeight = 'bold';
            guide.style.zIndex = '10001';
            guide.style.pointerEvents = 'none';
            guide.style.opacity = '0';
            guide.style.transition = 'opacity 0.3s ease';
            guide.textContent = '✨ Drag to position your note ✨';
            document.body.appendChild(guide);

            // Store the guide element for later removal
            element._guide = guide;

            // Fade in the guide
            setTimeout(() => {
                if (guide.parentNode) {
                    guide.style.opacity = '1';
                }
            }, 10);
        }

        e.preventDefault();

        // Calculate the new position
        const dx = e.clientX - startX;
        const dy = e.clientY - startY;

        // Update the element's position
        element.style.left = `${startLeft + dx}px`;
        element.style.top = `${startTop + dy}px`;

        // Remove margin-top when dragging
        element.style.marginTop = '0';
    }

    // Function to handle mouse up event
    function handleMouseUp() {
        // Remove dragging class
        element.classList.add('drop-animation');
        element.classList.remove('dragging');

        // Remove the ghost element if it exists
        if (element._ghost && element._ghost.parentNode) {
            element._ghost.parentNode.removeChild(element._ghost);
            element._ghost = null;
        }

        // Remove the guide element if it exists
        if (element._guide && element._guide.parentNode) {
            // Fade out the guide
            element._guide.style.opacity = '0';

            // Remove after fade out
            setTimeout(() => {
                if (element._guide && element._guide.parentNode) {
                    element._guide.parentNode.removeChild(element._guide);
                    element._guide = null;
                }
            }, 300);
        }

        // Remove event listeners
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // If we actually dragged (not just clicked)
        if (isDragging) {
            // Save the new position
            const highlight = highlightsData.find(h => h.id === highlightId);
            if (highlight) {
                highlight.notePosition = {
                    top: element.style.top,
                    left: element.style.left,
                    marginTop: '0'
                };
                saveHighlights();
            }

            // Remove the drop animation class after animation completes
            setTimeout(() => {
                element.classList.remove('drop-animation');
            }, 300);

            isDragging = false;
        } else {
            element.classList.remove('drop-animation');
        }
    }

    // Add event listener for mouse down
    element.addEventListener('mousedown', handleMouseDown);

    // Store the event listener reference for potential cleanup
    element._dragListener = handleMouseDown;
}

/**
 * Handles the "Highlight as Review Snippet" context menu action.
 * This creates a highlight with a prefix indicating whether it's a pro or con review point.
 * Also adds the highlighted text with prefix to the Stashy.
 * @param {string} snippetType - The type of review snippet ('pro' or 'con')
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightReviewSnippet(snippetType, color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stashy: Review Snippet highlight cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stashy's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stashy: Review Snippet highlight cancelled - selection is inside Stashy UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Get the complete selected text using enhanced extraction
    const selectedText = getTextFromRange(range);
    if (!selectedText.trim()) {
        console.log("Stashy: Review Snippet highlight cancelled - empty selection.");
        return;
    }

    // Determine the prefix based on the snippet type
    const prefix = snippetType.toLowerCase() === 'pro' ? '👍 Pro: ' : '👎 Con: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stashy: Failed to serialize range for review snippet highlighting.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'review';
    serialized.reviewType = snippetType.toLowerCase();
    serialized.prefix = prefix; // Store the prefix for display purposes

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stashy: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stashy-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stashy-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'; // Green for pro, red for con

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Add the highlighted text with prefix to the Stashy
        // Ensure note UI exists and is visible before adding text
        if (!noteContainer || !noteContainer.classList.contains('visible')) {
            showNote(); // Defined in interactions.js
            // Wait a short moment for the UI to become visible before adding text
            setTimeout(() => {
                // Create formatted text with appropriate styling for the Stashy
                const textWithPrefix = `<span style="font-weight: bold; color: ${snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'}">${prefix}</span>${selectedText}`;
                addFormattedTextToNote(textWithPrefix, highlightId);
            }, 150);
        } else {
            // Create formatted text with appropriate styling for the Stashy
            const textWithPrefix = `<span style="font-weight: bold; color: ${snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'}">${prefix}</span>${selectedText}`;
            addFormattedTextToNote(textWithPrefix, highlightId);
        }

        // Clear the selection
        selection.collapseToEnd();

        console.log(`Stashy: Review ${snippetType} Snippet highlight added. ID: ${highlightId}`);
    } else {
        console.log("Stashy: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Handles the "Highlight as Spec Snippet" context menu action.
 * This creates a highlight with a prefix indicating it's a specification detail.
 * Also adds the highlighted text with prefix to the Stashy.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightSpecSnippet(color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stashy: Spec Snippet highlight cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stashy's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stashy: Spec Snippet highlight cancelled - selection is inside Stashy UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Get the complete selected text using enhanced extraction
    const selectedText = getTextFromRange(range);
    if (!selectedText.trim()) {
        console.log("Stashy: Spec Snippet highlight cancelled - empty selection.");
        return;
    }

    // Define the prefix
    const prefix = '📋 Spec: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stashy: Failed to serialize range for spec snippet highlighting.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'spec';
    serialized.prefix = prefix; // Store the prefix for display purposes

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stashy: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stashy-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stashy-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = '#2196F3'; // Blue for spec

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Add the highlighted text with prefix to the Stashy
        // Ensure note UI exists and is visible before adding text
        if (!noteContainer || !noteContainer.classList.contains('visible')) {
            showNote(); // Defined in interactions.js
            // Wait a short moment for the UI to become visible before adding text
            setTimeout(() => {
                // Create formatted text with appropriate styling for the Stashy
                const textWithPrefix = `<span style="font-weight: bold; color: #2196F3">${prefix}</span>${selectedText}`;
                addFormattedTextToNote(textWithPrefix, highlightId);
            }, 150);
        } else {
            // Create formatted text with appropriate styling for the Stashy
            const textWithPrefix = `<span style="font-weight: bold; color: #2196F3">${prefix}</span>${selectedText}`;
            addFormattedTextToNote(textWithPrefix, highlightId);
        }

        // Clear the selection
        selection.collapseToEnd();

        console.log(`Stashy: Spec Snippet highlight added. ID: ${highlightId}`);
    } else {
        console.log("Stashy: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Handles the "Highlight as Review Snippet with Note" context menu action.
 * This creates a highlight with a prefix indicating whether it's a pro or con review point,
 * and opens a note editor for adding a note to the highlight.
 * @param {string} snippetType - The type of review snippet ('pro' or 'con')
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightReviewSnippetWithNote(snippetType, color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stashy: Review Snippet highlight with note cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stashy's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stashy: Review Snippet highlight with note cancelled - selection is inside Stashy UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Get the complete selected text using enhanced extraction
    const selectedText = getTextFromRange(range);
    if (!selectedText.trim()) {
        console.log("Stashy: Review Snippet highlight with note cancelled - empty selection.");
        return;
    }

    // Determine the prefix based on the snippet type
    const prefix = snippetType.toLowerCase() === 'pro' ? '👍 Pro: ' : '👎 Con: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stashy: Failed to serialize range for review snippet highlighting with note.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'review';
    serialized.reviewType = snippetType.toLowerCase();
    serialized.prefix = prefix; // Store the prefix for display purposes
    serialized.hasLinkedNote = true; // Mark that this highlight has a linked note

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stashy: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style, true);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stashy-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stashy-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = snippetType.toLowerCase() === 'pro' ? '#4CAF50' : '#F44336'; // Green for pro, red for con

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Show the note editor for this highlight
        showNoteEditor(highlightId, selectedText);

        console.log(`Stashy: Review ${snippetType} Snippet highlight with note added. ID: ${highlightId}`);
    } else {
        console.log("Stashy: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Handles the "Highlight as Spec Snippet with Note" context menu action.
 * This creates a highlight with a prefix indicating it's a specification detail,
 * and opens a note editor for adding a note to the highlight.
 * @param {string|null} color - The color name, or null/undefined if style is not 'color'.
 * @param {string} style - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function handleHighlightSpecSnippetWithNote(color, style) {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || !selection.rangeCount) {
        console.log("Stashy: Spec Snippet highlight with note cancelled - no selection.");
        return;
    }

    // Prevent highlighting within Stashy's own UI elements
    const container = selection.anchorNode?.parentElement;
    if (container?.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stashy: Spec Snippet highlight with note cancelled - selection is inside Stashy UI.");
        selection.collapseToEnd(); // Clear selection visually
        return;
    }

    // Create a range from the selection
    const range = selection.getRangeAt(0);
    if (!range) return;

    // Get the complete selected text using enhanced extraction
    const selectedText = getTextFromRange(range);
    if (!selectedText.trim()) {
        console.log("Stashy: Spec Snippet highlight with note cancelled - empty selection.");
        return;
    }

    // Define the prefix
    const prefix = '📋 Spec: ';

    // Create a new range that includes the prefix
    const newRange = document.createRange();
    newRange.setStart(range.startContainer, range.startOffset);
    newRange.setEnd(range.endContainer, range.endOffset);

    // Serialize the range for storage
    const serialized = serializeRangeWithContext(newRange);
    if (!serialized) {
        console.warn("Stashy: Failed to serialize range for spec snippet highlighting with note.");
        return;
    }

    // Generate a unique ID for this highlight
    const highlightId = `hl-${Date.now()}-${Math.random().toString(16).slice(2)}`;
    serialized.id = highlightId;
    serialized.style = style;
    serialized.color = (style === 'color' && color) ? color : null;
    serialized.snippetType = 'spec';
    serialized.prefix = prefix; // Store the prefix for display purposes
    serialized.hasLinkedNote = true; // Mark that this highlight has a linked note

    // Add the highlight to storage
    if (!Array.isArray(highlightsData)) {
        console.warn("Stashy: highlightsData was not an array, resetting.");
        highlightsData = [];
    }

    // Check for duplicates
    const isDuplicate = highlightsData.some(h =>
        h.text === serialized.text &&
        h.prefix === serialized.prefix &&
        h.suffix === serialized.suffix
    );

    if (!isDuplicate) {
        // Add the new highlight
        highlightsData.push(serialized);
        saveHighlights();

        // Apply the highlight to the page
        applyHighlight(range, highlightId, serialized.color, serialized.style, true);

        // Add the prefix to the highlighted text in the DOM
        const highlightElement = document.querySelector(`mark.Stashy-highlight[data-highlight-id="${highlightId}"]`);
        if (highlightElement) {
            // Create a prefix element
            const prefixElement = document.createElement('span');
            prefixElement.className = 'Stashy-snippet-prefix';
            prefixElement.textContent = prefix;
            prefixElement.style.fontWeight = 'bold';
            prefixElement.style.color = '#2196F3'; // Blue for spec

            // Insert the prefix at the beginning of the highlight
            highlightElement.insertBefore(prefixElement, highlightElement.firstChild);
        }

        // Show the note editor for this highlight
        showNoteEditor(highlightId, selectedText);

        console.log(`Stashy: Spec Snippet highlight with note added. ID: ${highlightId}`);
    } else {
        console.log("Stashy: Highlight text/context already exists.");
        selection.collapseToEnd(); // Clear selection visually
    }
}

/**
 * Creates a side-by-side comparison container for Pro and Con snippets
 * This function is called when both Pro and Con snippets are present in the note
 * It also hides the original Pro and Con snippets
 */
function createProConComparisonView() {
    if (!noteText) return;

    console.log("Stashy: Creating Pro/Con comparison view...");

    // Remove any existing comparison view
    const existingComparison = noteText.querySelector('.Stashy-comparison-container');
    const existingTitle = noteText.querySelector('.Stashy-comparison-title');

    if (existingComparison) {
        existingComparison.remove();
    }

    if (existingTitle) {
        existingTitle.remove();
    }

    // Find all Pro and Con snippets in the note
    const proSnippets = Array.from(noteText.querySelectorAll('.Stashy-snippet-note[data-snippet-type="pro"]'));
    const conSnippets = Array.from(noteText.querySelectorAll('.Stashy-snippet-note[data-snippet-type="con"]'));

    console.log(`Stashy: Found ${proSnippets.length} Pro snippets and ${conSnippets.length} Con snippets`);

    // Only proceed if we have both Pro and Con snippets
    if (proSnippets.length === 0 || conSnippets.length === 0) {
        console.log("Stashy: Not enough snippets for comparison view");

        // Show all Pro and Con snippets if comparison view is not created
        proSnippets.forEach(snippet => {
            snippet.style.display = '';
        });

        conSnippets.forEach(snippet => {
            snippet.style.display = '';
        });

        return;
    }

    // Create a comparison container
    const comparisonContainer = document.createElement('div');
    comparisonContainer.className = 'Stashy-comparison-container';
    comparisonContainer.id = 'Stashy-comparison-container';

    // Create Pro column
    const proColumn = document.createElement('div');
    proColumn.className = 'Stashy-comparison-column pro-column';

    // Create Pro header
    const proHeader = document.createElement('div');
    proHeader.className = 'Stashy-comparison-header pro-header';
    proHeader.innerHTML = '<span style="font-weight: bold; color: #4CAF50">👍 Pros</span>';
    proColumn.appendChild(proHeader);

    // Create Con column
    const conColumn = document.createElement('div');
    conColumn.className = 'Stashy-comparison-column con-column';

    // Create Con header
    const conHeader = document.createElement('div');
    conHeader.className = 'Stashy-comparison-header con-header';
    conHeader.innerHTML = '<span style="font-weight: bold; color: #F44336">👎 Cons</span>';
    conColumn.appendChild(conHeader);

    // Add Pro snippets to Pro column with numbers
    proSnippets.forEach((snippet, index) => {
        // Extract the text content (without the prefix)
        const snippetText = snippet.textContent.replace(/👍 Pro: /, '').trim();

        // Create a new item for the comparison view with number
        const proItem = document.createElement('div');
        proItem.className = 'Stashy-comparison-item pro-item';

        // Add number span
        const numberSpan = document.createElement('span');
        numberSpan.className = 'Stashy-comparison-number';
        numberSpan.textContent = `${index + 1}. `;

        // Add text span
        const textSpan = document.createElement('span');
        textSpan.textContent = snippetText;

        // Add spans to item
        proItem.appendChild(numberSpan);
        proItem.appendChild(textSpan);

        // Add the item to the Pro column
        proColumn.appendChild(proItem);

        // Hide the original snippet
        snippet.style.display = 'none';

        // Also hide the <br> element that follows the snippet
        const nextBr = snippet.nextElementSibling;
        if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
            nextBr.style.display = 'none';
        }
    });

    // Add Con snippets to Con column with numbers
    conSnippets.forEach((snippet, index) => {
        // Extract the text content (without the prefix)
        const snippetText = snippet.textContent.replace(/👎 Con: /, '').trim();

        // Create a new item for the comparison view with number
        const conItem = document.createElement('div');
        conItem.className = 'Stashy-comparison-item con-item';

        // Add number span
        const numberSpan = document.createElement('span');
        numberSpan.className = 'Stashy-comparison-number';
        numberSpan.textContent = `${index + 1}. `;

        // Add text span
        const textSpan = document.createElement('span');
        textSpan.textContent = snippetText;

        // Add spans to item
        conItem.appendChild(numberSpan);
        conItem.appendChild(textSpan);

        // Add the item to the Con column
        conColumn.appendChild(conItem);

        // Hide the original snippet
        snippet.style.display = 'none';

        // Also hide the <br> element that follows the snippet
        const nextBr = snippet.nextElementSibling;
        if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
            nextBr.style.display = 'none';
        }
    });

    // Add columns to the comparison container
    comparisonContainer.appendChild(proColumn);
    comparisonContainer.appendChild(conColumn);

    // Add a title for the comparison view
    const comparisonTitle = document.createElement('div');
    comparisonTitle.className = 'Stashy-comparison-title';
    comparisonTitle.innerHTML = '<span style="font-weight: bold;">Pro/Con Comparison</span>';

    // Insert the comparison title and container at the end of the note
    noteText.appendChild(document.createElement('br'));
    noteText.appendChild(comparisonTitle);
    noteText.appendChild(comparisonContainer);

    console.log("Stashy: Pro/Con comparison view created");

    // Save the note content
    scheduleSave();
}

// Add a function to check for Pro/Con snippets and create comparison view when needed
function checkForProConSnippets() {
    if (!noteText) {
        console.log("Stashy: checkForProConSnippets - noteText not found");
        return;
    }

    console.log("Stashy: Checking for Pro/Con snippets...");

    // Find all Pro and Con snippets in the note
    const proSnippets = noteText.querySelectorAll('.Stashy-snippet-note[data-snippet-type="pro"]');
    const conSnippets = noteText.querySelectorAll('.Stashy-snippet-note[data-snippet-type="con"]');

    console.log(`Stashy: Found ${proSnippets.length} Pro snippets and ${conSnippets.length} Con snippets`);

    // Always create or update the comparison view if we have both Pro and Con snippets
    if (proSnippets.length > 0 && conSnippets.length > 0) {
        console.log("Stashy: Creating Pro/Con comparison view");
        createProConComparisonView();
    } else {
        console.log("Stashy: Not enough snippets for comparison view");

        // Remove any existing comparison view if we don't have both Pro and Con snippets
        const existingComparison = noteText.querySelector('.Stashy-comparison-container');
        const existingTitle = noteText.querySelector('.Stashy-comparison-title');

        if (existingComparison) {
            existingComparison.remove();
            console.log("Stashy: Removed existing comparison container");
        }

        if (existingTitle) {
            existingTitle.remove();
            console.log("Stashy: Removed existing comparison title");
        }

        // Show all Pro and Con snippets if comparison view is removed
        proSnippets.forEach(snippet => {
            snippet.style.display = '';

            // Also show the <br> element that follows the snippet
            const nextBr = snippet.nextElementSibling;
            if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
                nextBr.style.display = '';
            }
        });

        conSnippets.forEach(snippet => {
            snippet.style.display = '';

            // Also show the <br> element that follows the snippet
            const nextBr = snippet.nextElementSibling;
            if (nextBr && nextBr.tagName.toLowerCase() === 'br') {
                nextBr.style.display = '';
            }
        });
    }
}

// We no longer need to override addFormattedTextToNote since we've updated the original function
// to handle Pro/Con comparison view creation

// Also check for Pro/Con snippets when the note is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Stashy: DOM loaded, setting up Pro/Con snippet checker");

    // Wait for the note to be loaded
    setTimeout(function() {
        if (noteText) {
            console.log("Stashy: Initial check for Pro/Con snippets");
            checkForProConSnippets();
        }
    }, 1000);

    // Also check periodically for Pro/Con snippets
    setInterval(function() {
        if (noteText && noteContainer && noteContainer.classList.contains('visible')) {
            console.log("Stashy: Periodic check for Pro/Con snippets");
            checkForProConSnippets();
        }
    }, 5000); // Check every 5 seconds while the note is visible
});

// --- Dynamic Content Change Detection for Highlights ---

/**
 * Global state for dynamic highlight restoration
 */
const dynamicHighlightState = {
    observer: null,
    isObserving: false,
    lastRestoreTime: 0,
    restoreInProgress: false,
    contentChangeCount: 0,
    restorationAttempts: 0,
    maxRestorationAttempts: 5,
    observerConfig: {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: false // Don't watch attributes to reduce noise
    }
};

/**
 * Checks if a DOM change is significant enough to warrant highlight restoration
 * @param {MutationRecord[]} mutations - Array of mutation records
 * @returns {boolean} - True if changes are significant
 */
function isSignificantContentChange(mutations) {
    let significantChanges = 0;
    let textChanges = 0;
    let nodeChanges = 0;
    let highlightRelatedChanges = 0;

    for (const mutation of mutations) {
        // Skip changes to Stashy's own elements
        if (mutation.target.closest && mutation.target.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
            continue;
        }

        // Skip changes to highlight elements themselves (they might be getting applied)
        if (mutation.target.classList && mutation.target.classList.contains(HIGHLIGHT_CLASS)) {
            highlightRelatedChanges++;
            continue;
        }

        // Skip changes that are likely from our own highlight application
        if (mutation.type === 'childList') {
            const hasHighlightNodes = [...mutation.addedNodes, ...mutation.removedNodes].some(node => {
                return node.nodeType === Node.ELEMENT_NODE &&
                       node.classList && node.classList.contains(HIGHLIGHT_CLASS);
            });
            if (hasHighlightNodes) {
                highlightRelatedChanges++;
                continue;
            }
        }

        // Count different types of changes
        if (mutation.type === 'characterData') {
            // Only count text changes that are substantial
            if (mutation.target.textContent && mutation.target.textContent.trim().length > 5) {
                textChanges++;
            }
        } else if (mutation.type === 'childList') {
            // Check if nodes were added or removed
            if (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0) {
                // Filter out script, style, and other non-content nodes
                const contentNodes = [...mutation.addedNodes, ...mutation.removedNodes].filter(node => {
                    if (node.nodeType === Node.TEXT_NODE) {
                        return node.textContent.trim().length > 10; // Require more substantial text
                    } else if (node.nodeType === Node.ELEMENT_NODE) {
                        const tagName = node.tagName?.toLowerCase();
                        return tagName &&
                               !['script', 'style', 'meta', 'link', 'noscript', 'br', 'hr'].includes(tagName) &&
                               !node.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`) &&
                               !node.classList?.contains(HIGHLIGHT_CLASS);
                    }
                    return false;
                });

                if (contentNodes.length > 0) {
                    nodeChanges++;
                }
            }
        }
    }

    significantChanges = textChanges + nodeChanges;

    // Be more conservative - require more substantial changes
    const isSignificant = (significantChanges >= 3) || (textChanges >= 5) || (nodeChanges >= 2);

    if (isSignificant) {
        console.log(`Stashy: Detected significant content change (text: ${textChanges}, nodes: ${nodeChanges}, highlight-related: ${highlightRelatedChanges})`);
    }

    return isSignificant;
}

/**
 * Checks if highlights actually need restoration by comparing expected vs current
 * @returns {boolean} - True if restoration is needed
 */
function shouldRestoreHighlights() {
    if (!Array.isArray(highlightsData) || highlightsData.length === 0) {
        return false; // No highlights to restore
    }

    const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
    const existingHighlightIds = new Set();

    // Collect IDs of existing highlights (excluding Stashy UI)
    existingHighlights.forEach(mark => {
        if (!mark.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
            const id = mark.getAttribute('data-highlight-id');
            if (id) {
                existingHighlightIds.add(id);
            }
        }
    });

    // Check if any expected highlights are missing
    const missingHighlights = highlightsData.filter(data =>
        data && data.id && !existingHighlightIds.has(data.id)
    );

    const needsRestoration = missingHighlights.length > 0;

    if (needsRestoration) {
        console.log(`Stashy: Missing ${missingHighlights.length}/${highlightsData.length} highlights, restoration needed`);
    }

    return needsRestoration;
}

/**
 * Restores highlights after content changes with intelligent filtering
 * @param {boolean} force - Force restoration even if recently attempted
 */
function restoreHighlightsAfterContentChange(force = false) {
    const now = Date.now();

    // Check if restoration is actually needed (unless forced)
    if (!force && !shouldRestoreHighlights()) {
        console.log("Stashy: Skipping highlight restoration - no missing highlights detected");
        return;
    }

    // Prevent excessive restoration attempts
    if (!force && (now - dynamicHighlightState.lastRestoreTime < 2000)) {
        console.log("Stashy: Skipping highlight restoration - too recent");
        return;
    }

    // Prevent concurrent restoration
    if (dynamicHighlightState.restoreInProgress) {
        console.log("Stashy: Skipping highlight restoration - already in progress");
        return;
    }

    // Check restoration attempt limits
    if (dynamicHighlightState.restorationAttempts >= dynamicHighlightState.maxRestorationAttempts) {
        console.log("Stashy: Maximum restoration attempts reached, stopping automatic restoration");
        return;
    }

    dynamicHighlightState.restoreInProgress = true;
    dynamicHighlightState.lastRestoreTime = now;
    dynamicHighlightState.restorationAttempts++;

    console.log(`Stashy: Attempting highlight restoration (attempt ${dynamicHighlightState.restorationAttempts}/${dynamicHighlightState.maxRestorationAttempts})`);

    // Temporarily disable threat detection during restoration to prevent false positives
    const threatDetectionWasActive = window.StashyThreatDetection && window.StashyThreatDetection.isActive();
    if (threatDetectionWasActive) {
        window.StashyThreatDetection.pause();
    }

    try {
        // Get existing highlights to avoid clearing ones that are working
        const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
        const existingHighlightIds = new Set();

        existingHighlights.forEach(mark => {
            if (!mark.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
                const id = mark.getAttribute('data-highlight-id');
                if (id) {
                    existingHighlightIds.add(id);
                }
            }
        });

        // Only restore missing highlights, don't clear existing ones
        if (Array.isArray(highlightsData) && highlightsData.length > 0) {
            let restoredCount = 0;
            let failedCount = 0;
            let skippedCount = 0;
            let needsSave = false;
            const currentTime = Date.now();

            highlightsData.forEach((data) => {
                if (!data || typeof data.text !== 'string' || !data.id) {
                    failedCount++;
                    return;
                }

                // Skip if highlight already exists
                if (existingHighlightIds.has(data.id)) {
                    skippedCount++;
                    return;
                }

                // Initialize restoration status if missing
                if (!data.restorationStatus) {
                    data.restorationStatus = {
                        lastRestoreAttempt: null,
                        restorationFailures: 0,
                        isTemporarilyUnrestorable: false,
                        lastSuccessfulRestore: null
                    };
                    needsSave = true;
                }

                // Update restoration attempt timestamp
                data.restorationStatus.lastRestoreAttempt = currentTime;

                const range = deserializeRangeByContext(data);
                if (range) {
                    try {
                        const highlightStyle = data.style || 'color';
                        const highlightColor = (highlightStyle === 'color') ? (data.color || DEFAULT_HIGHLIGHT_COLOR) : null;

                        applyHighlight(range, data.id, highlightColor, highlightStyle, data.hasLinkedNote);

                        // Update restoration success status
                        data.restorationStatus.lastSuccessfulRestore = currentTime;
                        data.restorationStatus.isTemporarilyUnrestorable = false;
                        // Reset failure count on successful restoration
                        if (data.restorationStatus.restorationFailures > 0) {
                            data.restorationStatus.restorationFailures = 0;
                            needsSave = true;
                        }

                        restoredCount++;
                    } catch (error) {
                        console.warn(`Stashy: Error applying restored highlight ${data.id}:`, error);
                        // Update restoration failure status
                        data.restorationStatus.restorationFailures++;
                        data.restorationStatus.isTemporarilyUnrestorable = data.restorationStatus.restorationFailures >= 3;
                        needsSave = true;
                        failedCount++;
                    }
                } else {
                    // Update restoration failure status
                    data.restorationStatus.restorationFailures++;
                    data.restorationStatus.isTemporarilyUnrestorable = data.restorationStatus.restorationFailures >= 3;
                    needsSave = true;
                    failedCount++;
                }
            });

            // Save restoration status updates if needed
            if (needsSave) {
                saveHighlights();
            }

            console.log(`Stashy: Highlight restoration complete - restored: ${restoredCount}, failed: ${failedCount}, skipped: ${skippedCount}`);

            // Reset attempt counter if we had good success rate
            if (restoredCount > 0 && (failedCount === 0 || restoredCount / (restoredCount + failedCount) > 0.7)) {
                dynamicHighlightState.restorationAttempts = 0;
                console.log("Stashy: Reset restoration attempt counter due to successful restoration");
            }
        }

    } catch (error) {
        console.error("Stashy: Error during highlight restoration:", error);
    } finally {
        dynamicHighlightState.restoreInProgress = false;

        // Re-enable threat detection after restoration is complete
        if (threatDetectionWasActive && window.StashyThreatDetection) {
            setTimeout(() => {
                window.StashyThreatDetection.resume();
            }, 500); // Wait 500ms to ensure all highlights are applied
        }
    }
}

/**
 * Debounced version of highlight restoration to prevent excessive calls
 */
const debouncedHighlightRestoration = window.debounce ?
    window.debounce(restoreHighlightsAfterContentChange, 500) :
    restoreHighlightsAfterContentChange;

/**
 * Handles mutations detected by the content change observer
 * @param {MutationRecord[]} mutations - Array of mutation records
 */
function handleContentMutations(mutations) {
    // Filter out mutations that are not relevant
    const relevantMutations = mutations.filter(mutation => {
        // Skip mutations on Stashy elements
        if (mutation.target.closest && mutation.target.closest(`#${NOTE_ID}, .Stashy-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
            return false;
        }

        // Skip mutations on highlight elements themselves
        if (mutation.target.classList && mutation.target.classList.contains(HIGHLIGHT_CLASS)) {
            return false;
        }

        return true;
    });

    if (relevantMutations.length === 0) {
        return;
    }

    dynamicHighlightState.contentChangeCount++;

    // Check if changes are significant enough to warrant restoration
    if (isSignificantContentChange(relevantMutations)) {
        console.log(`Stashy: Content change detected (total changes: ${dynamicHighlightState.contentChangeCount}), scheduling highlight restoration`);
        debouncedHighlightRestoration();
    }
}

/**
 * Starts observing content changes for dynamic highlight restoration
 */
function startDynamicHighlightObserver() {
    if (dynamicHighlightState.isObserving || !document.body) {
        return;
    }

    try {
        // Create the mutation observer
        dynamicHighlightState.observer = new MutationObserver(handleContentMutations);

        // Start observing
        dynamicHighlightState.observer.observe(document.body, dynamicHighlightState.observerConfig);
        dynamicHighlightState.isObserving = true;

        console.log("Stashy: Started dynamic highlight restoration observer");

        // Reset counters
        dynamicHighlightState.contentChangeCount = 0;
        dynamicHighlightState.restorationAttempts = 0;

        // Set initial grace period to avoid interfering with page load
        dynamicHighlightState.lastRestoreTime = Date.now();

    } catch (error) {
        console.error("Stashy: Error starting dynamic highlight observer:", error);
    }
}

/**
 * Stops observing content changes
 */
function stopDynamicHighlightObserver() {
    if (dynamicHighlightState.observer) {
        dynamicHighlightState.observer.disconnect();
        dynamicHighlightState.observer = null;
        dynamicHighlightState.isObserving = false;
        console.log("Stashy: Stopped dynamic highlight restoration observer");
    }
}

/**
 * Resets the dynamic highlight restoration state
 */
function resetDynamicHighlightState() {
    dynamicHighlightState.lastRestoreTime = 0;
    dynamicHighlightState.restoreInProgress = false;
    dynamicHighlightState.contentChangeCount = 0;
    dynamicHighlightState.restorationAttempts = 0;
    console.log("Stashy: Reset dynamic highlight restoration state");
}

/**
 * Temporarily disables the observer during highlight operations
 * @param {Function} operation - The operation to perform while observer is disabled
 * @param {number} delay - How long to keep observer disabled after operation (ms)
 */
function withObserverDisabled(operation, delay = 1000) {
    const wasObserving = dynamicHighlightState.isObserving;

    if (wasObserving) {
        stopDynamicHighlightObserver();
    }

    try {
        operation();
    } finally {
        if (wasObserving) {
            setTimeout(() => {
                startDynamicHighlightObserver();
            }, delay);
        }
    }
}

/**
 * Enhanced version of loadAndApplyHighlights that also starts dynamic observation
 */
function loadAndApplyHighlightsEnhanced() {
    // Temporarily disable observer during initial load to prevent interference
    withObserverDisabled(() => {
        loadAndApplyHighlights();
    }, 3000); // Keep observer disabled for 3 seconds after initial load

    // Start dynamic observation after a longer delay to allow initial highlights to fully settle
    setTimeout(() => {
        startDynamicHighlightObserver();
        setupPageVisibilityHandling();
        setupManualRestorationTriggers();
        handleSPANavigation();
    }, 3000); // Increased delay to 3 seconds to avoid interfering with initial load
}

/**
 * Sets up page visibility change handling to restore highlights when page becomes visible
 */
function setupPageVisibilityHandling() {
    if (typeof document.addEventListener !== 'function') {
        return;
    }

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden && dynamicHighlightState.isObserving) {
            // Page became visible, check if highlights need restoration
            setTimeout(() => {
                const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
                const expectedHighlights = Array.isArray(highlightsData) ? highlightsData.length : 0;

                if (expectedHighlights > 0 && existingHighlights.length < expectedHighlights) {
                    console.log(`Stashy: Page visibility restored, missing highlights detected (${existingHighlights.length}/${expectedHighlights}), restoring...`);
                    restoreHighlightsAfterContentChange(true); // Force restoration
                }
            }, 500);
        }
    });

    // Handle focus events as additional trigger
    window.addEventListener('focus', () => {
        if (dynamicHighlightState.isObserving) {
            setTimeout(() => {
                const existingHighlights = document.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
                const expectedHighlights = Array.isArray(highlightsData) ? highlightsData.length : 0;

                if (expectedHighlights > 0 && existingHighlights.length === 0) {
                    console.log("Stashy: Window focus restored, no highlights found, restoring...");
                    restoreHighlightsAfterContentChange(true); // Force restoration
                }
            }, 300);
        }
    });

    console.log("Stashy: Set up page visibility and focus handling for highlight restoration");
}

/**
 * Sets up manual restoration triggers for user-initiated restoration
 */
function setupManualRestorationTriggers() {
    // Add keyboard shortcut for manual restoration (Ctrl+Shift+H)
    document.addEventListener('keydown', (event) => {
        if (event.ctrlKey && event.shiftKey && event.key === 'H') {
            event.preventDefault();
            console.log("Stashy: Manual highlight restoration triggered by keyboard shortcut");
            resetDynamicHighlightState();
            restoreHighlightsAfterContentChange(true);
        }
    });

    // Expose global function for manual restoration
    window.StashyRestoreHighlights = function() {
        console.log("Stashy: Manual highlight restoration triggered via global function");
        resetDynamicHighlightState();

        // Use observer disabling during manual restoration
        withObserverDisabled(() => {
            restoreHighlightsAfterContentChange(true);
        }, 2000); // Keep observer disabled for 2 seconds after manual restoration

        return {
            expectedHighlights: Array.isArray(highlightsData) ? highlightsData.length : 0,
            currentHighlights: document.querySelectorAll(`.${HIGHLIGHT_CLASS}`).length,
            observerActive: dynamicHighlightState.isObserving
        };
    };

    // Expose function to get highlight restoration status
    window.StashyHighlightStatus = function() {
        return {
            expectedHighlights: Array.isArray(highlightsData) ? highlightsData.length : 0,
            currentHighlights: document.querySelectorAll(`.${HIGHLIGHT_CLASS}`).length,
            observerActive: dynamicHighlightState.isObserving,
            contentChangeCount: dynamicHighlightState.contentChangeCount,
            restorationAttempts: dynamicHighlightState.restorationAttempts,
            lastRestoreTime: dynamicHighlightState.lastRestoreTime,
            restoreInProgress: dynamicHighlightState.restoreInProgress
        };
    };

    // Expose function to reset restoration status for failed highlights
    window.StashyResetRestorationStatus = function(highlightId = null) {
        if (!Array.isArray(highlightsData)) {
            return { success: false, message: 'No highlights data available' };
        }

        let resetCount = 0;
        const currentTime = Date.now();

        highlightsData.forEach(data => {
            if (!data.restorationStatus) return;

            // Reset specific highlight or all highlights
            if (!highlightId || data.id === highlightId) {
                data.restorationStatus.restorationFailures = 0;
                data.restorationStatus.isTemporarilyUnrestorable = false;
                data.restorationStatus.lastRestoreAttempt = currentTime;
                resetCount++;
            }
        });

        if (resetCount > 0) {
            saveHighlights();
            console.log(`Stashy: Reset restoration status for ${resetCount} highlight${resetCount > 1 ? 's' : ''}`);
        }

        return {
            success: true,
            message: `Reset restoration status for ${resetCount} highlight${resetCount > 1 ? 's' : ''}`,
            resetCount: resetCount
        };
    };

    // Expose function to clean up highlights that are permanently unrestorable
    window.StashyCleanupUnrestorableHighlights = function(confirmAction = false) {
        if (!Array.isArray(highlightsData)) {
            return { success: false, message: 'No highlights data available' };
        }

        const unrestorableHighlights = highlightsData.filter(data =>
            data.restorationStatus && data.restorationStatus.isTemporarilyUnrestorable
        );

        if (!confirmAction) {
            return {
                success: false,
                message: `Found ${unrestorableHighlights.length} unrestorable highlight${unrestorableHighlights.length !== 1 ? 's' : ''}. Call with confirmAction=true to remove them.`,
                count: unrestorableHighlights.length,
                highlights: unrestorableHighlights.map(h => ({ id: h.id, text: h.text.substring(0, 50) + '...' }))
            };
        }

        // Remove unrestorable highlights
        const originalLength = highlightsData.length;
        highlightsData = highlightsData.filter(data =>
            !data.restorationStatus || !data.restorationStatus.isTemporarilyUnrestorable
        );
        const removedCount = originalLength - highlightsData.length;

        if (removedCount > 0) {
            saveHighlights();
            console.log(`Stashy: Removed ${removedCount} unrestorable highlight${removedCount > 1 ? 's' : ''}`);
        }

        return {
            success: true,
            message: `Removed ${removedCount} unrestorable highlight${removedCount > 1 ? 's' : ''}`,
            removedCount: removedCount
        };
    };

    console.log("Stashy: Set up manual restoration triggers (Ctrl+Shift+H, window.StashyRestoreHighlights())");
}

/**
 * Handles cases where the page URL changes without a full reload (SPA navigation)
 */
function handleSPANavigation() {
    // Store original pushState and replaceState
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    // Override pushState
    history.pushState = function() {
        originalPushState.apply(history, arguments);
        handleURLChange();
    };

    // Override replaceState
    history.replaceState = function() {
        originalReplaceState.apply(history, arguments);
        handleURLChange();
    };

    // Handle popstate events
    window.addEventListener('popstate', handleURLChange);

    // Handle hash changes (for hash-based routing)
    window.addEventListener('hashchange', handleURLChange);

    // Handle page visibility changes (for PWA/service worker navigation)
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            // Page became visible, check if URL changed while hidden
            setTimeout(handleURLChange, 100);
        }
    });

    // Handle focus events (for multi-tab/window scenarios)
    window.addEventListener('focus', () => {
        setTimeout(handleURLChange, 100);
    });



    function handleURLChange() {
        // URL changed, stop current observer and restart with new URL context
        setTimeout(() => {
            console.log("Stashy: URL change detected, restarting highlight system");
            stopDynamicHighlightObserver();
            resetDynamicHighlightState();

            // Reload highlights for new URL if needed
            setTimeout(() => {
                if (typeof loadAndApplyHighlightsEnhanced === 'function') {
                    loadAndApplyHighlightsEnhanced();
                }
            }, 500);
        }, 100);
    }

    console.log("Stashy: Set up SPA navigation handling for highlight restoration");
}

// --- Message Handler for Dashboard Communication ---

// Listen for messages from the dashboard
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'resetRestorationStatus') {
        const result = window.StashyResetRestorationStatus(request.highlightId);
        sendResponse(result);

        // Try to restore the highlight after resetting status
        if (result.success && request.highlightId) {
            setTimeout(() => {
                restoreHighlightsAfterContentChange(true);
            }, 500);
        }

        return true; // Keep message channel open for async response
    }

    if (request.action === 'cleanupUnrestorableHighlights') {
        const result = window.StashyCleanupUnrestorableHighlights(request.confirm);
        sendResponse(result);
        return true;
    }

    if (request.action === 'getHighlightStatus') {
        const status = window.StashyHighlightStatus();
        sendResponse(status);
        return true;
    }
});

// --- End Dynamic Content Change Detection ---

console.log("Stashy: Highlighting Logic Updated (Hover Delete Button Method, +Deserialize Logging, Fuzzy Context, Ellipsis Handling, Robust Deserialize V2, Highlight with Note, Enhanced Note UI, Draggable Notes, Review and Spec Snippets, Snippet with Note, Pro/Con Comparison, Dynamic Content Change Detection)");
// --- END OF FILE content-highlighting.js ---