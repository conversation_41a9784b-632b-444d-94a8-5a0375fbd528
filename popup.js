// --- START OF FILE popup.js ---

// Listen for messages from the background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // Validate the sender is from our extension
    if (sender.id !== chrome.runtime.id) {
        console.warn("Popup: Rejected message from unauthorized sender:", sender.id);
        return false;
    }

    if (message.action === 'updateVoiceSettingsUI' && message.html) {
        const container = document.getElementById('voice-settings-container');
        if (container) {
            container.innerHTML = message.html;
            // Set up event listeners for the voice settings UI
            setupVoiceSettingsEventListeners();
        }
    } else if (message.action === 'updateSyncStatus') { // Listener moved inside DOMContentLoaded in original, keeping it there for consistency
        // Handle this inside DOMContentLoaded
    } else if (message.action === 'updateConnectionStatus') { // Listener moved inside DOMContentLoaded in original, keeping it there for consistency
         // Handle this inside DOMContentLoaded
    }
    // Return true to indicate you wish to send a response asynchronously
    // Only relevant if you actually use sendResponse, otherwise false or omit return.
    // In this case, since we don't call sendResponse, returning false or omitting is fine.
    return false;
});

// Set up event listeners for the voice settings UI
function setupVoiceSettingsEventListeners() {
    // Provider select
    const providerSelect = document.getElementById('voice-provider');
    if (providerSelect) {
        providerSelect.addEventListener('change', () => {
            const selectedProvider = providerSelect.value;
            const apiKeyContainer = document.getElementById('api-key-container');

            if (apiKeyContainer) {
                // Show/hide API key input based on provider - properly handle hidden class
                const requiresKey = ['google', 'azure', 'assembly'].includes(selectedProvider);
                if (requiresKey) {
                    apiKeyContainer.classList.remove('hidden');
                    apiKeyContainer.style.display = 'block';
                } else {
                    apiKeyContainer.style.display = 'none';
                    apiKeyContainer.classList.add('hidden');
                }
            }

            // Update language options
            updateLanguageOptions(selectedProvider);
        });
    }

    // Save button
    const saveButton = document.querySelector('#voice-settings-container .Stashy-button');
    if (saveButton) {
        saveButton.addEventListener('click', saveVoiceSettings); // Note: saveVoiceSettings defined later
    }
}

// Update language options based on selected provider
function updateLanguageOptions(provider) {
    // Try to use the enhanced voice language system first
    const langSelect = document.getElementById('voice-language-select') || document.getElementById('voice-language');
    if (!langSelect) {
        // Language select element not found
        return;
    }

    // Clear existing options
    langSelect.innerHTML = '';

    // Try to get languages from the enhanced voice module
    let languages = [];
    let languageDisplayNames = {};

    if (typeof window.getDefaultLanguagesForProvider === 'function' &&
        typeof window.getLanguageDisplayName === 'function') {
        // Use the enhanced voice module functions
        languages = window.getDefaultLanguagesForProvider(provider);
        console.log(`Popup: Using enhanced voice languages for ${provider}:`, languages.length, 'languages');
    } else {
        // Fallback to basic language sets
        console.log(`Popup: Using fallback languages for ${provider}`);
        switch (provider) {
            case 'browser':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'es-ES', 'es-MX', 'fr-FR', 'fr-CA', 'de-DE', 'de-AT', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'ar-SA', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'cs-CZ', 'hu-HU'];
                break;
            case 'google':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-ZA', 'es-ES', 'es-MX', 'es-AR', 'es-CO', 'es-CL', 'es-PE', 'es-VE', 'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'de-DE', 'de-AT', 'de-CH', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'zh-HK', 'ar-SA', 'ar-AE', 'ar-EG', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'cs-CZ', 'hu-HU'];
                break;
            case 'azure':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-ZA', 'es-ES', 'es-MX', 'es-AR', 'es-CO', 'es-CL', 'es-PE', 'es-VE', 'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'de-DE', 'de-AT', 'de-CH', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'zh-HK', 'ar-SA', 'ar-AE', 'ar-EG', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'cs-CZ', 'hu-HU'];
                break;
            case 'assembly':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'es-ES', 'es-MX', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'ar-SA', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI'];
                break;
            default:
                languages = ['en-US'];
                break;
        }

        // Basic language display names for fallback
        languageDisplayNames = {
            'en-US': 'English (United States)',
            'en-GB': 'English (United Kingdom)',
            'en-AU': 'English (Australia)',
            'en-CA': 'English (Canada)',
            'en-IN': 'English (India)',
            'es-ES': 'Spanish (Spain)',
            'es-MX': 'Spanish (Mexico)',
            'fr-FR': 'French (France)',
            'fr-CA': 'French (Canada)',
            'de-DE': 'German (Germany)',
            'de-AT': 'German (Austria)',
            'it-IT': 'Italian (Italy)',
            'pt-BR': 'Portuguese (Brazil)',
            'pt-PT': 'Portuguese (Portugal)',
            'ru-RU': 'Russian (Russia)',
            'ja-JP': 'Japanese (Japan)',
            'ko-KR': 'Korean (South Korea)',
            'zh-CN': 'Chinese (Simplified)',
            'zh-TW': 'Chinese (Traditional)',
            'ar-SA': 'Arabic (Saudi Arabia)',
            'hi-IN': 'Hindi (India)',
            'th-TH': 'Thai (Thailand)',
            'vi-VN': 'Vietnamese (Vietnam)',
            'tr-TR': 'Turkish (Turkey)',
            'pl-PL': 'Polish (Poland)',
            'nl-NL': 'Dutch (Netherlands)',
            'sv-SE': 'Swedish (Sweden)',
            'da-DK': 'Danish (Denmark)',
            'no-NO': 'Norwegian (Norway)',
            'fi-FI': 'Finnish (Finland)',
            'cs-CZ': 'Czech (Czech Republic)',
            'hu-HU': 'Hungarian (Hungary)'
        };
    }

    // Add options to select
    if (languages && languages.length > 0) {
        languages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang;

            // Use enhanced display name function if available, otherwise fallback
            if (typeof window.getLanguageDisplayName === 'function') {
                option.textContent = window.getLanguageDisplayName(lang);
            } else {
                option.textContent = languageDisplayNames[lang] || lang;
            }

            langSelect.appendChild(option);
        });

        // Added language options for provider
    } else {
        // Emergency fallback
        const option = document.createElement('option');
        option.value = 'en-US';
        option.textContent = 'English (United States)';
        langSelect.appendChild(option);
        // No languages found, using emergency fallback
    }
}

// Save voice settings (Placeholder, full definition is inside DOMContentLoaded)
function saveVoiceSettings() {
    // Implementation is within initVoiceSettings inside DOMContentLoaded
    // saveVoiceSettings called before initVoiceSettings was fully defined
}

// Initialize premium manager for popup
function initializePremiumManager() {
    if (typeof window.StashyPremium === 'undefined') {
        console.log('Popup: Loading premium manager...');
        try {
            // Load premium manager script
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('lib/premium-manager.js');
            script.onload = () => {
                console.log('Popup: Premium manager loaded successfully');
                window.StashyPremium.initialize().then(() => {
                    updatePremiumUI();
                    updateUsageCounters();
                }).catch(error => {
                    console.error('Popup: Error initializing premium manager:', error);
                });
            };
            script.onerror = () => {
                console.error('Popup: Failed to load premium manager');
            };
            document.head.appendChild(script);
        } catch (error) {
            console.error('Popup: Error loading premium manager:', error);
        }
    } else {
        console.log('Popup: Premium manager already available');
        window.StashyPremium.initialize().then(() => {
            updatePremiumUI();
            updateUsageCounters();
        }).catch(error => {
            console.error('Popup: Error initializing premium manager:', error);
        });
    }
}

// Update premium UI elements
function updatePremiumUI() {
    if (typeof window.StashyPremium === 'undefined') {
        return;
    }

    try {
        const isPremium = window.StashyPremium.isPremium();
        const trialStatus = window.StashyPremium.getTrialStatus();
        console.log('Popup: Updating premium UI, isPremium:', isPremium, 'trialStatus:', trialStatus);

        // Update premium status badge
        const statusIndicator = document.getElementById('premium-status');
        if (statusIndicator) {
            if (trialStatus.isActive) {
                statusIndicator.textContent = 'Trial';
                statusIndicator.className = 'premium-badge trial';
            } else if (isPremium) {
                statusIndicator.textContent = 'Pro';
                statusIndicator.className = 'premium-badge pro';
            } else {
                statusIndicator.textContent = 'Free';
                statusIndicator.className = 'premium-badge free';
            }
        }

        // Update trial countdown display
        const trialCountdown = document.getElementById('trial-countdown');
        const trialDaysRemaining = document.getElementById('trial-days-remaining');

        if (trialCountdown && trialDaysRemaining) {
            if (trialStatus.isActive) {
                trialCountdown.classList.remove('hidden');

                // Format remaining time
                const days = trialStatus.daysRemaining;
                const hours = trialStatus.hoursRemaining;

                if (days > 0) {
                    trialDaysRemaining.textContent = `${days} day${days !== 1 ? 's' : ''}`;
                } else if (hours > 0) {
                    trialDaysRemaining.textContent = `${hours} hour${hours !== 1 ? 's' : ''}`;
                } else {
                    trialDaysRemaining.textContent = 'Expiring soon';
                }

                // Add urgent styling if less than 2 days remaining
                if (days <= 1) {
                    trialCountdown.classList.add('urgent');
                } else {
                    trialCountdown.classList.remove('urgent');
                }
            } else {
                trialCountdown.classList.add('hidden');
            }
        }

        // Handle trial activation button
        const trialActivationContainer = document.getElementById('trial-activation-container');
        const trialButton = document.getElementById('trial-activation-button');

        if (trialActivationContainer) {
            if (trialStatus.canActivate) {
                trialActivationContainer.classList.remove('hidden');
                if (trialButton) {
                    trialButton.onclick = activateTrialFromPopup;
                }
            } else {
                trialActivationContainer.classList.add('hidden');
            }
        }

        // Update feature buttons with premium locks
        const featureButtons = document.querySelectorAll('[data-premium-feature]');
        featureButtons.forEach(button => {
            const feature = button.getAttribute('data-premium-feature');
            const isAvailable = window.StashyPremium.isFeatureAvailable(feature);

            if (!isAvailable) {
                button.classList.add('premium-locked');
                button.title = 'This feature requires Stashy Pro';

                // Only add click handler for non-export buttons (export buttons handle their own premium checks)
                if (!button.id.includes('export')) {
                    // Add click handler for upgrade prompt
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        window.StashyPremium.showUpgradePrompt(feature, 'popup');
                    });
                }
            } else {
                button.classList.remove('premium-locked');
                button.title = '';
            }
        });

    } catch (error) {
        console.error('Popup: Error updating premium UI:', error);
    }
}

// Update usage counters
function updateUsageCounters() {
    if (typeof window.StashyPremium === 'undefined') {
        return;
    }

    try {
        const usageStats = window.StashyPremium.getUsageStats();
        const trialStatus = window.StashyPremium.getTrialStatus();
        console.log('Popup: Usage stats:', usageStats, 'Trial status:', trialStatus);

        // Update notes counter
        const notesCounter = document.getElementById('notes-usage');
        if (notesCounter && usageStats.notes) {
            if (usageStats.notes.unlimited) {
                notesCounter.innerHTML = trialStatus.isActive ?
                    '<span class="trial-unlimited">Trial</span>' :
                    '<span class="premium-unlimited">∞</span>';
            } else {
                notesCounter.textContent = `${usageStats.notes.used}/${usageStats.notes.limit}`;
                if (usageStats.notes.remaining <= 2) {
                    notesCounter.classList.add('usage-warning');
                }
            }
        }

        // Update notebooks counter
        const notebooksCounter = document.getElementById('notebooks-usage');
        if (notebooksCounter && usageStats.notebooks) {
            if (usageStats.notebooks.unlimited) {
                notebooksCounter.innerHTML = trialStatus.isActive ?
                    '<span class="trial-unlimited">Trial</span>' :
                    '<span class="premium-unlimited">∞</span>';
            } else {
                notebooksCounter.textContent = `${usageStats.notebooks.used}/${usageStats.notebooks.limit}`;
                if (usageStats.notebooks.remaining <= 0) {
                    notebooksCounter.classList.add('usage-warning');
                }
            }
        }

        // Update highlights counter
        const highlightsCounter = document.getElementById('highlights-usage');
        if (highlightsCounter && usageStats.highlights) {
            if (usageStats.highlights.unlimited) {
                highlightsCounter.innerHTML = trialStatus.isActive ?
                    '<span class="trial-unlimited">Trial</span>' :
                    '<span class="premium-unlimited">∞</span>';
            } else {
                highlightsCounter.textContent = `${usageStats.highlights.used}/${usageStats.highlights.limit}`;
                if (usageStats.highlights.remaining <= 1) {
                    highlightsCounter.classList.add('usage-warning');
                }
            }
        }

    } catch (error) {
        console.error('Popup: Error updating usage counters:', error);
    }
}

// Activate trial from popup
async function activateTrialFromPopup() {
    console.log('Popup: Activating trial from popup');

    if (!window.StashyPremium) {
        console.error('Popup: Premium manager not available');
        return;
    }

    try {
        const result = await window.StashyPremium.activateTrial();

        if (result.success) {
            console.log('Popup: Trial activated successfully');

            // Update UI
            updatePremiumUI();
            updateUsageCounters();

            // Show success message
            const statusDiv = document.createElement('div');
            statusDiv.className = 'trial-success-message';
            statusDiv.textContent = '🎉 Trial activated! Enjoy 7 days of premium features.';
            statusDiv.style.cssText = `
                background: #4caf50;
                color: white;
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
                text-align: center;
                font-size: 12px;
            `;

            const container = document.querySelector('.popup-content') || document.body;
            container.insertBefore(statusDiv, container.firstChild);

            // Remove success message after 3 seconds
            setTimeout(() => {
                if (statusDiv.parentElement) {
                    statusDiv.remove();
                }
            }, 3000);

        } else {
            console.error('Popup: Trial activation failed', result);

            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'trial-error-message';
            errorDiv.textContent = result.error || 'Failed to activate trial';
            errorDiv.style.cssText = `
                background: #f44336;
                color: white;
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
                text-align: center;
                font-size: 12px;
            `;

            const container = document.querySelector('.popup-content') || document.body;
            container.insertBefore(errorDiv, container.firstChild);

            // Remove error message after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentElement) {
                    errorDiv.remove();
                }
            }, 5000);
        }
    } catch (error) {
        console.error('Popup: Error activating trial:', error);
    }
}

document.addEventListener('DOMContentLoaded', function () {

  // Initialize premium manager first
  initializePremiumManager();

  // URL utilities are no longer needed - using direct URL handling

  // Existing DOM Elements
  const clearButton = document.getElementById('clear-note');
  const searchButton = document.getElementById('search-button');
  const searchQueryInput = document.getElementById('search-query');
  const searchResultsDiv = document.getElementById('search-results');
  const viewDashboardButton = document.getElementById('view-dashboard-button');
  const statusMessage = document.getElementById('main-status-message');

  // --- Premium Status Integration ---
  /**
   * Initialize and display premium status in popup
   */
  async function initializePremiumStatus() {
    try {
      // Get premium status from background script
      const premiumStatus = await chrome.runtime.sendMessage({ action: 'getPremiumStatus' });

      if (premiumStatus) {
        updatePremiumStatusUI(premiumStatus.isPremium, premiumStatus.expiryDate);
      } else {
        updatePremiumStatusUI(false);
      }
    } catch (error) {
      console.error('Popup: Error getting premium status:', error);
      updatePremiumStatusUI(false);
    }
  }

  /**
   * Update the popup UI based on premium status
   * @param {boolean} isPremium - Whether user is premium
   * @param {string} expiryDate - Premium expiry date (if applicable)
   */
  function updatePremiumStatusUI(isPremium, expiryDate = null) {
    // Find or create premium status container
    let premiumContainer = document.getElementById('premium-status-container');
    if (!premiumContainer) {
      premiumContainer = document.createElement('div');
      premiumContainer.id = 'premium-status-container';
      premiumContainer.style.cssText = `
        padding: 8px 12px;
        margin: 8px 0;
        border-radius: 6px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      `;

      // Insert after the main status message
      const statusMessage = document.getElementById('main-status-message');
      if (statusMessage && statusMessage.parentNode) {
        statusMessage.parentNode.insertBefore(premiumContainer, statusMessage.nextSibling);
      } else {
        // Fallback: add to top of popup
        const popupContent = document.querySelector('.popup-content') || document.body;
        popupContent.insertBefore(premiumContainer, popupContent.firstChild);
      }
    }

    if (isPremium) {
      premiumContainer.style.background = 'linear-gradient(135deg, #ffd700, #ffed4e)';
      premiumContainer.style.color = '#8b5a00';
      premiumContainer.innerHTML = `
        <div style="display: flex; align-items: center; gap: 6px;">
          <span>✨</span>
          <span style="font-weight: 600;">Stashy Pro</span>
          ${expiryDate ? `<span style="opacity: 0.8; font-size: 10px;">expires ${new Date(expiryDate).toLocaleDateString()}</span>` : ''}
        </div>
        <button id="manage-subscription" style="background: rgba(139, 90, 0, 0.2); border: none; color: inherit; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer;">Manage</button>
      `;
    } else {
      premiumContainer.style.background = '#f3f4f6';
      premiumContainer.style.color = '#6b7280';
      premiumContainer.innerHTML = `
        <div style="display: flex; align-items: center; gap: 6px;">
          <span>🆓</span>
          <span>Free Plan</span>
        </div>
        <button id="upgrade-to-pro" style="background: linear-gradient(135deg, #667eea, #764ba2); border: none; color: white; padding: 4px 8px; border-radius: 4px; font-size: 10px; cursor: pointer; font-weight: 600;">Upgrade to Pro</button>
      `;
    }

    // Add event listeners for buttons
    const upgradeButton = document.getElementById('upgrade-to-pro');
    const manageButton = document.getElementById('manage-subscription');

    if (upgradeButton) {
      upgradeButton.addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://stashyapp.com/pricing.html' });
      });
    }

    if (manageButton) {
      manageButton.addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://stashyapp.com/account' });
      });
    }

    // Update body class for styling
    document.body.classList.toggle('premium-user', isPremium);
    document.body.classList.toggle('free-user', !isPremium);
  }

  // Initialize premium status
  initializePremiumStatus();

  // Check if content scripts are injected and inject if needed
  async function ensureContentScriptsInjected() {
    try {
      // Get current active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!tab || !tab.id) {
        // No active tab found
        return false;
      }

      // Check if tab is a valid web page
      if (!tab.url || (!tab.url.startsWith('http://') && !tab.url.startsWith('https://'))) {
        // Tab is not a web page, cannot inject content scripts
        showStatus('Extension only works on web pages (http/https)', 'info');
        return false;
      }

      // Check if content scripts are already injected
      try {
        const response = await chrome.tabs.sendMessage(tab.id, {
          action: 'ping',
          _sourceExtensionId: chrome.runtime.id
        });

        if (response && response.status === 'ready') {
          console.log('Popup: Content scripts already active');
          return true;
        }
      } catch (error) {
        console.log('Popup: Content scripts not yet injected, requesting injection...');
      }

      // Request background script to inject content scripts
      const injectionResponse = await chrome.runtime.sendMessage({
        action: 'injectContentScripts',
        tabId: tab.id
      });

      if (injectionResponse && injectionResponse.success) {
        console.log('Popup: Content scripts injected successfully');
        showStatus('Extension activated on this tab', 'success');

        // Wait a moment for scripts to initialize
        await new Promise(resolve => setTimeout(resolve, 500));
        return true;
      } else {
        console.error('Popup: Failed to inject content scripts:', injectionResponse?.error);
        showStatus('Failed to activate extension on this tab', 'error');
        return false;
      }
    } catch (error) {
      console.error('Popup: Error ensuring content scripts:', error);
      showStatus('Error activating extension', 'error');
      return false;
    }
  }

  // Initialize content scripts on popup open
  ensureContentScriptsInjected();

  // User Authentication Elements
  const userSignInButton = document.getElementById('user-sign-in-button');
  const userSignOutButton = document.getElementById('user-sign-out-button');
  const userSignInArea = document.getElementById('user-sign-in-area');
  const userInfoDisplay = document.getElementById('user-info-display');
  const userNameElement = document.getElementById('user-name');
  const userEmailElement = document.getElementById('user-email');
  const userAvatarImg = document.getElementById('user-avatar-img');
  const userAvatarFallback = document.getElementById('user-avatar-fallback');
  const driveAuthNotice = document.getElementById('drive-auth-notice');

  // New DOM Elements for Drive Sync
  const connectDriveButton = document.getElementById('connect-drive-button');
  const disconnectDriveButton = document.getElementById('disconnect-drive-button');
  const syncStatusIndicator = document.getElementById('sync-status-indicator');

  // Global Notes Element
  const globalNotesResultsDiv = document.getElementById('global-notes-results')?.querySelector('.results-content'); // Target the inner content div

  // *** MODIFIED/NEW EXPORT ELEMENTS ***
  const exportNotesButton = document.getElementById('export-notes'); // Renamed for clarity
  const exportHighlightsButton = document.getElementById('export-highlights');
  const commonExportFormatSelect = document.getElementById('common-export-format-select'); // *** NEW COMMON SELECT ID ***

  // Close Buttons
  const closeVoiceSettingsButton = document.getElementById('close-voice-settings');
  const closeNoteSettingsButton = document.getElementById('close-note-settings');


  // Other Settings Elements

  // Constants
  const STORAGE_KEY_PREFIX = 'Stashy_note_';
  const STATE_KEY_PREFIX = 'Stashy_state_';
  const HIGHLIGHT_KEY_PREFIX = 'Stashy_highlights_';
  const DEFAULT_HIGHLIGHT_COLOR = 'yellow'; // Should match content-state.js

  // --- User Authentication Functions ---

  /**
   * Updates the user interface based on authentication state
   * @param {boolean} isAuthenticated - Whether user is authenticated
   * @param {Object} userProfile - User profile data (if authenticated)
   */
  function updateUserAuthUI(isAuthenticated, userProfile = null) {
    if (isAuthenticated && userProfile) {
      // Show user info, hide sign-in
      userSignInArea.classList.add('hidden');
      userInfoDisplay.classList.remove('hidden');

      // Update user details
      userNameElement.textContent = userProfile.name || 'Unknown User';
      userEmailElement.textContent = userProfile.email || 'No email';

      // Handle user avatar
      if (userProfile.picture) {
        userAvatarImg.src = userProfile.picture;
        userAvatarImg.style.display = 'block';
        userAvatarFallback.style.display = 'none';
      } else {
        userAvatarImg.style.display = 'none';
        userAvatarFallback.style.display = 'flex';
      }

      // Enable Drive connection and hide auth notice
      connectDriveButton.disabled = false;
      driveAuthNotice.classList.add('hidden');

    } else {
      // Show sign-in, hide user info
      userSignInArea.classList.remove('hidden');
      userInfoDisplay.classList.add('hidden');

      // Disable Drive connection and show auth notice
      connectDriveButton.disabled = true;
      driveAuthNotice.classList.remove('hidden');
    }
  }

  /**
   * Handles user sign-in process
   */
  async function handleUserSignIn() {
    try {
      userSignInButton.disabled = true;
      userSignInButton.textContent = 'Signing In...';

      console.log('Popup: Starting user sign-in process...');
      const response = await chrome.runtime.sendMessage({ action: 'authenticateUser' });
      console.log('Popup: Received authentication response:', response);

      if (response && response.success) {
        console.log('Popup: Authentication successful, updating UI...');
        updateUserAuthUI(true, response.userProfile);
        showStatus('Successfully signed in!', 'success');
      } else {
        const errorMessage = response?.error || 'Sign-in failed - no response received';
        console.error('Popup: Authentication failed:', errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('User sign-in error:', error);

      // Provide more specific error messages based on common issues
      let userMessage = error.message;
      if (error.message.includes('OAuth')) {
        userMessage = 'OAuth authentication failed. Please check your internet connection and try again.';
      } else if (error.message.includes('token')) {
        userMessage = 'Failed to get authentication token. Please try again.';
      } else if (error.message.includes('profile')) {
        userMessage = 'Failed to get user profile. Please check your Google account permissions.';
      }

      showStatus(`Sign-in failed: ${userMessage}`, 'error');
    } finally {
      userSignInButton.disabled = false;
      userSignInButton.innerHTML = '<span class="popup-icon">👤</span> Sign In';
    }
  }

  /**
   * Handles user sign-out process
   */
  async function handleUserSignOut() {
    try {
      userSignOutButton.disabled = true;

      const response = await chrome.runtime.sendMessage({ action: 'signOutUser' });

      if (response.success) {
        updateUserAuthUI(false);
        showStatus('Successfully signed out', 'success');

        // Also disconnect Drive if connected
        const driveResult = await chrome.storage.local.get(['driveSyncEnabled']);
        if (driveResult.driveSyncEnabled) {
          chrome.runtime.sendMessage({ action: 'disconnectGoogleDrive' });
        }
      } else {
        throw new Error(response.error || 'Sign-out failed');
      }
    } catch (error) {
      console.error('User sign-out error:', error);
      showStatus(`Sign-out failed: ${error.message}`, 'error');
    } finally {
      userSignOutButton.disabled = false;
    }
  }

  /**
   * Checks and loads current user authentication state
   */
  async function loadUserAuthState() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getUserProfile' });

      // Check if response exists and has expected structure
      if (response && response.success && response.userProfile) {
        updateUserAuthUI(true, response.userProfile);
      } else {
        updateUserAuthUI(false);
      }
    } catch (error) {
      console.error('Error loading user auth state:', error);
      updateUserAuthUI(false);
    }
  }

  // --- Utility Functions ---

  /**
   * Displays a status message for a short duration.
   * @param {string} message - The message to display.
   * @param {('info'|'success'|'error')} [type='info'] - The type of message (for styling).
   */
  function showStatus(message, type = 'info') {
      if (!statusMessage) return; // Guard against missing element
      statusMessage.textContent = message;
      statusMessage.className = 'visible ' + type; // Use classes for styling
      // Clear any existing timeouts to prevent overlaps
      if (statusMessage.timeoutId) {
          clearTimeout(statusMessage.timeoutId);
      }
      if (statusMessage.resetTimeoutId) {
          clearTimeout(statusMessage.resetTimeoutId);
      }
      statusMessage.style.opacity = '1'; // Ensure visible initially

      statusMessage.timeoutId = setTimeout(() => {
          statusMessage.style.opacity = '0'; // Fade out first
          // Reset text content and class after fade out transition
          statusMessage.resetTimeoutId = setTimeout(() => {
              // Only clear if the message hasn't changed in the meantime
              if (statusMessage.textContent === message) {
                    statusMessage.textContent = '';
                    statusMessage.className = '';
              }
              statusMessage.timeoutId = null;
              statusMessage.resetTimeoutId = null;
            }, 300); // Match CSS transition duration if any
      }, 3000);
  }

  /**
   * Helper to convert basic HTML to plain text for export/search with enhanced security.
   * Handles breaks, basic tags, and images. More complex HTML might need refinement.
   * Includes security measures to prevent XSS attacks.
   * @param {string} html - The HTML string.
   * @param {string} format - The target format ('txt', 'md', or 'html').
   * @returns {string} - The plain text representation.
   */
  function getPlainText(html, format = 'txt') {
      if (!html) return '';

      try {
          // SECURITY: Sanitize the HTML before processing
          // First replace <script> tags and other potentially dangerous elements
          const sanitizedHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                                  .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
                                  .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
                                  .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
                                  .replace(/javascript:/gi, 'removed:')
                                  .replace(/on\w+(\s*)=/gi, 'data-removed$1=');

          // Replace <br> tags with newlines first for better block element handling
          const processedHtml = sanitizedHtml.replace(/<br\s*\/?>/gi, '\n');

          // Use a temporary div to let the browser parse HTML
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = processedHtml;

          // Remove any remaining script elements that might have been nested
          const scriptElements = tempDiv.querySelectorAll('script, iframe, object, embed');
          scriptElements.forEach(el => el.parentNode.removeChild(el));

          // Remove event handlers from all elements
          const allElements = tempDiv.querySelectorAll('*');
          allElements.forEach(el => {
              // Remove all attributes that start with "on" (event handlers)
              Array.from(el.attributes).forEach(attr => {
                  if (attr.name.startsWith('on') || attr.name.includes('javascript:')) {
                      el.removeAttribute(attr.name);
                  }
              });
          });

          // Note: For HTML format, we don't modify images here as we'll use the original HTML

          // Add newlines after block elements for better structure
          tempDiv.querySelectorAll('p, li, h1, h2, h3, h4, h5, h6, div, blockquote, pre').forEach(el => {
               // Check if the element itself or its last child already ends with whitespace effectively
               const content = el.textContent || '';
               // Add newline only if content exists and doesn't end with whitespace
               if (content.length > 0 && !/\s$/.test(content)) {
                   el.append('\n');
               }
          });

          // Finally, get the text content, which includes the added newlines and markers
          let text = tempDiv.textContent || tempDiv.innerText || "";

          // Optional: Collapse multiple spaces/newlines for cleaner output
          text = text.replace(/ +/g, ' '); // Collapse multiple spaces to one
          text = text.replace(/\n\s*\n/g, '\n\n'); // Collapse multiple newlines but keep double newlines for paragraphs
          text = text.trim(); // Remove leading/trailing whitespace

          return text;
      } catch (e) {
          console.error("Error in getPlainText:", e);
          return html ? String(html).replace(/<[^>]*>/g, '') : ''; // Fallback to simple tag stripping
      }


  }

  /**
   * Convert HTML content to clean PDF using jsPDF with plain text
   * @param {string} htmlContent - The complete HTML document content with styling
   * @param {string} filename - The filename for the PDF
   * @param {string} statusMessage - Status message to show during conversion
   * @returns {Promise} - Promise that resolves when PDF is generated and downloaded
   */
  async function convertStyledHtmlToPdf(htmlContent, filename, statusMessage) {
      try {
          showStatus(statusMessage, 'info');

          // Check if jsPDF is available
          if (typeof window.jspdf === 'undefined') {
              throw new Error('jsPDF library not loaded');
          }

          // Create new PDF document
          const { jsPDF } = window.jspdf;
          const pdf = new jsPDF();

          // Set up PDF properties
          const pageWidth = pdf.internal.pageSize.getWidth();
          const pageHeight = pdf.internal.pageSize.getHeight();
          const margin = 20;
          const maxWidth = pageWidth - (margin * 2);
          let currentY = margin;

          // Add title
          pdf.setFontSize(16);
          pdf.setFont(undefined, 'bold');
          pdf.text('Stashy Notes', margin, currentY);
          currentY += 15;

          // Add export date
          pdf.setFontSize(10);
          pdf.setFont(undefined, 'normal');
          pdf.text(`Exported on: ${new Date().toLocaleDateString()}`, margin, currentY);
          currentY += 20;

          // Extract content from HTML and process each note/highlight
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = htmlContent;

          // Find all note cards or content sections
          const contentSections = tempDiv.querySelectorAll('.note-card, .highlight-card, .content-section');

          if (contentSections.length === 0) {
              // If no structured content found, process as single content block
              const content = extractContentWithImages(htmlContent);
              currentY = await addContentToPdf(pdf, content.text, content.images, margin, currentY, maxWidth, pageHeight, margin);
          } else {
              // Process each content section
              for (let i = 0; i < contentSections.length; i++) {
                  const section = contentSections[i];

                  // Extract title if present
                  const titleElement = section.querySelector('.note-title, .highlight-title, h1, h2, h3');
                  if (titleElement) {
                      const title = titleElement.textContent.trim();
                      if (title) {
                          pdf.setFontSize(12);
                          pdf.setFont(undefined, 'bold');
                          currentY = await addContentToPdf(pdf, title, [], margin, currentY, maxWidth, pageHeight, margin);
                          currentY += 5;
                      }
                  }

                  // Extract metadata if present
                  const metaElement = section.querySelector('.note-meta, .highlight-meta, .meta');
                  if (metaElement) {
                      const meta = metaElement.textContent.trim();
                      if (meta) {
                          pdf.setFontSize(9);
                          pdf.setFont(undefined, 'normal');
                          currentY = await addContentToPdf(pdf, meta, [], margin, currentY, maxWidth, pageHeight, margin);
                          currentY += 5;
                      }
                  }

                  // Extract main content
                  const contentElement = section.querySelector('.note-content, .highlight-content, .content') || section;
                  const content = extractContentWithImages(contentElement.innerHTML);

                  if (content.text.trim() || content.images.length > 0) {
                      pdf.setFontSize(10);
                      pdf.setFont(undefined, 'normal');
                      currentY = await addContentToPdf(pdf, content.text, content.images, margin, currentY, maxWidth, pageHeight, margin);
                  }

                  // Add spacing between sections
                  if (i < contentSections.length - 1) {
                      currentY += 10;
                  }
              }
          }

          // Add footer
          const totalPages = pdf.internal.getNumberOfPages();
          for (let i = 1; i <= totalPages; i++) {
              pdf.setPage(i);
              pdf.setFontSize(8);
              pdf.setFont(undefined, 'normal');
              pdf.text(`Generated by Stashy - Page ${i} of ${totalPages}`, margin, pageHeight - 10);
          }

          showStatus('Downloading PDF...', 'info');

          // Download the PDF
          const pdfFilename = filename.replace('.html', '.pdf');
          pdf.save(pdfFilename);

          showStatus('PDF downloaded successfully!', 'success');

      } catch (error) {
          console.error("Error generating PDF:", error);
          showStatus('PDF generation failed.', 'error');
          throw error;
      }
  }

  /**
   * Extract content from HTML with embedded images and equation placeholders
   * @param {string} html - The HTML content
   * @returns {Object} - Object containing text and images array
   */
  function extractContentWithImages(html) {
      if (!html) return { text: '', images: [] };

      try {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = html;

          const images = [];
          const imageElements = tempDiv.querySelectorAll('img');

          // Extract images and replace with markers
          imageElements.forEach((img, index) => {
              // For video screenshots, prioritize original high-quality data for export
              const originalImageData = img.getAttribute('data-image-data');
              const src = originalImageData || img.src; // Use original if available, fallback to src
              const alt = img.alt || 'Image';
              const timestamp = img.getAttribute('data-timestamp');

              if (src && (src.startsWith('data:') || src.startsWith('http'))) {
                  images.push({
                      src: src,
                      alt: alt,
                      timestamp: timestamp,
                      index: index,
                      isVideoScreenshot: !!originalImageData // Flag to identify video screenshots
                  });

                  // Replace with a marker that we can find later
                  const marker = `[IMAGE_MARKER_${index}]`;
                  img.replaceWith(document.createTextNode(marker));
              } else {
                  // If no valid src, just use placeholder text
                  let placeholder = `[${alt}`;
                  if (timestamp) {
                      placeholder += ` at ${timestamp}`;
                  }
                  placeholder += ']';
                  img.replaceWith(document.createTextNode(placeholder));
              }
          });

          // Replace equations with LaTeX content or placeholder
          const equations = tempDiv.querySelectorAll('.Stashy-equation');
          equations.forEach(eq => {
              const latex = eq.getAttribute('data-latex');
              const placeholder = latex ? `[Equation: ${latex}]` : '[Equation]';
              eq.replaceWith(document.createTextNode(placeholder));
          });

          // Handle line breaks
          tempDiv.innerHTML = tempDiv.innerHTML.replace(/<br\s*\/?>/gi, '\n');
          tempDiv.innerHTML = tempDiv.innerHTML.replace(/<\/p>\s*<p/gi, '</p>\n<p');
          tempDiv.innerHTML = tempDiv.innerHTML.replace(/<\/div>\s*<div/gi, '</div>\n<div');

          const text = tempDiv.textContent || tempDiv.innerText || '';

          return { text, images };
      } catch (error) {
          console.error('Error extracting content:', error);
          return { text: html.replace(/<[^>]*>/g, ''), images: [] };
      }
  }

  /**
   * Add content to PDF with text and images
   * @param {object} pdf - The jsPDF instance
   * @param {string} text - The text to add
   * @param {Array} images - Array of image objects
   * @param {number} x - X position
   * @param {number} y - Y position
   * @param {number} maxWidth - Maximum width
   * @param {number} pageHeight - Page height
   * @param {number} margin - Page margin
   * @returns {number} - New Y position
   */
  async function addContentToPdf(pdf, text, images, x, y, maxWidth, pageHeight, margin) {
      if (!text) return y;

      const lineHeight = pdf.getLineHeight() / pdf.internal.scaleFactor;

      // Split text by image markers
      const parts = text.split(/(\[IMAGE_MARKER_\d+\])/);

      for (let i = 0; i < parts.length; i++) {
          const part = parts[i];

          // Check if this part is an image marker
          const imageMatch = part.match(/\[IMAGE_MARKER_(\d+)\]/);
          if (imageMatch) {
              const imageIndex = parseInt(imageMatch[1]);
              const imageData = images[imageIndex];

              if (imageData) {
                  y = await addImageToPdf(pdf, imageData, x, y, maxWidth, pageHeight, margin);
              }
          } else if (part.trim()) {
              // This is regular text
              const lines = pdf.splitTextToSize(part, maxWidth);

              for (let j = 0; j < lines.length; j++) {
                  // Check if we need a new page
                  if (y + lineHeight > pageHeight - margin) {
                      pdf.addPage();
                      y = margin;
                  }

                  pdf.text(lines[j], x, y);
                  y += lineHeight;
              }
          }
      }

      return y;
  }

  /**
   * Add image to PDF with automatic page breaks
   * @param {object} pdf - The jsPDF instance
   * @param {object} imageData - Image data object
   * @param {number} x - X position
   * @param {number} y - Y position
   * @param {number} maxWidth - Maximum width
   * @param {number} pageHeight - Page height
   * @param {number} margin - Page margin
   * @returns {number} - New Y position
   */
  async function addImageToPdf(pdf, imageData, x, y, maxWidth, pageHeight, margin) {
      try {
          // Load the image to get dimensions
          const img = new Image();

          return new Promise((resolve) => {
              img.onload = () => {
                  try {
                      // Calculate image dimensions to fit within maxWidth
                      let imgWidth = img.width;
                      let imgHeight = img.height;

                      // Scale down if too wide
                      if (imgWidth > maxWidth) {
                          const ratio = maxWidth / imgWidth;
                          imgWidth = maxWidth;
                          imgHeight = imgHeight * ratio;
                      }

                      // Convert to PDF units (assuming 72 DPI)
                      const pdfImgWidth = imgWidth * 0.75; // Convert pixels to points
                      const pdfImgHeight = imgHeight * 0.75;

                      // Check if image fits on current page
                      if (y + pdfImgHeight > pageHeight - margin) {
                          pdf.addPage();
                          y = margin;
                      }

                      // Add image to PDF
                      pdf.addImage(imageData.src, 'JPEG', x, y, pdfImgWidth, pdfImgHeight);

                      // Add caption if available
                      y += pdfImgHeight + 5;
                      if (imageData.alt && imageData.alt !== 'Image') {
                          pdf.setFontSize(8);
                          pdf.setFont(undefined, 'italic');
                          const caption = imageData.timestamp ?
                              `${imageData.alt} (${imageData.timestamp})` :
                              imageData.alt;
                          pdf.text(caption, x, y);
                          y += 10;
                          pdf.setFontSize(10);
                          pdf.setFont(undefined, 'normal');
                      } else {
                          y += 5;
                      }

                      resolve(y);
                  } catch (error) {
                      console.error('Error adding image to PDF:', error);
                      // Add placeholder text instead
                      const placeholder = `[Image: ${imageData.alt}${imageData.timestamp ? ` at ${imageData.timestamp}` : ''}]`;
                      const lines = pdf.splitTextToSize(placeholder, maxWidth);
                      const lineHeight = pdf.getLineHeight() / pdf.internal.scaleFactor;

                      for (let i = 0; i < lines.length; i++) {
                          if (y + lineHeight > pageHeight - margin) {
                              pdf.addPage();
                              y = margin;
                          }
                          pdf.text(lines[i], x, y);
                          y += lineHeight;
                      }
                      resolve(y);
                  }
              };

              img.onerror = () => {
                  console.error('Error loading image for PDF:', imageData.src);
                  // Add placeholder text instead
                  const placeholder = `[Image: ${imageData.alt}${imageData.timestamp ? ` at ${imageData.timestamp}` : ''}]`;
                  const lines = pdf.splitTextToSize(placeholder, maxWidth);
                  const lineHeight = pdf.getLineHeight() / pdf.internal.scaleFactor;

                  for (let i = 0; i < lines.length; i++) {
                      if (y + lineHeight > pageHeight - margin) {
                          pdf.addPage();
                          y = margin;
                      }
                      pdf.text(lines[i], x, y);
                      y += lineHeight;
                  }
                  resolve(y);
              };

              img.src = imageData.src;
          });
      } catch (error) {
          console.error('Error in addImageToPdf:', error);
          return y;
      }
  }





  /**
   * Process HTML content for plain PDF export, removing all formatting
   * @param {string} html - The HTML content to process
   * @param {object} pdf - The jsPDF instance
   * @param {number} x - The x position to start drawing
   * @param {number} y - The y position to start drawing
   * @param {number} maxWidth - The maximum width for text
   * @returns {number} - The new y position after drawing all content
   */
  function processPlainTextForPDF(html, pdf, x, y, maxWidth) {
      if (!html) return y;

      // Get plain text content only
      const plainText = getPlainText(html, 'txt');

      // Split text into lines that fit within the page width
      const textLines = pdf.splitTextToSize(plainText, maxWidth);

      // Set simple text style
      pdf.setFont("helvetica", "normal");
      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0); // Black text only

      // Draw the text without any formatting
      let currentY = y;
      const lineHeight = 6;

      for (let i = 0; i < textLines.length; i++) {
          const line = textLines[i];
          pdf.text(line, x, currentY);
          currentY += lineHeight;
      }

      return currentY;
  }

  /**
   * Process HTML content for PDF export, preserving formatting like highlights, bold, italic, etc.
   * @param {string} html - The HTML content to process
   * @param {object} pdf - The jsPDF instance
   * @param {number} x - The x position to start drawing
   * @param {number} y - The y position to start drawing
   * @param {number} maxWidth - The maximum width for text
   * @returns {number} - The new y position after drawing all content
   */
  function processFormattedTextForPDF(html, pdf, x, y, maxWidth) {
      if (!html) return y;

      // Create a temporary div to parse the HTML
      const tempDiv = document.createElement('div');

      // Replace HTML line breaks with newlines before setting innerHTML
      html = html.replace(/<br\s*\/?>/gi, '\n');
      html = html.replace(/<\/p>\s*<p/gi, '</p>\n<p');
      html = html.replace(/<\/div>\s*<div/gi, '</div>\n<div');
      html = html.replace(/<\/li>\s*<li/gi, '</li>\n<li');

      tempDiv.innerHTML = html;

      // Get the plain text content and preserve line breaks
      let plainText = tempDiv.textContent || tempDiv.innerText || '';

      // Ensure paragraphs have line breaks
      plainText = plainText.replace(/\n\s*\n/g, '\n\n'); // Normalize multiple line breaks

      // Create a smaller width to prevent overflow
      const adjustedMaxWidth = maxWidth * 0.9; // 90% of the original width

      // Split text into lines
      const textLines = pdf.splitTextToSize(plainText, adjustedMaxWidth);

      // Set default text style
      pdf.setFont("helvetica", "normal");
      pdf.setTextColor(0, 0, 0);

      // Draw the text
      let currentY = y;
      const lineHeight = 7;

      // First pass: Draw all text
      for (let i = 0; i < textLines.length; i++) {
          // Don't trim the line to preserve indentation
          const line = textLines[i];

          // Handle empty lines as paragraph breaks
          if (!line.trim()) {
              currentY += lineHeight;
              continue;
          }

          // Handle code blocks
          if (line.startsWith('    ') || line.startsWith('\t') || line.includes('`')) {
              // Draw code block background
              pdf.setFillColor(240, 240, 240);
              const textWidth = pdf.getStringUnitWidth(line) * pdf.getFontSize() / pdf.internal.scaleFactor;
              pdf.rect(x - 2, currentY - 4, textWidth + 4, lineHeight + 2, 'F');
              pdf.setDrawColor(200, 200, 200);
              pdf.rect(x - 2, currentY - 4, textWidth + 4, lineHeight + 2, 'S');

              // Use monospace font for code
              pdf.setFont("courier", "normal");
              pdf.text(line, x, currentY);
              pdf.setFont("helvetica", "normal");
          }
          // Handle checklist items
          else if (line.startsWith('☐') || line.startsWith('☑') ||
                  line.startsWith('□') || line.startsWith('✓')) {

              const isChecked = line.startsWith('☑') || line.startsWith('✓');

              // Draw checkbox
              pdf.setDrawColor(100, 100, 100);
              pdf.rect(x, currentY - 3, 5, 5, 'S');

              if (isChecked) {
                  // Draw checkmark
                  pdf.setDrawColor(0, 150, 0);
                  pdf.line(x + 1, currentY - 1, x + 2, currentY + 1);
                  pdf.line(x + 2, currentY + 1, x + 4, currentY - 2);
              }

              // Draw the text without the checkbox character
              const textWithoutCheckbox = line.substring(1).trim();
              pdf.text(textWithoutCheckbox, x + 8, currentY);
          }
          // Handle blockquotes
          else if (line.startsWith('>')) {
              // Draw blockquote
              pdf.setFillColor(245, 245, 245);
              const textWidth = pdf.getStringUnitWidth(line.substring(1).trim()) * pdf.getFontSize() / pdf.internal.scaleFactor;
              pdf.rect(x - 2, currentY - 4, textWidth + 4, lineHeight + 2, 'F');

              // Draw left border
              pdf.setDrawColor(180, 180, 180);
              pdf.setLineWidth(2);
              pdf.line(x - 2, currentY - 4, x - 2, currentY + 3);
              pdf.setLineWidth(0.2);

              // Draw the text without the blockquote marker
              pdf.setFont("helvetica", "italic");
              pdf.text(line.substring(1).trim(), x + 2, currentY);
              pdf.setFont("helvetica", "normal");
          }
          else {
              // Regular text
              pdf.text(line, x, currentY);
          }

          currentY += lineHeight;
      }

      // Reset Y position for second pass
      currentY = y;

      // Second pass: Apply highlights
      // Find all mark tags in the HTML
      const markRegex = /<mark[^>]*?data-color="([^"]*)"[^>]*>([\s\S]*?)<\/mark>|<mark[^>]*?class="[^"]*?color-([^"]*?)"[^>]*>([\s\S]*?)<\/mark>|<mark[^>]*>([\s\S]*?)<\/mark>/gi;

      let match;
      const highlights = [];

      // Extract all highlights
      while ((match = markRegex.exec(html)) !== null) {
          const color = match[1] || match[3] || "yellow"; // Get color from data-color or class
          const content = match[2] || match[4] || match[5]; // Get the highlighted content

          if (content) {
              // Create a temporary div to get the plain text
              const tempHighlightDiv = document.createElement('div');
              tempHighlightDiv.innerHTML = content;
              const highlightText = tempHighlightDiv.textContent || tempHighlightDiv.innerText || content;

              highlights.push({
                  text: highlightText.trim(),
                  color: color
              });
          }
      }

      // Also check for spans with Stashy-in-note-highlight class
      const spanRegex = /<[^>]*?class="[^"]*?Stashy-in-note-highlight[^"]*?color-([^"]*?)"[^>]*>([\s\S]*?)<\/[^>]*>/gi;

      while ((match = spanRegex.exec(html)) !== null) {
          const color = match[1] || "yellow";
          const content = match[2];

          if (content) {
              // Create a temporary div to get the plain text
              const tempHighlightDiv = document.createElement('div');
              tempHighlightDiv.innerHTML = content;
              const highlightText = tempHighlightDiv.textContent || tempHighlightDiv.innerText || content;

              highlights.push({
                  text: highlightText.trim(),
                  color: color
              });
          }
      }

      // Apply highlights to the text
      for (let i = 0; i < textLines.length; i++) {
          // Don't trim the line to preserve indentation and exact text matching
          const line = textLines[i];

          // Handle empty lines as paragraph breaks
          if (!line.trim()) {
              currentY += lineHeight;
              continue;
          }

          // Skip special formatting
          if (line.startsWith('    ') || line.startsWith('\t') || line.includes('`') ||
              line.startsWith('☐') || line.startsWith('☑') || line.startsWith('□') ||
              line.startsWith('✓') || line.startsWith('>')) {
              currentY += lineHeight;
              continue;
          }

          // Check if this line contains any highlighted text
          for (const highlight of highlights) {
              if (line.includes(highlight.text)) {
                  // Find the position of the highlight in the line
                  const startIndex = line.indexOf(highlight.text);
                  if (startIndex !== -1) {
                      // Calculate the width and position of the highlight
                      const beforeText = line.substring(0, startIndex);
                      const highlightWidth = pdf.getStringUnitWidth(highlight.text) * pdf.getFontSize() / pdf.internal.scaleFactor;
                      const highlightX = x + pdf.getStringUnitWidth(beforeText) * pdf.getFontSize() / pdf.internal.scaleFactor;

                      // Set the fill color based on the highlight color
                      switch (highlight.color.toLowerCase()) {
                          case "pink":
                              pdf.setFillColor(255, 192, 203); // Pink
                              break;
                          case "green":
                              pdf.setFillColor(144, 238, 144); // Light green
                              break;
                          case "blue":
                              pdf.setFillColor(173, 216, 230); // Light blue
                              break;
                          case "purple":
                              pdf.setFillColor(221, 160, 221); // Plum
                              break;
                          case "orange":
                              pdf.setFillColor(255, 215, 140); // Light orange
                              break;
                          default:
                              pdf.setFillColor(255, 255, 150); // Light yellow (default)
                              break;
                      }

                      // Draw the highlight background
                      pdf.rect(highlightX, currentY - 4, highlightWidth, lineHeight + 2, 'F');

                      // Redraw the text on top of the highlight to ensure it's visible
                      pdf.text(highlight.text, highlightX, currentY);
                  }
              }
          }

          currentY += lineHeight;
      }

      return currentY;
  }




   /**
    * Placeholder function for HTML to Markdown conversion.
    * In a real scenario, this would call the function from content-utils.js via messaging.
    * For simplicity in the popup, we might have to rely on a less accurate conversion here
    * or just export plain text for Markdown option from the popup.
    *
    * UPDATE: Since converters are in content script, we'll need messaging or
    * duplicate the logic. Let's duplicate simplified converters for popup use,
    * acknowledging potential differences from the content script version.
    */
   function convertToMarkdownPopup(htmlContent) {
       if (!htmlContent) return '';
       // Simplified converter for popup (less robust than content script version)
       let md = htmlContent;
        // Basic replacements - order matters!
        md = md.replace(/<style[^>]*>.*<\/style>/gi, ''); // Remove style blocks
        md = md.replace(/<script[^>]*>.*<\/script>/gi, ''); // Remove script blocks
        md = md.replace(/<!--.*?-->/gs, ''); // Remove comments

        // Block elements
        md = md.replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n\n');
        md = md.replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n\n');
        md = md.replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n\n');
        md = md.replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n#### $1\n\n');
        md = md.replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n##### $1\n\n');
        md = md.replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n###### $1\n\n');
        md = md.replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n\n');
        md = md.replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, (_, content) => '\n> ' + content.replace(/<br\s*\/?>/gi, '\n> ').replace(/<[^>]+>/g, '').trim() + '\n\n'); // Basic blockquote, strip inner tags
        md = md.replace(/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/gsi, '\n```\n$1\n```\n\n'); // Code blocks
        md = md.replace(/<hr[^>]*>/gi, '\n---\n\n');

        // Lists (simplified - doesn't handle nesting well)
        md = md.replace(/<ul[^>]*>(.*?)<\/ul>/gsi, (_, content) => content.replace(/<li[^>]*>(.*?)<\/li>/gi, (__, liContent) => '\n- ' + convertToMarkdownPopup(liContent).trim()).trim() + '\n\n');
        md = md.replace(/<ol[^>]*>(.*?)<\/ol>/gsi, (_, content) => {
            let itemCounter = 1;
            return content.replace(/<li[^>]*>(.*?)<\/li>/gi, (__, liContent) => `\n${itemCounter++}. ` + convertToMarkdownPopup(liContent).trim()).trim() + '\n\n';
        });

        // Inline elements
        md = md.replace(/<(strong|b)>(.*?)<\/(strong|b)>/gi, '**$2**');
        md = md.replace(/<(em|i)>(.*?)<\/(em|i)>/gi, '*$2*');
        md = md.replace(/<(del|s)>(.*?)<\/(del|s)>/gi, '~~$2~~');
        md = md.replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`');
        md = md.replace(/<mark[^>]*>(.*?)<\/mark>/gi, '==$1=='); // Highlight
        md = md.replace(/<a[^>]*href=["'](.*?)["'][^>]*>(.*?)<\/a>/gi, '[$2]($1)'); // Links
        md = md.replace(/<img[^>]*src=["'](.*?)["'][^>]*alt=["'](.*?)["'][^>]*>/gi, '![$2]($1)'); // Images

        // Cleanup <br> and other tags
        md = md.replace(/<br\s*\/?>/gi, '  \n'); // Line breaks
        md = md.replace(/<[^>]+>/g, ''); // Remove remaining HTML tags

        // Decode entities and clean up whitespace
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = md;
        md = tempDiv.textContent || tempDiv.innerText || "";
        md = md.replace(/(\n\s*){3,}/g, '\n\n').trim(); // Collapse blank lines
        return md;
   }

   /** Placeholder for basic HTML export wrapper (similar to content-utils) */
   function convertToBasicHTMLPopup(htmlContent, pageTitle = 'Stashy Note') {
        const basicCSS = `body { font-family: sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: auto; } h1, h2, h3 { margin-top: 1.5em; margin-bottom: 0.5em; } blockquote { border-left: 4px solid #ccc; padding-left: 1em; margin-left: 0; color: #555; font-style: italic; } pre { background-color: #f4f4f4; border: 1px solid #ddd; padding: 10px; border-radius: 4px; overflow-x: auto; } code { font-family: monospace; } mark { background-color: yellow; padding: 0.1em 0.2em; } img { max-width: 100%; height: auto; display: block; margin: 10px 0; } table { border-collapse: collapse; width: 100%; margin-bottom: 1em; } th, td { border: 1px solid #ccc; padding: 8px; text-align: left; } th { background-color: #f2f2f2; } .Stashy-equation { border: 1px dashed #ccc; padding: 2px 5px; display: inline-block; }`;
        return `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>${pageTitle.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</title><style>${basicCSS}</style></head><body>${htmlContent}</body></html>`;
   }

   /**
    * Parses the raw search query into filters and general query terms.
    * @param {string} rawQuery - The user's input query.
    * @returns {object} Object with { filters: { tag: [], url: [], color: [], reminder: null/true/false, date: null/string }, generalQuery: "..." }
    */
   function parseQuery(rawQuery) {
       const filters = {
           tag: [],
           url: [],
           color: [],
           reminder: null, // null = don't filter, true = has reminder, false = no reminder
           date: null // Store raw date filter string (e.g., ">2023-10-26")
       };
       let generalQueryParts = [];
       // Regex to find key:value pairs, handling quotes
       const operatorRegex = /(\w+):(?:"([^"]+)"|([^\s"]+))/g;
       let lastIndex = 0;
       let match;

       while ((match = operatorRegex.exec(rawQuery)) !== null) {
           // Add text between operators (or start) to general query
           const precedingText = rawQuery.substring(lastIndex, match.index).trim();
           if (precedingText) {
               generalQueryParts.push(precedingText);
           }

           const key = match[1].toLowerCase();
           const value = match[2] || match[3]; // match[2] is quoted, match[3] is non-quoted

           switch (key) {
               case 'tag':
               case 'tags':
                   filters.tag.push(value.toLowerCase());
                   break;
               case 'url':
                   filters.url.push(value.toLowerCase());
                   break;
               case 'color':
                   filters.color.push(value.toLowerCase());
                   break;
               case 'reminder':
                   if (value.toLowerCase() === 'true' || value.toLowerCase() === 'yes' || value.toLowerCase() === 'any') {
                       filters.reminder = true;
                   } else if (value.toLowerCase() === 'false' || value.toLowerCase() === 'no' || value.toLowerCase() === 'none') {
                       filters.reminder = false;
                   }
                   break;
               case 'date': // Handles >, <, or specific date
                   filters.date = value;
                   break;
           }
           lastIndex = operatorRegex.lastIndex;
       }

       // Add any remaining text after the last operator
       const remainingText = rawQuery.substring(lastIndex).trim();
       if (remainingText) {
           generalQueryParts.push(remainingText);
       }

       return {
           filters: filters,
           generalQuery: generalQueryParts.join(' ').trim().toLowerCase() // Lowercase general query for matching
       };
   }

   /**
    * Checks if a note's date matches the date filter string.
    * @param {Date|null} noteDate - The Date object from the note's lastSaved.
    * @param {string|null} dateFilter - The filter string (e.g., ">2023-10-26", "<2023-10-27", "2023-10-26").
    * @returns {boolean} True if the note matches the filter, false otherwise.
    */
   function matchDateFilter(noteDate, dateFilter) {
      if (!dateFilter || !noteDate || isNaN(noteDate.getTime())) return true; // No filter or invalid note date -> pass

      const noteTimestamp = noteDate.getTime();
      let filterTimestamp;
      let comparisonType = 'exact'; // 'exact', 'after', 'before', 'after_inclusive', 'before_inclusive'

      // Check for operators with inclusive first
      if (dateFilter.startsWith('>=')) {
          comparisonType = 'after_inclusive';
          dateFilter = dateFilter.substring(2);
      } else if (dateFilter.startsWith('<=')) {
          comparisonType = 'before_inclusive';
          dateFilter = dateFilter.substring(2);
      } else if (dateFilter.startsWith('>')) {
          comparisonType = 'after';
          dateFilter = dateFilter.substring(1);
      } else if (dateFilter.startsWith('<')) {
          comparisonType = 'before';
          dateFilter = dateFilter.substring(1);
      }

      try {
          // Try parsing as YYYY-MM-DD. Add time parts for comparisons.
          const datePart = dateFilter.split('T')[0]; // Handle potential time part, just use date
          // Create date in UTC to avoid timezone issues with comparisons
          const filterDate = new Date(datePart + 'T00:00:00Z');
          if (isNaN(filterDate.getTime())) return true; // Invalid filter date -> pass

          filterTimestamp = filterDate.getTime();
          // Use UTC day boundaries for comparison
          const dayInMillis = 24 * 60 * 60 * 1000;
          const endOfDayTimestamp = filterTimestamp + dayInMillis - 1;

          switch (comparisonType) {
              case 'exact':
                  // Match anywhere within that UTC day
                  return noteTimestamp >= filterTimestamp && noteTimestamp <= endOfDayTimestamp;
              case 'after':
                  // Strictly after the *end* of the specified UTC day
                  return noteTimestamp > endOfDayTimestamp;
               case 'after_inclusive':
                  // On or after the *start* of the specified UTC day
                  return noteTimestamp >= filterTimestamp;
              case 'before':
                  // Strictly before the *start* of the specified UTC day
                  return noteTimestamp < filterTimestamp;
               case 'before_inclusive':
                   // On or before the *end* of the specified UTC day
                  return noteTimestamp <= endOfDayTimestamp;
              default: return true; // Should not happen
          }
      } catch (e) {
          console.warn("Date filter parsing error:", e);
          return true; // Invalid filter -> pass
      }
   }

  /**
   * Creates a DOM element for a single search result (for the popup quick search).
   * @param {string} key - The storage key for the note.
   * @param {object} note - The note data object.
   * @param {string} query - The search query for highlighting (should be the general query part).
   * @returns {HTMLElement|null} - The result item div, or null if key is invalid.
   */
  function createSearchResultElement(key, note, query) {
       const keyPrefix = STORAGE_KEY_PREFIX;
       if (!key.startsWith(keyPrefix)) return null;

       // Extract URL and note index carefully
       const keySuffix = key.substring(keyPrefix.length);
       const lastNoteMarker = keySuffix.lastIndexOf('_note');
       if (lastNoteMarker === -1 || lastNoteMarker === 0) {
           console.warn("Could not parse key format:", key);
           return null; // Invalid format
       }

       let url;
       try {
            url = decodeURIComponent(keySuffix.substring(0, lastNoteMarker));
       } catch (e) {
            console.warn("Could not decode URL from key:", keySuffix.substring(0, lastNoteMarker), e);
            url = keySuffix.substring(0, lastNoteMarker); // Use raw if decode fails
       }
       const noteIndexStr = keySuffix.substring(lastNoteMarker + 5);
       const noteIndex = parseInt(noteIndexStr, 10);
       if (isNaN(noteIndex) || !url) {
            console.warn("Could not parse URL or index from key:", key);
            return null; // Invalid format
       }

       // Get plain text snippet and safely highlight matches based on the general query
       const plainText = getPlainText(note.text || '');

       // Enhanced safe highlighting using the new helper function
       let displaySnippet = '';
       try {
           // Use the new safe highlighting function
           displaySnippet = safeHighlightSearchTerms(plainText, query);
       } catch (e) {
           console.warn("Error during safe highlighting:", e);
           // Fallback to escaped text
           displaySnippet = escapeHtml(plainText);
       }

       // Limit snippet length for popup view
       const truncatedSnippet = displaySnippet.substring(0, 150) + (plainText.length > 150 ? '...' : '');

       // --- Create result element ---
       const resultDiv = document.createElement('div');
       resultDiv.className = 'result-item';

       const urlLink = document.createElement('a');
       urlLink.className = 'result-url';
       urlLink.href = '#'; // Prevent default navigation

       // Add favicon if available
       if (window.StashyFaviconUtils && url) {
           const favicon = window.StashyFaviconUtils.getFaviconElementSync(url);
           urlLink.appendChild(favicon);
       }

       // Add URL text
       const urlText = document.createElement('span');
       urlText.className = 'result-url-text';
       urlText.textContent = url;
       urlLink.appendChild(urlText);

       urlLink.title = `Click to open Note ${noteIndex} on this page (opens or switches to tab)`;
       urlLink.addEventListener('click', (e) => {
           e.preventDefault();
           openNoteInTab(url, noteIndex); // Use the helper function
       });

       const detailsSpan = document.createElement('span');
       detailsSpan.className = 'result-details';
       let detailsText = `Note ${noteIndex}`;
       if (note.globallyPinned) detailsText += ' ⭐'; // Add star icon if important note
       if (note.color) detailsText += ` | ${note.color.charAt(0).toUpperCase() + note.color.slice(1)}`;
       if (note.tags && note.tags.length > 0) detailsText += ` | Tags: ${note.tags.join(', ')}`;
       if (note.reminder) {
          try { detailsText += ` | Reminder: ${new Date(note.reminder).toLocaleDateString()}` } catch(e) {}
      }
      if (note.lastSaved) {
          // Ensure lastSaved is valid before formatting
          const savedDate = new Date(note.lastSaved);
          if (!isNaN(savedDate.getTime())) {
              try { detailsText += ` | Saved: ${savedDate.toLocaleDateString()}`; } catch (e) {}
          }
      }
       detailsSpan.textContent = detailsText;

       const snippetP = document.createElement('p');
       snippetP.className = 'result-snippet';
       snippetP.innerHTML = truncatedSnippet || '[Empty Note]'; // Use innerHTML for <mark> but with safe content

       resultDiv.appendChild(urlLink);
       resultDiv.appendChild(detailsSpan);
       resultDiv.appendChild(snippetP);

      return resultDiv;
  }

  // URL fixing functions removed - using direct URL handling

  /**
   * Opens the specific note's URL in a tab, focusing if exists, creating if not.
   * @param {string} url - The raw URL associated with the note.
   * @param {number} noteIndex - The index of the note (optional, for potential future use).
   */
  function openNoteInTab(url, noteIndex) {
       console.log(`--- openNoteInTab ---`);
       console.log(`Request to open URL: ${url} for Note ${noteIndex}`);

       // With the new storage approach, URLs are stored in raw format
       // so we can use them directly without complex processing
       let targetUrl = url;

       // Basic validation - ensure URL has a protocol
       if (!targetUrl.includes('://')) {
           targetUrl = 'http://' + targetUrl;
           console.log(`Added http:// prefix to URL: ${targetUrl}`);
       }

       console.log(`Using URL: ${targetUrl}`);
       chrome.tabs.query({ url: targetUrl }, (existingTabs) => {
           console.log(`Querying for existing tabs with URL: ${targetUrl}`);
           if (chrome.runtime.lastError) {
               console.error("Error during tabs.query:", chrome.runtime.lastError.message);
               showStatus(`Error finding tab: ${chrome.runtime.lastError.message}. Trying to create...`, 'error');
               chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                  if(chrome.runtime.lastError || !newTab) {
                      console.error("Fallback tab creation failed:", chrome.runtime.lastError?.message || "No new tab info");
                      showStatus(`Failed to open URL: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');

                      // Try a different approach as a last resort
                      try {
                          // Try to open in current window
                          window.open(targetUrl, '_blank');
                      } catch (windowErr) {
                          console.error("Window.open fallback also failed:", windowErr);
                      }
                  }
               });
               return;
           }
           console.log(`Found ${existingTabs ? existingTabs.length : 0} existing tabs.`);
           if (existingTabs && existingTabs.length > 0) {
               const targetTab = existingTabs[0];
               console.log(`Found existing tab: ${targetTab.id}. Attempting to update and focus.`);
               chrome.tabs.update(targetTab.id, { active: true }, (updatedTab) => {
                   if (chrome.runtime.lastError || !updatedTab) {
                       console.error(`Error updating tab ${targetTab.id}:`, chrome.runtime.lastError?.message || "No updated tab info received");
                       showStatus(`Error switching to tab. Trying to create...`, 'error');
                       chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                           if(chrome.runtime.lastError || !newTab) {
                               console.error("Fallback tab creation failed:", chrome.runtime.lastError?.message || "No new tab info");
                               showStatus(`Failed to open URL: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');

                               // Try a different approach as a last resort
                               try {
                                   // Try to open in current window
                                   window.open(targetUrl, '_blank');
                               } catch (windowErr) {
                                   console.error("Window.open fallback also failed:", windowErr);
                               }
                           }
                       }); // Fallback create
                   } else {
                       console.log(`Tab ${updatedTab.id} activated. Focusing window ${updatedTab.windowId}.`);
                       chrome.windows.update(updatedTab.windowId, { focused: true }, () => {
                           if (chrome.runtime.lastError) console.warn(`Could not focus window ${updatedTab.windowId}:`, chrome.runtime.lastError.message);
                           else console.log(`Window ${updatedTab.windowId} focused.`);
                       });
                   }
               });
           } else {
               console.log(`No existing tabs found. Creating new tab for: ${targetUrl}`);
               chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                   if (chrome.runtime.lastError || !newTab) {
                       console.error("New tab creation failed:", chrome.runtime.lastError?.message || "No new tab info received");
                       showStatus(`Failed to create tab: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');

                       // Try a different approach as a last resort
                       try {
                           // Try to open in current window
                           window.open(targetUrl, '_blank');
                       } catch (windowErr) {
                           console.error("Window.open fallback also failed:", windowErr);

                           // Final fallback - try to copy URL to clipboard
                           try {
                               navigator.clipboard.writeText(targetUrl).then(() => {
                                   showStatus('URL copied to clipboard. Please paste in a new tab.', 'info');
                               }).catch(err => {
                                   console.error("Clipboard copy failed:", err);
                               });
                           } catch (clipboardErr) {
                               console.error("Clipboard API not available:", clipboardErr);
                           }
                       }
                   } else {
                       console.log("New tab created successfully:", newTab.id);
                   }
               });
           }
       });
   }

   /**
    * Renders the important notes into their dedicated section.
    * @param {Array} allNotes - Array of all note objects {key, data}.
    */
   function renderGlobalNotes(allNotes) {
       if (!globalNotesResultsDiv) return; // Guard if element doesn't exist

       globalNotesResultsDiv.innerHTML = ''; // Clear previous important notes
       let globalNotesFragment = document.createDocumentFragment();
       let globalCount = 0;

       // Sort important notes by lastSaved descending before rendering
       const sortedGlobalNotes = allNotes
           .filter(({ data }) => data.globallyPinned === true)
           .sort((a, b) => {
               const timeA = a.data.lastSaved ? new Date(a.data.lastSaved).getTime() : 0;
               const timeB = b.data.lastSaved ? new Date(b.data.lastSaved).getTime() : 0;
               return timeB - timeA; // Descending order
           });

       sortedGlobalNotes.forEach(({ key, data }) => {
           // Pass empty query string "" for highlighting as we don't highlight important notes based on search
           const resultElement = createSearchResultElement(key, data, "");
           if (resultElement) {
               globalNotesFragment.appendChild(resultElement);
               globalCount++;
           }
       });

       if (globalCount === 0) {
           globalNotesResultsDiv.innerHTML = '<p class="no-results">No important notes found.</p>';
       } else {
           globalNotesResultsDiv.appendChild(globalNotesFragment);


       }
   }

  /**
   * Helper function to match date filters
   * @param {Date|null} noteDate - The note's date
   * @param {string} dateFilter - The date filter string (e.g., ">2023-10-26", "<2023-12-01")
   * @returns {boolean} - Whether the note matches the date filter
   */
  function matchDateFilter(noteDate, dateFilter) {
      if (!noteDate || !dateFilter) return false;

      try {
          const now = new Date();

          if (dateFilter.startsWith('>')) {
              const filterDate = new Date(dateFilter.substring(1));
              return noteDate > filterDate;
          } else if (dateFilter.startsWith('<')) {
              const filterDate = new Date(dateFilter.substring(1));
              return noteDate < filterDate;
          } else if (dateFilter.startsWith('=')) {
              const filterDate = new Date(dateFilter.substring(1));
              // Same day comparison
              return noteDate.toDateString() === filterDate.toDateString();
          } else {
              // Default to exact match
              const filterDate = new Date(dateFilter);
              return noteDate.toDateString() === filterDate.toDateString();
          }
      } catch (error) {
          console.warn("Invalid date filter:", dateFilter, error);
          return false;
      }
  }

  /**
   * Safely highlight search terms in text to prevent XSS
   * @param {string} text - The text to highlight
   * @param {string} query - The search query
   * @returns {string} - HTML with highlighted terms
   */
  function safeHighlightSearchTerms(text, query) {
      if (!text || !query) return escapeHtml(text);

      try {
          // First escape the text to prevent XSS
          const escapedText = escapeHtml(text);

          // Escape special regex characters in the query
          const escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');

          // Create regex for case-insensitive matching
          const regex = new RegExp(`(${escapedQuery})`, 'gi');

          // Highlight matches with <mark> tags
          return escapedText.replace(regex, '<mark>$1</mark>');
      } catch (error) {
          console.warn("Error highlighting search terms:", error);
          return escapeHtml(text); // Fallback to escaped text
      }
  }

  /**
   * Escape HTML to prevent XSS
   * @param {string} text - Text to escape
   * @returns {string} - Escaped HTML
   */
  function escapeHtml(text) {
      const map = {
          '&': '&amp;',
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#039;'
      };
      return String(text || '').replace(/[&<>"']/g, function(m) { return map[m]; });
  }

  // --- Search Logic ---
  /**
   * Handles the 'Search' button click or Enter key press (for popup quick search).
   * Incorporates advanced search operators. Uses chrome.storage.local.
   * Fetches ALL notes first to render global notes separately.
   */
   function performSearch() {
      // Enhanced guard elements check with detailed logging
      if (!searchQueryInput) {
          console.error("Stashy Popup: Search input element not found");
          return;
      }
      if (!searchResultsDiv) {
          console.error("Stashy Popup: Search results div not found");
          return;
      }
      if (!searchButton) {
          console.error("Stashy Popup: Search button not found");
          return;
      }

      const rawQuery = searchQueryInput.value.trim();

      // Early exit for empty queries
      if (!rawQuery) {
          searchResultsDiv.innerHTML = '<p class="no-results">Enter text or use operators (tag:, url:, date:, color:, reminder:true/false) to search.</p>';
          return;
      }

      const { filters, generalQuery } = parseQuery(rawQuery);

      // Show searching indicator
      searchResultsDiv.innerHTML = '<p class="no-results">Searching...</p>';

      // Disable inputs during search to prevent multiple simultaneous searches
      searchButton.disabled = true;
      searchQueryInput.disabled = true;

      console.log(`Performing popup search for: Filters=`, filters, `General Query="${generalQuery}"`);
      showStatus('Searching all notes...', 'info');

      chrome.storage.local.get(null, function (items) { // <-- Use local storage
           searchButton.disabled = false; // Re-enable buttons regardless of outcome
           searchQueryInput.disabled = false;

           if (chrome.runtime.lastError) {
              console.error("Stashy Popup Error getting all local storage:", chrome.runtime.lastError.message);
              showStatus(`Error accessing storage: ${chrome.runtime.lastError.message}`, 'error');
              searchResultsDiv.innerHTML = '<p class="no-results">Error loading notes.</p>';
              return;
           }

           const allNotes = Object.entries(items)
               .filter(([key, value]) => key.startsWith(STORAGE_KEY_PREFIX) && typeof value === 'object' && value !== null)
               .map(([key, data]) => ({ key, data }));

           // Global notes are already rendered and are not affected by search.

           // Filter for regular search results, excluding global notes
           let searchMatchCount = 0;
           let searchResultsFragment = document.createDocumentFragment();

           // Sort notes by last saved date descending before filtering/searching
           allNotes.sort((a, b) => {
              const timeA = a.data.lastSaved ? new Date(a.data.lastSaved).getTime() : 0;
              const timeB = b.data.lastSaved ? new Date(b.data.lastSaved).getTime() : 0;
              return timeB - timeA; // Descending
           });

           allNotes.forEach(({ key, data }) => {
                // Include all notes in search results (including globally pinned notes)
                // This ensures consistency with dashboard search behavior

                // Apply Filters logic
                let passesFilters = true;
                // URL Filter
                if (filters.url.length > 0) {
                     let urlPart = '';
                     const keyPrefix = STORAGE_KEY_PREFIX;
                     if (key.startsWith(keyPrefix)) {
                         const keySuffix = key.substring(keyPrefix.length);
                         const lastNoteMarker = keySuffix.lastIndexOf('_note');
                         if (lastNoteMarker > 0) {
                             // With the new storage approach, URLs are stored in raw format
                             // so we can use them directly without decoding
                             urlPart = keySuffix.substring(0, lastNoteMarker).toLowerCase();
                         }
                     }
                     if (!filters.url.every(filterUrl => urlPart.includes(filterUrl))) {
                         passesFilters = false;
                     }
                }
                // Tag Filter
                if (passesFilters && filters.tag.length > 0) {
                    const noteTagsLower = data.tags ? data.tags.map(tag => String(tag).toLowerCase()) : [];
                    if (!filters.tag.every(filterTag => noteTagsLower.some(noteTag => noteTag.includes(filterTag)))) {
                        passesFilters = false;
                    }
                }
                 // Color Filter
                 if (passesFilters && filters.color.length > 0) {
                     const noteColorLower = (data.color || '').toLowerCase();
                     if (!filters.color.every(filterColor => noteColorLower.includes(filterColor))) {
                         passesFilters = false;
                     }
                 }
                 // Reminder Filter - Enhanced logic
                if (passesFilters && filters.reminder !== null) {
                     // Check for reminder in multiple possible formats
                     const hasReminder = !!(
                         (data.reminder && (
                             (typeof data.reminder === 'object' && data.reminder.enabled) ||
                             (typeof data.reminder === 'number' && data.reminder > 0) ||
                             (typeof data.reminder === 'string' && data.reminder.length > 0) ||
                             (typeof data.reminder === 'boolean' && data.reminder)
                         )) ||
                         data.hasReminder ||
                         data.reminderEnabled
                     );
                     if (filters.reminder !== hasReminder) {
                         passesFilters = false;
                     }
                 }
                 // Date Filter
                if (passesFilters && filters.date) {
                    const noteDate = data.lastSaved ? new Date(data.lastSaved) : null;
                    if (!matchDateFilter(noteDate, filters.date)) {
                        passesFilters = false;
                    }
                }

                // Apply General Query logic
                let matchesGeneralQuery = false;
                if (passesFilters) {
                    if (!generalQuery) {
                        matchesGeneralQuery = true; // Pass if no general query and filters passed
                    } else {
                        // Enhanced text extraction - consistent with dashboard logic
                        let plainTextLower = '';

                        // Try multiple data sources to match dashboard behavior
                        const textContent = data.text || data.content || '';
                        if (textContent) {
                            try {
                                // Use the same getPlainText function as dashboard for consistency
                                plainTextLower = (typeof getPlainText === 'function' ? getPlainText(textContent) : textContent).toLowerCase();
                            } catch (e) {
                                console.warn("Error extracting plain text:", e);
                                // Fallback to simple text extraction
                                if (data.content) {
                                    const tempDiv = document.createElement('div');
                                    tempDiv.innerHTML = data.content;
                                    plainTextLower = (tempDiv.textContent || tempDiv.innerText || '').toLowerCase();
                                } else {
                                    plainTextLower = String(textContent).toLowerCase();
                                }
                            }
                        }
                        const tagsLower = data.tags ? data.tags.map(tag => String(tag).toLowerCase()) : [];
                        let urlPartLower = '';
                        const keyPrefix = STORAGE_KEY_PREFIX;
                        if (key.startsWith(keyPrefix)) {
                            const keySuffix = key.substring(keyPrefix.length);
                            const lastNoteMarker = keySuffix.lastIndexOf('_note');
                            if (lastNoteMarker > 0) {
                                // With the new storage approach, URLs are stored in raw format
                                // so we can use them directly without decoding
                                urlPartLower = keySuffix.substring(0, lastNoteMarker).toLowerCase();
                            }
                        }
                        // Match general query against text, tags, URL, and title (consistent with dashboard)
                        const textMatch = plainTextLower.includes(generalQuery);
                        const tagMatch = tagsLower.some(tag => tag.includes(generalQuery));
                        const urlMatch = urlPartLower.includes(generalQuery);
                        const titleMatch = (data.title || '').toLowerCase().includes(generalQuery);
                        matchesGeneralQuery = textMatch || tagMatch || urlMatch || titleMatch;
                    }
                }

                // Add to search results if all criteria met
                if (passesFilters && matchesGeneralQuery) {
                    // Pass generalQuery for highlighting
                    const resultElement = createSearchResultElement(key, data, generalQuery);
                    if (resultElement) {
                        searchResultsFragment.appendChild(resultElement);
                        searchMatchCount++;
                    }
                }
            });

           // Update search results DOM
           searchResultsDiv.innerHTML = ''; // Clear "Searching..." message
           if (searchMatchCount === 0) {
               searchResultsDiv.innerHTML = '<p class="no-results">No matching notes found for your query.</p>';
           } else {
               searchResultsDiv.appendChild(searchResultsFragment);


           }
           if (searchMatchCount === 0 && rawQuery) {
               console.log(`Popup Search: No results found for query: "${rawQuery}"`);
           }
           showStatus(`Search complete: ${searchMatchCount} result(s) found.`, 'success');
           searchQueryInput.focus();
           // searchQueryInput.select(); // Select sometimes feels jarring, focus might be enough
      });
  }


  // --- Google Drive Sync UI Logic ---
  /**
   * Updates the visibility of Drive Connect/Disconnect buttons and the indicator's
   * base connected/disconnected state and color.
   * @param {boolean} isConnected - Whether the extension believes it's connected.
   */
  function updateSyncButtonUI(isConnected) {
      if (!connectDriveButton || !disconnectDriveButton || !syncStatusIndicator) return;

      if (isConnected) {
          connectDriveButton.style.display = 'none';
          disconnectDriveButton.style.display = 'inline-flex';
          // Set default connected state (green) - remove others first for safety
          syncStatusIndicator.className = 'sync-indicator connected'; // Use base + state class
          syncStatusIndicator.title = "Google Drive Connected";

      } else {
          connectDriveButton.style.display = 'inline-flex';
          disconnectDriveButton.style.display = 'none';
          // Set disconnected state (grey/red)
          syncStatusIndicator.className = 'sync-indicator disconnected'; // Use base + state class
          syncStatusIndicator.title = "Google Drive Disconnected";
      }
  }

   /**
    * Exports highlights from the current page based on the common format selection.
    */
   function exportPageHighlights() {
       if (!exportHighlightsButton || !commonExportFormatSelect) return; // Guard elements

       // --- Premium Check for Export Features ---
       if (typeof window.StashyPremium !== 'undefined') {
           // Check if user has access to export features
           if (!window.StashyPremium.isFeatureAvailable('export_pdf') &&
               !window.StashyPremium.isFeatureAvailable('export_word') &&
               !window.StashyPremium.isFeatureAvailable('export_html')) {
               console.log('Stashy: Highlights export blocked - premium feature');
               showStatus('Export features require Stashy Pro. Upgrade to unlock PDF, Word, and HTML exports!', 'warning');

               // Show upgrade prompt
               if (window.StashyPremium.showUpgradePrompt) {
                   window.StashyPremium.showUpgradePrompt('Export Features', 'popup-export-highlights');
               }
               return;
           }
       }

       exportHighlightsButton.disabled = true;
       const selectedFormat = commonExportFormatSelect.value; // *** READ FROM COMMON SELECT ***
       showStatus(`Exporting highlights (${selectedFormat.toUpperCase()})...`, 'info');

       chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
           if (chrome.runtime.lastError || !tabs || tabs.length === 0 || !tabs[0].url || !tabs[0].id) {
               showStatus('Could not get current page details.', 'error');
               exportHighlightsButton.disabled = false;
               return;
           }
           const url = tabs[0].url;
           // Allow http and https protocols
           if (!url || (!url.startsWith('http:') && !url.startsWith('https:'))) {
               showStatus('Cannot export highlights from this type of page.', 'error');
               exportHighlightsButton.disabled = false;
               return;
           }

           const highlightKey = `${HIGHLIGHT_KEY_PREFIX}${url}`;

           chrome.storage.local.get([highlightKey], async function (result) {
               if (chrome.runtime.lastError) {
                   showStatus(`Error getting highlights: ${chrome.runtime.lastError.message}`, 'error');
                   exportHighlightsButton.disabled = false;
                   return;
               }

               const highlights = result[highlightKey];

               if (!highlights || !Array.isArray(highlights) || highlights.length === 0) {
                   showStatus('No highlights found on this page to export.', 'info');
                   exportHighlightsButton.disabled = false;
                   return;
               }

               // Generate filename base
               let filenameBase = `Stashy_highlights_${new Date().toISOString().split('T')[0]}`;
               try {
                   const urlObj = new URL(url);
                   const hostname = urlObj.hostname.replace(/^www\./, '');
                   const safeHostname = hostname.replace(/[^a-z0-9\.]/gi, '_').substring(0, 50);
                   filenameBase = `Stashy_highlights_${safeHostname}`;
               } catch (e) {
                   console.warn("Error creating filename from URL:", e);
               }

               // --- Format Selection Logic ---
               let fileContent = '';
               let fileExtension = '.txt';
               let mimeType = 'text/plain;charset=utf-8';

               // *** Use selectedFormat read from commonExportFormatSelect ***
               if (selectedFormat === 'pdf') {
                  // Check if required libraries are available
                  if (typeof window.jspdf === 'undefined') {
                      showStatus('PDF library not loaded.', 'error');
                      console.error("jsPDF library is required for PDF export but not loaded.");
                      exportHighlightsButton.disabled = false;
                      return;
                  }

                   // Generate the same beautiful HTML content as HTML export
                   let htmlBodyContent = `
<div class="header">
    <h1>Stashy Highlights</h1>
    <div class="export-date">Exported on: ${(() => {
        const exportDate = new Date();
        return !isNaN(exportDate.getTime())
            ? exportDate.toLocaleString()
            : new Date().toISOString().split('T')[0];
    })()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">
            ${window.StashyFaviconUtils ?
                `<img src="${window.StashyFaviconUtils.DEFAULT_FAVICON}" alt="Website favicon" style="width: 16px; height: 16px;">` :
                '📄'
            }
        </div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📊</div>
        <div class="meta-content">
            <div class="meta-label">Total Highlights</div>
            <div class="meta-value">${highlights.length}</div>
        </div>
    </div>
</div>`;

                   // Add highlights to the HTML content
                   highlights.forEach((h, index) => {
                       const style = h.style || 'color'; // Default to 'color' style if not specified
                       const color = (style === 'color') ? (h.color || DEFAULT_HIGHLIGHT_COLOR) : null;
                       const colorName = color ? color.charAt(0).toUpperCase() + color.slice(1) : '';

                       // Get style emoji and display text
                       let styleEmoji = '🟨'; // Default yellow highlight
                       let styleText = '';
                       let styleCSS = '';

                       if (style === 'color' && color) {
                           // Color highlight
                           if (color === 'green') styleEmoji = '🟩';
                           else if (color === 'blue') styleEmoji = '🟦';
                           else if (color === 'red') styleEmoji = '🟥';
                           else if (color === 'purple') styleEmoji = '🟪';
                           styleText = `Color: ${colorName}`;
                           styleCSS = `background-color: ${color};`;
                       }
                       else if (style === 'underline') {
                           styleEmoji = '🔽'; // Down arrow for underline
                           styleText = 'Style: Underline';
                           styleCSS = 'text-decoration: underline; text-decoration-color: #2196F3;';
                       }
                       else if (style === 'wavy') {
                           styleEmoji = '〰️'; // Wavy dash
                           styleText = 'Style: Wavy Underline';
                           styleCSS = 'text-decoration: underline wavy; text-decoration-color: #9C27B0;';
                       }
                       else if (style === 'border-thick') {
                           styleEmoji = '🔲'; // Black square button
                           styleText = 'Style: Thick Border';
                           styleCSS = 'border: 2px solid #4CAF50; padding: 2px 4px;';
                       }
                       else if (style === 'strikethrough') {
                           styleEmoji = '❌'; // Cross mark
                           styleText = 'Style: Strikethrough';
                           styleCSS = 'text-decoration: line-through; text-decoration-color: #F44336;';
                       }
                       else {
                           styleEmoji = '📝'; // Memo for unknown styles
                           styleText = `Style: ${style}`;
                           styleCSS = '';
                       }

                       // Process the HTML content to preserve images but escape other HTML
                       let processedHtml = h.text || '[Highlight Text Missing]';

                       // Create a temporary div to extract images
                       const tempDiv = document.createElement('div');
                       tempDiv.innerHTML = processedHtml;

                       // Extract all images
                       const images = Array.from(tempDiv.querySelectorAll('img'));
                       const imageHtml = images.map(img => {
                           // Create a safe image tag with the original src
                           return `<img src="${img.src}" alt="${img.alt || 'Image'}" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 4px;">`;
                       }).join('');

                       // Remove images from the original content
                       images.forEach(img => img.parentNode.removeChild(img));

                       // Get the text content without images and escape it
                       const textContent = tempDiv.innerHTML
                           .replace(/&/g, "&amp;")
                           .replace(/</g, "&lt;")
                           .replace(/>/g, "&gt;")
                           .replace(/"/g, "&quot;")
                           .replace(/'/g, "&#039;")
                           .replace(/\n/g, "<br>"); // Convert newlines to <br>

                       // Combine escaped text with preserved images
                       const escapedText = textContent + (imageHtml ? `<div class="highlight-images">${imageHtml}</div>` : '');

                       // Add timestamp if available
                       const timestampHtml = h.timestamp
                           ? `<div class="highlight-timestamp">Highlighted on: ${new Date(h.timestamp).toLocaleString()}</div>`
                           : '';

                       htmlBodyContent += `
    <div class="highlight-card">
        <div class="highlight-header">
            <div class="highlight-title">${styleEmoji} Highlight ${index + 1}</div>
            <div class="highlight-style">${styleText}</div>
        </div>
        ${timestampHtml}
        <blockquote class="highlight-content" style="${styleCSS} ${style === 'color' ? `border-left: 4px solid ${color};` : ''}">
            ${escapedText}
        </blockquote>
    </div>`;
                   });

                   // Add CSS styling (same as HTML export)
                   const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        min-height: 100vh;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 3px;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .highlight-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin: 25px 30px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: 1px solid var(--border-color);
    }

    .highlight-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .highlight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .highlight-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .highlight-style {
        padding: 6px 12px;
        border-radius: 20px;
        background-color: var(--secondary-color);
        color: white;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .highlight-timestamp {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 20px;
        font-style: italic;
        padding: 8px 12px;
        background-color: #f0f0f0;
        border-radius: 6px;
        border-left: 3px solid var(--primary-color);
    }

    .highlight-content {
        background-color: var(--light-bg);
        padding: 1.8rem;
        border-radius: 8px;
        margin: 0;
        line-height: 1.8;
        border-left: 5px solid var(--accent-color);
        border: 1px solid #e8e8e8;
        font-size: 1.05rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .highlight-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
    }

    .highlight-images {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e0e0e0;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .highlight-card {
            margin: 10px;
        }
    }
</style>`;

                   // Add footer
                   const footer = `
<footer>
    <p>Generated by Stashy on ${new Date().toLocaleDateString()}</p>
</footer>`;

                   // Combine all parts into complete HTML document
                   const completeHtmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy Highlights</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

                   // Convert styled HTML to PDF
                   const filename = `${filenameBase}.pdf`;
                   try {
                       await convertStyledHtmlToPdf(completeHtmlContent, filename, 'Generating beautiful PDF...');
                       exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button
                   } catch(pdfError) {
                       console.error("Error generating styled PDF:", pdfError);
                       showStatus('PDF generation failed.', 'error');
                       exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button on error
                   }
                   return; // PDF handles its own download and re-enabling

               } else if (selectedFormat === 'md') {
                   // Markdown export
                   fileExtension = '.md';
                   mimeType = 'text/markdown;charset=utf-8';

                   // Create a more visually appealing Markdown header
                   fileContent = `# 🔍 Stashy Highlights\n\n`;
                   fileContent += `> *Exported on: ${new Date().toLocaleString()}*\n\n`;
                   fileContent += `**📄 Page URL:** ${url}\n`;
                   fileContent += `**📊 Total Highlights:** ${highlights.length}\n\n`;
                   fileContent += `---\n\n`;

                   // Initialize markdown content
                   let markdownContent = '';

                   highlights.forEach((h, index) => {
                       const style = h.style || 'color'; // Default to 'color' style if not specified
                       const color = (style === 'color') ? (h.color || DEFAULT_HIGHLIGHT_COLOR) : null;
                       const colorName = color ? color.charAt(0).toUpperCase() + color.slice(1) : '';

                       // Get style emoji and display text
                       let styleEmoji = '🟨'; // Default yellow highlight
                       let styleText = '';

                       if (style === 'color' && color) {
                           // Color highlight
                           if (color === 'green') styleEmoji = '🟩';
                           else if (color === 'blue') styleEmoji = '🟦';
                           else if (color === 'red') styleEmoji = '🟥';
                           else if (color === 'purple') styleEmoji = '🟪';
                           styleText = `Color: ${colorName}`;
                       }
                       else if (style === 'underline') {
                           styleEmoji = '🔽'; // Down arrow for underline
                           styleText = 'Style: Underline';
                       }
                       else if (style === 'wavy') {
                           styleEmoji = '〰️'; // Wavy dash
                           styleText = 'Style: Wavy Underline';
                       }
                       else if (style === 'border-thick') {
                           styleEmoji = '🔲'; // Black square button
                           styleText = 'Style: Thick Border';
                       }
                       else if (style === 'strikethrough') {
                           styleEmoji = '❌'; // Cross mark
                           styleText = 'Style: Strikethrough';
                       }
                       else {
                           styleEmoji = '📝'; // Memo for unknown styles
                           styleText = `Style: ${style}`;
                       }

                       markdownContent += `## ${styleEmoji} Highlight ${index + 1} (${styleText})\n\n`;

                       // Add timestamp if available
                       if (h.timestamp) {
                           markdownContent += `*Highlighted on: ${new Date(h.timestamp).toLocaleString()}*\n\n`;
                       }

                       // Process the HTML content to preserve images in Markdown
                       let processedHtml = h.text || '[Highlight Text Missing]';

                       // Create a temporary div to extract images
                       const tempDiv = document.createElement('div');
                       tempDiv.innerHTML = processedHtml;

                       // Extract all images
                       const images = Array.from(tempDiv.querySelectorAll('img'));
                       let imageMarkdown = '';

                       // Create Markdown for each image
                       images.forEach((img, idx) => {
                           const alt = img.alt || `Image ${idx + 1}`;
                           const src = img.src || '#';
                           imageMarkdown += `\n\n![${alt}](${src})\n\n`;
                       });

                       // Remove images from the original content
                       images.forEach(img => img.parentNode.removeChild(img));

                       // Get the text content without images
                       const textContent = getPlainText(tempDiv.innerHTML, 'md');

                       // Quote the text content
                       const quotedText = textContent
                           .split('\n')
                           .map(line => `> ${line}`)
                           .join('\n');

                       // Combine quoted text with image markdown
                       markdownContent += `${quotedText}\n\n${imageMarkdown}`;

                       if (index < highlights.length - 1) {
                            markdownContent += `---\n\n`;
                       }
                   });

                   // Add footer
                   markdownContent += `\n\n---\n\n*Generated by Stashy on ${new Date().toLocaleDateString()}*`;

                   // Add the markdown content to the file content
                   fileContent += markdownContent;

               } else if (selectedFormat === 'html') {
                   // HTML export with enhanced styling
                   fileExtension = '.html';
                   mimeType = 'text/html;charset=utf-8';
                   let htmlBodyContent = `
<div class="header">
    <h1>Stashy Highlights</h1>
    <div class="export-date">Exported on: ${(() => {
        const exportDate = new Date();
        return !isNaN(exportDate.getTime())
            ? exportDate.toLocaleString()
            : new Date().toISOString().split('T')[0];
    })()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">
            ${window.StashyFaviconUtils ?
                `<img src="${window.StashyFaviconUtils.DEFAULT_FAVICON}" alt="Website favicon" style="width: 16px; height: 16px;">` :
                '📄'
            }
        </div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📊</div>
        <div class="meta-content">
            <div class="meta-label">Total Highlights</div>
            <div class="meta-value">${highlights.length}</div>
        </div>
    </div>
</div>`;

                   highlights.forEach((h, index) => {
                       const style = h.style || 'color'; // Default to 'color' style if not specified
                       const color = (style === 'color') ? (h.color || DEFAULT_HIGHLIGHT_COLOR) : null;
                       const colorName = color ? color.charAt(0).toUpperCase() + color.slice(1) : '';

                       // Get style emoji and display text
                       let styleEmoji = '🟨'; // Default yellow highlight
                       let styleText = '';
                       let styleCSS = '';

                       if (style === 'color' && color) {
                           // Color highlight
                           if (color === 'green') styleEmoji = '🟩';
                           else if (color === 'blue') styleEmoji = '🟦';
                           else if (color === 'red') styleEmoji = '🟥';
                           else if (color === 'purple') styleEmoji = '🟪';
                           styleText = `Color: ${colorName}`;
                           styleCSS = `background-color: ${color};`;
                       }
                       else if (style === 'underline') {
                           styleEmoji = '🔽'; // Down arrow for underline
                           styleText = 'Style: Underline';
                           styleCSS = 'text-decoration: underline; text-decoration-color: #2196F3;';
                       }
                       else if (style === 'wavy') {
                           styleEmoji = '〰️'; // Wavy dash
                           styleText = 'Style: Wavy Underline';
                           styleCSS = 'text-decoration: underline wavy; text-decoration-color: #9C27B0;';
                       }
                       else if (style === 'border-thick') {
                           styleEmoji = '🔲'; // Black square button
                           styleText = 'Style: Thick Border';
                           styleCSS = 'border: 2px solid #4CAF50; padding: 2px 4px;';
                       }
                       else if (style === 'strikethrough') {
                           styleEmoji = '❌'; // Cross mark
                           styleText = 'Style: Strikethrough';
                           styleCSS = 'text-decoration: line-through; text-decoration-color: #F44336;';
                       }
                       else {
                           styleEmoji = '📝'; // Memo for unknown styles
                           styleText = `Style: ${style}`;
                           styleCSS = '';
                       }

                       // Process the HTML content to preserve images but escape other HTML
                       let processedHtml = h.text || '[Highlight Text Missing]';

                       // Create a temporary div to extract images
                       const tempDiv = document.createElement('div');
                       tempDiv.innerHTML = processedHtml;

                       // Extract all images
                       const images = Array.from(tempDiv.querySelectorAll('img'));
                       const imageHtml = images.map(img => {
                           // Create a safe image tag with the original src
                           return `<img src="${img.src}" alt="${img.alt || 'Image'}" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 4px;">`;
                       }).join('');

                       // Remove images from the original content
                       images.forEach(img => img.parentNode.removeChild(img));

                       // Get the text content without images and escape it
                       const textContent = tempDiv.innerHTML
                           .replace(/&/g, "&amp;")
                           .replace(/</g, "&lt;")
                           .replace(/>/g, "&gt;")
                           .replace(/"/g, "&quot;")
                           .replace(/'/g, "&#039;")
                           .replace(/\n/g, "<br>"); // Convert newlines to <br>

                       // Combine escaped text with preserved images
                       const escapedText = textContent + (imageHtml ? `<div class="highlight-images">${imageHtml}</div>` : '');

                       // Add timestamp if available
                       const timestampHtml = h.timestamp
                           ? `<div class="highlight-timestamp">Highlighted on: ${new Date(h.timestamp).toLocaleString()}</div>`
                           : '';

                       htmlBodyContent += `
    <div class="highlight-card">
        <div class="highlight-header">
            <div class="highlight-title">${styleEmoji} Highlight ${index + 1}</div>
            <div class="highlight-style">${styleText}</div>
        </div>
        ${timestampHtml}
        <blockquote class="highlight-content" style="${styleCSS} ${style === 'color' ? `border-left: 4px solid ${color};` : ''}">
            ${escapedText}
        </blockquote>
    </div>`;
                   });

                   // Add CSS styling
                   const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        min-height: 100vh;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 3px;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .highlight-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin: 25px 30px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: 1px solid var(--border-color);
    }

    .highlight-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .highlight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .highlight-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .highlight-style {
        padding: 6px 12px;
        border-radius: 20px;
        background-color: var(--secondary-color);
        color: white;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .highlight-timestamp {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 20px;
        font-style: italic;
        padding: 8px 12px;
        background-color: #f0f0f0;
        border-radius: 6px;
        border-left: 3px solid var(--primary-color);
    }

    .highlight-content {
        background-color: var(--light-bg);
        padding: 1.8rem;
        border-radius: 8px;
        margin: 0;
        line-height: 1.8;
        border-left: 5px solid var(--accent-color);
        border: 1px solid #e8e8e8;
        font-size: 1.05rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .highlight-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
    }

    .highlight-images {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e0e0e0;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .highlight-card {
            margin: 10px;
        }
    }
</style>`;

                   // Add footer
                   const footer = `
<footer>
    <p>Generated by Stashy on ${new Date().toLocaleDateString()}</p>
</footer>`;

                   // Combine all parts
                   fileContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy Highlights</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

               } else { // Default to TXT export
                   fileExtension = '.txt';
                   mimeType = 'text/plain;charset=utf-8';

                   // Create a visually appealing separator between highlights
                   const highlightSeparator = '\n' +
                       '+=======================================================================+\n' +
                       '|                                                                       |\n' +
                       '+=======================================================================+\n\n';

                   // Create a decorative header
                   fileContent =
                       '+=======================================================================+\n' +
                       '|                      Stashy HIGHLIGHTS                          |\n' +
                       '+=======================================================================+\n\n' +
                       `Page URL: ${url}\n` +
                       `Exported on: ${new Date().toLocaleString()}\n` +
                       `Total Highlights: ${highlights.length}\n\n` +
                       '-----------------------------------------------------------------------\n\n';

                   highlights.forEach((h, index) => {
                       const color = h.color || DEFAULT_HIGHLIGHT_COLOR; // Use default if missing
                       const colorName = color.charAt(0).toUpperCase() + color.slice(1);

                       // Create a more structured highlight header
                       const highlightHeader =
                           `+----------------------[ HIGHLIGHT ${index + 1} ]------------------------+\n\n` +
                           `COLOR:    ${colorName}\n`;

                       // Add timestamp if available
                       const timestampText = h.timestamp
                           ? `DATE:     ${new Date(h.timestamp).toLocaleString()}\n`
                           : '';

                       const contentHeader =
                           `\nCONTENT:\n` +
                           `-----------------------------------------------------------------------\n`;

                       // Use 'txt' format to properly handle images in plain text
                       fileContent += highlightHeader + timestampText + contentHeader +
                           `${getPlainText(h.text || '[Highlight Text Missing]', 'txt')}\n\n`;

                       // Add separator between highlights (except after the last one)
                       if (index < highlights.length - 1) {
                           fileContent += highlightSeparator;
                       }
                   });

                   // Add footer
                   fileContent += '\n' +
                       '+=======================================================================+\n' +
                       '|                    Generated by Stashy                          |\n' +
                       '+=======================================================================+\n';
               }
               // --- End Format Selection ---


               // --- Download Logic (for non-PDF formats) ---
               try {
                    const blob = new Blob([fileContent], { type: mimeType });
                    const objectUrl = URL.createObjectURL(blob);
                    const filename = `${filenameBase}${fileExtension}`;

                    chrome.downloads.download({ url: objectUrl, filename: filename, saveAs: true }, (id) => {
                        URL.revokeObjectURL(objectUrl); // Clean up
                        if (chrome.runtime.lastError) {
                            showStatus(`Export failed: ${chrome.runtime.lastError.message}`, 'error');
                        } else if (!id) {
                            showStatus('Export may have been cancelled or blocked.', 'info');
                        } else {
                            showStatus('Highlight export started!', 'success');
                        }
                        exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button
                    });
                } catch (error) {
                    console.error("Error creating blob or initiating download:", error);
                    showStatus(`Export failed: ${error.message}`, 'error');
                    exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button on error
                }
                // --- End Download Logic ---
           });
       });
   }


  // --- Event Listeners ---

  // Enhanced Clear Button Listener
  if (clearButton) {
      clearButton.addEventListener('click', function () {
          clearButton.disabled = true; // Disable button immediately
          showStatus('Checking current page...', 'info'); // Give feedback

          chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
              if (chrome.runtime.lastError || !tabs || tabs.length === 0 || !tabs[0].id || !tabs[0].url) {
                  console.error("Popup Error querying tabs:", chrome.runtime.lastError?.message || "No active tab");
                  showStatus('Error finding current tab.', 'error');
                  clearButton.disabled = false; // Re-enable on error
                  return;
              }

              const tabId = tabs[0].id;
              const url = tabs[0].url;
              // With the new storage approach, URLs are stored in raw format
              // so we can display them directly without decoding
              let displayUrl = url;
              displayUrl = displayUrl.length > 60 ? displayUrl.substring(0, 57) + '...' : displayUrl; // Truncate long URLs

              // Allow http and https protocols
              if (!url || (!url.startsWith('http:') && !url.startsWith('https:'))) {
                  showStatus('Cannot clear notes on this type of page.', 'error');
                  clearButton.disabled = false; // Re-enable
                  return;
              }

              // --- Generate keys for notes 1 to 10 for checking using raw URL like highlights do ---
              const noteKeysToCheck = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${url}_note${i + 1}`);
              // --- End key generation ---

              chrome.storage.local.get(noteKeysToCheck, function(existingNotesResult) {
                  if (chrome.runtime.lastError) {
                       console.warn("Could not check which notes exist for clear confirmation:", chrome.runtime.lastError.message);
                       const fallbackMsg = `Clear ALL notes and highlights for the current page?\n\nPage: ${displayUrl}\n\nThis will also reset toolbar customization to default settings.\n\nThis cannot be undone.`;
                       if (!confirm(fallbackMsg)) {
                           clearButton.disabled = false; // Re-enable if cancelled
                           showStatus('Clear cancelled.', 'info');
                           return;
                       }
                       proceedWithClearing(tabId, url);
                       return;
                  }

                  const existingIndices = noteKeysToCheck
                      .map((key, index) => existingNotesResult[key] ? index + 1 : null)
                      .filter(index => index !== null);

                  let confirmMsg = `Clear all Stashy data for the current page?\n\nPage: ${displayUrl}\n\n`;
                  if (existingIndices.length > 0) {
                      confirmMsg += `This will permanently delete Note(s) ${existingIndices.join(', ')} and all highlights on this page.`;
                  } else {
                      confirmMsg += `This will permanently delete any highlights on this page (no note content found).`;
                  }
                  confirmMsg += `\n\nThis will also reset toolbar customization to default settings.`;
                  confirmMsg += `\n\nThis action cannot be undone.`;

                  if (!confirm(confirmMsg)) {
                      clearButton.disabled = false; // Re-enable if cancelled
                      showStatus('Clear cancelled.', 'info');
                      return;
                  }

                  proceedWithClearing(tabId, url);
              });
          });
      });
  }

  // Helper function to perform the actual clearing after confirmation
  function proceedWithClearing(tabId, url) {
       showStatus('Clearing page notes & highlights...', 'info');
       // --- Generate keys for notes 1 to 10 for removal using raw URL like highlights do ---
       const noteKeysToRemove = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${url}_note${i + 1}`);
       // --- End key generation ---
       const stateKey = `${STATE_KEY_PREFIX}${url}`;
       const highlightKey = `${HIGHLIGHT_KEY_PREFIX}${url}`;

       const keysToRemove = [...noteKeysToRemove, stateKey, highlightKey];

       chrome.storage.local.remove(keysToRemove, function () { // <-- Uses local
           if (chrome.runtime.lastError) {
               showStatus(`Failed to clear data: ${chrome.runtime.lastError.message}`, 'error');
           } else {
               showStatus('Page data cleared!', 'success');
               // Send message to content script to update its state/UI
               if (tabId) {
                   chrome.tabs.sendMessage(tabId, {
                       action: 'notesCleared'
                   }).catch(e => console.warn("Could not send notesCleared message to content script:", e?.message));
               } else {
                   console.warn("Cannot send notesCleared message, tabId is missing.");
               }

               // Re-load global notes in case the cleared page had a global note
               chrome.storage.local.get(null, (items) => {
                   if (chrome.runtime.lastError) {
                       console.error("Error getting storage after clear:", chrome.runtime.lastError);
                       return;
                   }
                  const allNotes = Object.entries(items)
                      .filter(([key, value]) => key.startsWith(STORAGE_KEY_PREFIX) && typeof value === 'object' && value !== null)
                      .map(([key, data]) => ({ key, data }));
                  renderGlobalNotes(allNotes);
              });
           }
           if(clearButton) clearButton.disabled = false; // Re-enable button after operation
       });
  }

  // *** MODIFIED *** Export Notes Listener
  if (exportNotesButton && commonExportFormatSelect) {
      exportNotesButton.addEventListener('click', function () {
          // --- Premium Check for Export Features ---
          if (typeof window.StashyPremium !== 'undefined') {
              // Check if user has access to export features
              if (!window.StashyPremium.isFeatureAvailable('export_pdf') &&
                  !window.StashyPremium.isFeatureAvailable('export_word') &&
                  !window.StashyPremium.isFeatureAvailable('export_html')) {
                  console.log('Stashy: Notes export blocked - premium feature');
                  showStatus('Export features require Stashy Pro. Upgrade to unlock PDF, Word, and HTML exports!', 'warning');

                  // Show upgrade prompt
                  if (window.StashyPremium.showUpgradePrompt) {
                      window.StashyPremium.showUpgradePrompt('Export Features', 'popup-export-notes');
                  }
                  return;
              }
          }

          exportNotesButton.disabled = true;
          const selectedFormat = commonExportFormatSelect.value; // *** READ FROM COMMON SELECT ***
          showStatus(`Exporting page notes (${selectedFormat.toUpperCase()})...`, 'info');

          chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
               if (chrome.runtime.lastError || !tabs || tabs.length === 0 || !tabs[0].url || !tabs[0].id) {
                   showStatus('Could not get current page details.', 'error');
                   exportNotesButton.disabled = false;
                   return;
               }
               const url = tabs[0].url;
               if (!url || (!url.startsWith('http:') && !url.startsWith('https:'))) {
                   showStatus('Cannot export notes from this type of page.', 'error');
                   exportNotesButton.disabled = false;
                   return;
               }
               // Generate note keys using raw URL like highlights do
               // This ensures URLs are preserved exactly as they appear in the browser
               const noteKeys = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${url}_note${i + 1}`);

               chrome.storage.local.get(noteKeys, async function (results) { // <-- Uses local
                   if (chrome.runtime.lastError) {
                      showStatus(`Error retrieving notes: ${chrome.runtime.lastError.message}`, 'error');
                      exportNotesButton.disabled = false;
                      return;
                  }
                  // Process results based on the `noteKeys` array (which now includes 1-10)
                  const notes = noteKeys.map((k, i) => ({ data: results[k], index: i + 1 }))
                                     .filter(item => item.data && (item.data.text || item.data.tags?.length || item.data.globallyPinned));
                  if (notes.length === 0) {
                      showStatus('No notes found with content or tags to export.', 'info');
                      exportNotesButton.disabled = false;
                      return;
                  }

                  // --- Format Selection Logic ---
                  let fileContent = '';
                  let fileExtension = '.txt';
                  let mimeType = 'text/plain;charset=utf-8';
                  let pageTitle = `Stashy Export: ${url}`;

                  // *** Use selectedFormat read from commonExportFormatSelect ***
                  if (selectedFormat === 'md') {
                      fileExtension = '.md';
                      mimeType = 'text/markdown;charset=utf-8';

                      // Create a more visually appealing Markdown header
                      fileContent = `# 📝 Stashy Notes\n\n`;
                      fileContent += `> *Exported on: ${new Date().toLocaleString()}*\n\n`;
                      fileContent += `**📄 Page URL:** ${url}\n`;
                      fileContent += `**📊 Total Notes:** ${notes.length}\n\n`;
                      fileContent += `---\n\n`;

                      // Process each note
                      fileContent += notes.map((noteItem) => {
                           const n = noteItem.data;
                           const idx = noteItem.index;

                           // Format important note status
                           const globalStatus = n.globallyPinned ? "⭐ **IMPORTANT NOTE**\n\n" : "";

                           // Format metadata
                           const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                           const savedText = savedDate && !isNaN(savedDate.getTime()) ? `*Last Saved:* ${savedDate.toLocaleString()}` : "";

                           const reminderDate = n.reminder ? new Date(n.reminder) : null;
                           const reminderText = reminderDate && !isNaN(reminderDate.getTime()) ? `*Reminder:* ${reminderDate.toLocaleString()}` : "";

                           const tagsText = n.tags?.length ? `*Tags:* ${n.tags.join(', ')}` : "";
                           const metadata = [tagsText, reminderText, savedText].filter(Boolean).join('\n');

                           // Process the note content to preserve images
                           let noteContent = n.text || '';

                           // Create a temporary div to extract images
                           const tempDiv = document.createElement('div');
                           tempDiv.innerHTML = noteContent;

                           // Extract all images
                           const images = Array.from(tempDiv.querySelectorAll('img'));
                           let imageMarkdown = '';

                           // Create Markdown for each image
                           images.forEach((img, idx) => {
                               const alt = img.alt || `Image ${idx + 1}`;
                               const src = img.src || '#';
                               imageMarkdown += `\n\n![${alt}](${src})\n\n`;
                           });

                           // Remove images from the original content
                           images.forEach(img => img.parentNode.removeChild(img));

                           // Convert the remaining HTML to Markdown
                           const textContent = convertToMarkdownPopup(tempDiv.innerHTML);

                           // Combine everything
                           return `## 📌 Note ${idx}\n\n${globalStatus}${textContent}\n\n${imageMarkdown}${metadata ? '\n\n' + metadata + '\n\n' : ''}\n---\n\n`;
                      }).join('');

                  } else if (selectedFormat === 'html') {
                      fileExtension = '.html';
                      mimeType = 'text/html;charset=utf-8';

                      // Create a more visually appealing HTML export
                      let htmlBodyContent = `
<div class="header">
    <h1>Stashy Notes</h1>
    <div class="export-date">Exported on: ${new Date().toLocaleString()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">
            ${window.StashyFaviconUtils ?
                `<img src="${window.StashyFaviconUtils.DEFAULT_FAVICON}" alt="Website favicon" style="width: 16px; height: 16px;">` :
                '📄'
            }
        </div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📝</div>
        <div class="meta-content">
            <div class="meta-label">Total Notes</div>
            <div class="meta-value">${notes.length}</div>
        </div>
    </div>
</div>`;

                      notes.forEach((noteItem) => {
                          const n = noteItem.data;
                          const idx = noteItem.index;

                          // Handle important note status
                          const globalStatus = n.globallyPinned
                              ? `<div class="note-global-pin">IMPORTANT NOTE</div>`
                              : "";

                          // Format metadata
                          const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                          const savedText = savedDate && !isNaN(savedDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Last Saved:</span> ${savedDate.toLocaleString()}</div>`
                              : "";

                          const reminderDate = n.reminder ? new Date(n.reminder) : null;
                          const reminderText = reminderDate && !isNaN(reminderDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Reminder:</span> ${reminderDate.toLocaleString()}</div>`
                              : "";

                          const tagsText = n.tags?.length
                              ? `<div class="meta-item"><span class="meta-label">Tags:</span> ${n.tags.join(', ')}</div>`
                              : "";

                          const metadata = `<div class="note-metadata">${[tagsText, reminderText, savedText].filter(Boolean).join('')}</div>`;

                          // Process the note content to ensure images are preserved
                          let noteContent = n.text || '';

                          // Create a note card with the content
                          htmlBodyContent += `
<div class="note-card">
    <div class="note-header">
        <div class="note-title">Note ${idx}</div>
        ${globalStatus}
    </div>
    <div class="note-content">
        ${noteContent}
    </div>
    ${metadata}
</div>`;
                      });

                      // Add CSS styling
                      const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 0;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-right: 5px;
        font-weight: bold;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .note-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        margin: 20px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .note-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 10px;
    }

    .note-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .note-global-pin {
        background-color: #dc3545;
        color: white;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .note-content {
        background-color: var(--light-bg);
        padding: 1.2rem;
        border-radius: 6px;
        margin-bottom: 15px;
        line-height: 1.7;
        border-left: 4px solid var(--accent-color);
    }

    .note-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: block;
        page-break-inside: avoid;
    }

    .note-content .Stashy-video-screenshot {
        border: 2px solid #007bff;
        border-radius: 8px;
        margin: 20px 0;
        max-width: 100%;
        height: auto;
        display: block;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
    }

    .note-metadata {
        font-size: 0.85rem;
        color: #666;
        padding: 10px;
        background-color: var(--light-bg);
        border-radius: 4px;
    }

    .note-metadata .meta-item {
        margin-bottom: 5px;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .note-card {
            margin: 10px;
        }
    }
</style>`;

                      // Add footer
                      const footer = `
<footer>
    <p>Generated by Stashy on ${(() => {
        const footerDate = new Date();
        return !isNaN(footerDate.getTime())
            ? footerDate.toLocaleDateString()
            : new Date().toISOString().split('T')[0];
    })()}</p>
</footer>`;

                      // Combine all parts
                      fileContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

                  } else if (selectedFormat === 'pdf') {
                      fileExtension = '.pdf';
                      mimeType = 'application/pdf';

                      // Check if required libraries are available
                      if (typeof window.jspdf === 'undefined') {
                            showStatus('PDF library not loaded.', 'error');
                            console.error("jsPDF library is required for PDF export but not loaded.");
                            exportNotesButton.disabled = false;
                            return;
                      }

                      // Generate the same beautiful HTML content as HTML export
                      let htmlBodyContent = `
<div class="header">
    <h1>Stashy Notes</h1>
    <div class="export-date">Exported on: ${new Date().toLocaleString()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">
            ${window.StashyFaviconUtils ?
                `<img src="${window.StashyFaviconUtils.DEFAULT_FAVICON}" alt="Website favicon" style="width: 16px; height: 16px;">` :
                '📄'
            }
        </div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📝</div>
        <div class="meta-content">
            <div class="meta-label">Total Notes</div>
            <div class="meta-value">${notes.length}</div>
        </div>
    </div>
</div>`;

                      // Add notes to the HTML content
                      notes.forEach((noteItem) => {
                          const n = noteItem.data;
                          const idx = noteItem.index;

                          // Handle important note status
                          const globalStatus = n.globallyPinned
                              ? `<div class="note-global-pin">IMPORTANT NOTE</div>`
                              : "";

                          // Format metadata
                          const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                          const savedText = savedDate && !isNaN(savedDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Last Saved:</span> ${savedDate.toLocaleString()}</div>`
                              : "";

                          const reminderDate = n.reminder ? new Date(n.reminder) : null;
                          const reminderText = reminderDate && !isNaN(reminderDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Reminder:</span> ${reminderDate.toLocaleString()}</div>`
                              : "";

                          const tagsText = n.tags?.length
                              ? `<div class="meta-item"><span class="meta-label">Tags:</span> ${n.tags.join(', ')}</div>`
                              : "";

                          const metadata = `<div class="note-metadata">${[tagsText, reminderText, savedText].filter(Boolean).join('')}</div>`;

                          // Process the note content to ensure images are preserved
                          let noteContent = n.text || '';

                          // Create a note card with the content
                          htmlBodyContent += `
<div class="note-card">
    <div class="note-header">
        <div class="note-title">Note ${idx}</div>
        ${globalStatus}
    </div>
    <div class="note-content">
        ${noteContent}
    </div>
    ${metadata}
</div>`;
                      });

                      // Add CSS styling (same as HTML export)
                      const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 0;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-right: 5px;
        font-weight: bold;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .note-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        margin: 20px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .note-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 10px;
    }

    .note-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .note-global-pin {
        background-color: #dc3545;
        color: white;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .note-content {
        background-color: var(--light-bg);
        padding: 1.2rem;
        border-radius: 6px;
        margin-bottom: 15px;
        line-height: 1.7;
        border-left: 4px solid var(--accent-color);
    }

    .note-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: block;
        page-break-inside: avoid;
    }

    .note-content .Stashy-video-screenshot {
        border: 2px solid #007bff;
        border-radius: 8px;
        margin: 20px 0;
        max-width: 100%;
        height: auto;
        display: block;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
        page-break-inside: avoid;
    }

    .note-metadata {
        font-size: 0.85rem;
        color: #666;
        padding: 10px;
        background-color: var(--light-bg);
        border-radius: 4px;
    }

    .note-metadata .meta-item {
        margin-bottom: 5px;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .note-card {
            margin: 10px;
        }
    }
</style>`;

                      // Add footer
                      const footer = `
<footer>
    <p>Generated by Stashy on ${(() => {
        const footerDate = new Date();
        return !isNaN(footerDate.getTime())
            ? footerDate.toLocaleDateString()
            : new Date().toISOString().split('T')[0];
    })()}</p>
</footer>`;

                      // Combine all parts into complete HTML document
                      const completeHtmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy Notes</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

                      // Generate filename
                      let filename = `Stashy_notes_${new Date().toISOString().split('T')[0]}.pdf`;
                      try {
                          const urlObj = new URL(url);
                          const hostname = urlObj.hostname.replace(/^www\./, '');
                          const safeHostname = hostname.replace(/[^a-z0-9\.]/gi, '_').substring(0, 50);
                          const indicesString = notes.map(n => n.index).join('_');
                          filename = `Stashy_${safeHostname}_notes_${indicesString}.pdf`;
                      } catch (e) {
                          console.warn("Error creating filename from URL:", e);
                      }

                      // Convert styled HTML to PDF
                      try {
                          await convertStyledHtmlToPdf(completeHtmlContent, filename, 'Generating beautiful PDF...');
                          exportNotesButton.disabled = false; // Re-enable NOTE export button
                      } catch (pdfError) {
                          console.error("Error generating styled PDF:", pdfError);
                          showStatus('PDF generation failed.', 'error');
                          exportNotesButton.disabled = false; // Re-enable NOTE export button on error
                      }

                      // Return early since PDF handles its own download disabling/enabling
                      return;

                  } else { // Default to Plain Text
                      fileExtension = '.txt';
                      mimeType = 'text/plain;charset=utf-8';

                      // Create a visually appealing separator between notes
                      const noteSeparator = '\n' +
                          '+=======================================================================+\n' +
                          '|                                                                       |\n' +
                          '+=======================================================================+\n\n';

                      // Create a decorative header
                      fileContent =
                          '+=======================================================================+\n' +
                          '|                        Stashy NOTES                             |\n' +
                          '+=======================================================================+\n\n' +
                          `Page URL: ${url}\n` +
                          `Exported on: ${new Date().toLocaleString()}\n` +
                          `Total Notes: ${notes.length}\n\n` +
                          '-----------------------------------------------------------------------\n\n';

                      notes.forEach((noteItem, index) => {
                          const n = noteItem.data;
                          const idx = noteItem.index;

                          // Create a more structured note header
                          const noteHeader =
                              `+----------------------[ NOTE ${idx} ]---------------------------+\n\n`;

                          // Format important note status
                          const globalStatus = n.globallyPinned ? "IMPORTANT NOTE: Yes\n" : "";

                          // Format metadata
                          const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                          const savedText = savedDate && !isNaN(savedDate.getTime()) ?
                              `Last Saved: ${savedDate.toLocaleString()}` : "Last Saved: N/A";

                          const reminderDate = n.reminder ? new Date(n.reminder) : null;
                          const reminderText = reminderDate && !isNaN(reminderDate.getTime()) ?
                              `Reminder: ${reminderDate.toLocaleString()}` : "Reminder: None";

                          const tagsText = n.tags?.length ?
                              `Tags: ${n.tags.join(', ')}` : "Tags: None";

                          // Combine metadata
                          const metadataHeader =
                              `METADATA:\n-----------------------------------------------------------------------\n`;

                          const metadataContent = [globalStatus, tagsText, reminderText, savedText].filter(Boolean).join('\n');

                          // Process the note content to handle images
                          let noteContent = n.text || '';

                          // Create a temporary div to extract images
                          const tempDiv = document.createElement('div');
                          tempDiv.innerHTML = noteContent;

                          // Extract all images
                          const images = Array.from(tempDiv.querySelectorAll('img'));
                          let imageText = '';

                          // Create text representation for each image
                          if (images.length > 0) {
                              imageText = `\nIMAGES (${images.length}):\n-----------------------------------------------------------------------\n`;
                              images.forEach((img, imgIdx) => {
                                  const alt = img.alt || `Image ${imgIdx + 1}`;
                                  const src = img.src || '#';
                                  imageText += `[IMAGE ${imgIdx + 1}: ${alt}] Source: ${src.substring(0, 50)}...\n`;
                              });
                              imageText += '\n';
                          }

                          // Get plain text content
                          const plainText = getPlainText(noteContent, 'txt');

                          // Content header
                          const contentHeader =
                              `CONTENT:\n-----------------------------------------------------------------------\n`;

                          // Combine all parts
                          fileContent += noteHeader +
                              metadataHeader + metadataContent + '\n\n' +
                              contentHeader + plainText + '\n\n' +
                              imageText;

                          // Add separator between notes (except after the last one)
                          if (index < notes.length - 1) {
                              fileContent += noteSeparator;
                          }
                      });

                      // Add footer
                      fileContent += '\n' +
                          '+=======================================================================+\n' +
                          '|                    Generated by Stashy                          |\n' +
                          '+=======================================================================+\n';
                  }
                  // --- End Format Selection ---

                  // --- Download Logic (for non-PDF formats) ---
                  try {
                      const blob = new Blob([fileContent], { type: mimeType });
                      const objectUrl = URL.createObjectURL(blob);
                      let filename = `Stashy_notes_${new Date().toISOString().split('T')[0]}${fileExtension}`;
                      try {
                          const urlObj = new URL(url);
                          const hostname = urlObj.hostname.replace(/^www\./, '');
                          const safeHostname = hostname.replace(/[^a-z0-9\.]/gi, '_').substring(0, 50);
                          // Include all note indices in filename if multiple exist
                          const indicesString = notes.map(n=>n.index).join('_');
                          filename = `Stashy_${safeHostname}_notes_${indicesString}${fileExtension}`;
                      } catch(e) { console.warn("Error creating filename from URL:", e); }

                      chrome.downloads.download({ url: objectUrl, filename: filename, saveAs: true }, (id) => {
                            URL.revokeObjectURL(objectUrl); // Clean up
                          if (chrome.runtime.lastError) {
                              showStatus(`Export failed: ${chrome.runtime.lastError.message}`, 'error');
                          } else if (!id) {
                              showStatus('Export may have been cancelled or blocked.', 'info');
                          } else {
                              showStatus('Export download started!', 'success');
                          }
                          exportNotesButton.disabled = false; // Re-enable NOTE export button
                       });
                  } catch(error) {
                       console.error("Error creating blob or initiating download:", error);
                       showStatus(`Export failed: ${error.message}`, 'error');
                       exportNotesButton.disabled = false; // Re-enable NOTE export button on error
                  }
                   // --- End Download Logic ---
               });
          });
      });
  }

  // *** MODIFIED *** Export Highlights Listener
  if (exportHighlightsButton && commonExportFormatSelect) {
        // Use the previously defined exportPageHighlights function
        exportHighlightsButton.addEventListener('click', exportPageHighlights);
  } else if (!exportHighlightsButton) {
      console.warn("Export highlights button not found in popup.");
  } else if (!commonExportFormatSelect) {
       console.warn("Common export format select not found in popup.");
  }


  if (searchButton && searchQueryInput) {
      searchButton.addEventListener('click', performSearch);
      searchQueryInput.addEventListener('keypress', function (e) {
          if (e.key === 'Enter') {
              e.preventDefault(); // Prevent form submission if inside a form
              performSearch();
          }
      });
  }

  if (viewDashboardButton) {
      viewDashboardButton.addEventListener('click', function() {
          chrome.tabs.create({ url: chrome.runtime.getURL('dashboard.html') });
          window.close(); // Close the popup after opening the dashboard
      });
  }

  // User Authentication Event Listeners
  if (userSignInButton) {
    userSignInButton.addEventListener('click', handleUserSignIn);
  }

  if (userSignOutButton) {
    userSignOutButton.addEventListener('click', handleUserSignOut);
  }

  // Load initial user authentication state
  loadUserAuthState();

  // Google Drive Sync Button Listeners
  if (connectDriveButton && disconnectDriveButton && syncStatusIndicator) {
      connectDriveButton.addEventListener('click', async () => {
          // Check if user is authenticated first
          try {
            const userResponse = await chrome.runtime.sendMessage({ action: 'getUserProfile' });
            if (!userResponse || !userResponse.success || !userResponse.userProfile) {
              showStatus('Please sign in first to connect Google Drive', 'error');
              return;
            }
          } catch (error) {
            showStatus('Please sign in first to connect Google Drive', 'error');
            return;
          }

          showStatus('Requesting Google Drive access...', 'info');
          connectDriveButton.disabled = true;
          disconnectDriveButton.disabled = true;
          chrome.runtime.sendMessage({ action: 'connectGoogleDrive' }, (response) => {
              // Always re-enable buttons after response or error
              connectDriveButton.disabled = false;
              disconnectDriveButton.disabled = false; // It might be visible now or not
              if (chrome.runtime.lastError) {
                   showStatus(`Error: ${chrome.runtime.lastError.message}`, 'error');
                   updateSyncButtonUI(false); // Assume failed connection
                   if(syncStatusIndicator) syncStatusIndicator.className = 'sync-indicator error'; // Base + state
                   if(syncStatusIndicator) syncStatusIndicator.title = `Sync Status: Error - ${chrome.runtime.lastError.message}`;
              } else if (response?.status === 'success') {
                  showStatus('Google Drive connected! Initial sync may take time.', 'success'); // Update status
                  updateSyncButtonUI(true);
                  // Background script should trigger sync automatically on connect
              } else {
                  showStatus(`Connection failed: ${response?.message || 'Unknown error'}`, 'error');
                  updateSyncButtonUI(false);
                  if(syncStatusIndicator) syncStatusIndicator.className = 'sync-indicator error'; // Base + state
                  if(syncStatusIndicator) syncStatusIndicator.title = `Sync Status: Error - ${response?.message || 'Unknown error'}`;
              }
          });
      });

      disconnectDriveButton.addEventListener('click', () => {
          if (!confirm("Disconnect from Google Drive? Notes will no longer sync.")) return;
          showStatus('Disconnecting Google Drive...', 'info');
          connectDriveButton.disabled = true;
          disconnectDriveButton.disabled = true;

          chrome.runtime.sendMessage({ action: 'disconnectGoogleDrive' }, (response) => {
              // Always re-enable relevant button after response or error
              connectDriveButton.disabled = false; // Re-enable connect in case disconnect fails
              disconnectDriveButton.disabled = false; // Re-enable disconnect in case disconnect fails (though UI might hide it)

              if (chrome.runtime.lastError) {
                  showStatus(`Error disconnecting: ${chrome.runtime.lastError.message}`, 'error');
                  updateSyncButtonUI(true); // Assume still connected on error
              } else if (response && response.success) {
                  showStatus('Disconnected from Google Drive.', 'success');
                  updateSyncButtonUI(false); // Update UI to disconnected state
              } else {
                  showStatus('Failed to disconnect from Google Drive.', 'error');
                  if(syncStatusIndicator) syncStatusIndicator.className = 'sync-indicator error'; // Base + state
                  updateSyncButtonUI(true); // Assume still connected if disconnect failed
              }
          });
      });
  }

  // Close buttons for settings panels
  if (closeVoiceSettingsButton) {
    closeVoiceSettingsButton.addEventListener('click', () => {
      const panel = document.getElementById('voice-settings-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          panel.classList.add('hidden');
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  if (closeNoteSettingsButton) {
    closeNoteSettingsButton.addEventListener('click', () => {
      const panel = document.getElementById('note-settings-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          panel.classList.add('hidden');
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  // Close UI Customization Panel
  const closeUICustomizationButton = document.getElementById('close-ui-customization');
  if (closeUICustomizationButton) {
    closeUICustomizationButton.addEventListener('click', () => {
      const panel = document.getElementById('ui-customization-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          panel.classList.add('hidden');
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  // Close Help & Info Panel
  const closeHelpInfoButton = document.getElementById('close-help-info');
  if (closeHelpInfoButton) {
    closeHelpInfoButton.addEventListener('click', () => {
      const panel = document.getElementById('help-info-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          panel.classList.add('hidden');
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }



  const closeScreenshotSettingsButton = document.getElementById('close-screenshot-settings');
  if (closeScreenshotSettingsButton) {
    closeScreenshotSettingsButton.addEventListener('click', () => {
      const panel = document.getElementById('screenshot-settings-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          panel.classList.add('hidden');
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  // Close AI Settings Panel
  const closeAiSettingsButton = document.getElementById('close-ai-settings');
  if (closeAiSettingsButton) {
    closeAiSettingsButton.addEventListener('click', () => {
      const panel = document.getElementById('ai-settings-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          panel.classList.add('hidden');
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  // ===================================================
  // == Voice Settings Initialization and Handling =====
  // ===================================================
  // NOTE: initVoiceSettings function has been moved outside DOMContentLoaded
  // to make it globally accessible. See function definition after DOMContentLoaded.













  // Listener for Background Updates (Sync Status, Connection Status)
   chrome.runtime.onMessage.addListener((message) => {
       if (message.action === 'updateSyncStatus') {
           if (syncStatusIndicator) {
               let statusClass = 'connected'; // Default green
               let statusText = 'Connected'; // Default text

               switch(message.status) {
                   case 'syncing':
                       statusClass = 'syncing';
                       statusText = 'Syncing';
                       break;
                   case 'error':
                       statusClass = 'error';
                       statusText = 'Sync Error';
                       break;
                   case 'disconnected':
                       statusClass = 'disconnected';
                       statusText = 'Disconnected';
                       break;
                   case 'connected': // Explicitly handle 'connected' state from background
                   default:
                        statusClass = 'connected';
                        statusText = 'Connected';
                        break;
               }
               syncStatusIndicator.className = `sync-indicator ${statusClass}`; // Update class
               if (message.details) statusText += `: ${message.details}`;
               else if (statusClass !== 'disconnected' && statusClass !== 'error') {
                   statusText += ` (${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })})`; // Add timestamp if not disconnected/error
               }
               syncStatusIndicator.title = `Sync Status: ${statusText}`;
           }
       } else if (message.action === 'updateConnectionStatus') {
           updateSyncButtonUI(message.isConnected);
       }
       // No async response needed from popup for these messages
       return false;
   });

  // --- Initialize Search Interface ---
  if (searchResultsDiv && searchQueryInput) {
    searchResultsDiv.innerHTML = '<p class="no-results">Enter text or use operators (tag:, url:, date:, color:, reminder:true/false) to search.</p>';
    searchQueryInput.focus(); // Focus search input on popup open
  } else {
    console.error("Stashy Popup: Search initialization failed - missing elements:", {
      searchResultsDiv: !!searchResultsDiv,
      searchQueryInput: !!searchQueryInput
    });
  }

  // Load global notes on popup open
  chrome.storage.local.get(null, (items) => {
       if (chrome.runtime.lastError) {
          console.error("Error getting storage for global notes:", chrome.runtime.lastError);
          if (globalNotesResultsDiv) globalNotesResultsDiv.innerHTML = '<p class="no-results">Error loading global notes.</p>';
          showStatus('Error loading notes.', 'error');
          return;
       }
       const allNotes = Object.entries(items)
           .filter(([key, value]) => key.startsWith(STORAGE_KEY_PREFIX) && typeof value === 'object' && value !== null)
           .map(([key, data]) => ({ key, data }));
       renderGlobalNotes(allNotes); // Render global notes
  });

  // Check initial Drive connection state and last known sync status
  chrome.storage.local.get(['driveSyncEnabled', 'lastSyncStatus', 'lastSyncDetails', 'lastSyncTime'], (result) => {
        const isConnected = result.driveSyncEnabled === true;
        updateSyncButtonUI(isConnected);

        if (isConnected && syncStatusIndicator) {
            let initialStatusClass = 'connected';
            let initialStatusText = 'Connected';
            const lastStatus = result.lastSyncStatus;
            const lastDetails = result.lastSyncDetails;
            const lastTime = result.lastSyncTime ? new Date(result.lastSyncTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : null;

            switch(lastStatus) {
                case 'syncing': // Unlikely on load, but possible if popup closed mid-sync
                    initialStatusClass = 'syncing';
                    initialStatusText = 'Syncing...';
                    break;
                case 'error':
                    initialStatusClass = 'error';
                    initialStatusText = 'Sync Error';
                    if (lastDetails) initialStatusText += `: ${lastDetails}`;
                    break;
                case 'success': // Treat success as connected
                case 'connected':
                default:
                    initialStatusClass = 'connected';
                    initialStatusText = 'Connected';
                    if (lastTime) initialStatusText += ` (Last sync: ${lastTime})`;
                    break;
            }
            syncStatusIndicator.className = `sync-indicator ${initialStatusClass}`;
            syncStatusIndicator.title = `Sync Status: ${initialStatusText}`;

        } else if (!isConnected && syncStatusIndicator) {
            syncStatusIndicator.className = 'sync-indicator disconnected';
            syncStatusIndicator.title = 'Sync Status: Disconnected';
        }
    });
});

// ===================================================
// == Note Settings Initialization and Handling =======
// ===================================================
function initNoteSettings(panel) {
  // Store the panel reference
  const noteSettingsPanel = panel;

  // DOM Elements within the note settings panel
  const defaultWidthInput = document.getElementById('default-width');
  const defaultHeightInput = document.getElementById('default-height');
  const defaultTopInput = document.getElementById('default-top');
  const defaultRightInput = document.getElementById('default-right');
  const saveButton = document.getElementById('save-note-settings');
  const resetButton = document.getElementById('reset-note-settings');
  const statusMessage = document.getElementById('note-status-text');


  // Check if essential elements exist before proceeding
  if (!defaultWidthInput || !defaultHeightInput || !saveButton) {
    console.error("Note settings panel elements not found. Cannot initialize.");
    if (noteSettingsPanel) noteSettingsPanel.innerHTML = "<p style='color: red;'>Error loading settings UI.</p>";
    return;
  }

  // Load settings and populate form
  loadNoteSettings();

  // Event listeners
  saveButton.addEventListener('click', saveNoteSettings);
  resetButton.addEventListener('click', resetNoteSettings);



  /**
   * Loads settings from storage and populates the form
   */
  function loadNoteSettings() {
    // Use the settings module to get current settings
    if (window.StashySettings && typeof window.StashySettings.loadSettings === 'function') {
      window.StashySettings.loadSettings().then(settings => {
        // Remove 'px' from size values and convert to numbers
        defaultWidthInput.value = parseInt(settings.defaultNoteSize.width);
        defaultHeightInput.value = parseInt(settings.defaultNoteSize.height);
        defaultTopInput.value = parseInt(settings.defaultNotePosition.top);
        defaultRightInput.value = parseInt(settings.defaultNotePosition.right);
      });
    } else {
      // Fallback to direct storage access if settings module is not available
      chrome.storage.local.get(['Stashy_settings'], (result) => {
        const settings = result.Stashy_settings || {
          defaultNoteSize: { width: '340px', height: '380px' },
          defaultNotePosition: { top: '100px', right: '24px' }
        };

        // Remove 'px' from size values and convert to numbers
        defaultWidthInput.value = parseInt(settings.defaultNoteSize.width);
        defaultHeightInput.value = parseInt(settings.defaultNoteSize.height);
        defaultTopInput.value = parseInt(settings.defaultNotePosition.top);
        defaultRightInput.value = parseInt(settings.defaultNotePosition.right);
      });
    }
  }

  /**
   * Saves the form settings to storage
   */
  function saveNoteSettings() {
    // Get values from form
    const defaultWidth = defaultWidthInput.value + 'px';
    const defaultHeight = defaultHeightInput.value + 'px';
    const defaultTop = defaultTopInput.value + 'px';
    const defaultRight = defaultRightInput.value + 'px';

    // Create settings object
    const settings = {
      defaultNoteSize: {
        width: defaultWidth,
        height: defaultHeight
      },
      defaultNotePosition: {
        top: defaultTop,
        right: defaultRight
      },
      version: '1.0'
    };



    // Save to storage
    chrome.storage.local.set({ 'Stashy_settings': settings }, () => {
      if (chrome.runtime.lastError) {
        showNoteSettingsStatus('Error saving settings: ' + chrome.runtime.lastError.message, 'error');
      } else {
        showNoteSettingsStatus('Settings saved successfully!', 'success');
      }
    });
  }

  /**
   * Resets settings to defaults
   */
  function resetNoteSettings() {
    if (confirm('Reset all settings to default values?')) {
      const defaultSettings = {
        defaultNoteSize: {
          width: '340px',
          height: '380px'
        },
        defaultNotePosition: {
          top: '100px',
          right: '24px'
        },
        version: '1.0'
      };

      // Save defaults to storage
      chrome.storage.local.set({ 'Stashy_settings': defaultSettings }, () => {
        if (chrome.runtime.lastError) {
          showNoteSettingsStatus('Error resetting settings: ' + chrome.runtime.lastError.message, 'error');
        } else {
          // Reload form with default values
          defaultWidthInput.value = parseInt(defaultSettings.defaultNoteSize.width);
          defaultHeightInput.value = parseInt(defaultSettings.defaultNoteSize.height);
          defaultTopInput.value = parseInt(defaultSettings.defaultNotePosition.top);
          defaultRightInput.value = parseInt(defaultSettings.defaultNotePosition.right);

          showNoteSettingsStatus('Settings reset to defaults', 'success');
        }
      });
    }
  }

  /**
   * Shows a status message in the note settings panel
   * @param {string} message - The message to display
   * @param {string} type - The type of message ('success', 'error', 'info')
   */
  function showNoteSettingsStatus(message, type = 'info') {
    if (!statusMessage) return;

    statusMessage.textContent = message;
    statusMessage.parentElement.className = 'status-message ' + type;
    statusMessage.parentElement.style.display = 'block';

    // Hide after 3 seconds
    setTimeout(() => {
      statusMessage.parentElement.style.display = 'none';
    }, 3000);
  }
}







// Add event listeners for settings dropdown
document.addEventListener('DOMContentLoaded', function() {
  // Get settings dropdown elements
  const mainSettingsButton = document.getElementById('main-settings-button');
  const settingsDropdownMenu = document.getElementById('settings-dropdown-menu');

  // Get settings options
  const voiceSettingsOption = document.getElementById('voice-settings-option');
  const noteSettingsOption = document.getElementById('note-settings-option');
  const aiSettingsOption = document.getElementById('ai-settings-option');
  const screenshotSettingsOption = document.getElementById('screenshot-settings-option');

  // Get settings panels
  const voiceSettingsPanel = document.getElementById('voice-settings-panel');
  const noteSettingsPanel = document.getElementById('note-settings-panel');
  const aiSettingsPanel = document.getElementById('ai-settings-panel');
  const screenshotSettingsPanel = document.getElementById('screenshot-settings-panel');

  // Enhanced Settings Dropdown Toggle with visual feedback
  if (mainSettingsButton && settingsDropdownMenu) {
    mainSettingsButton.addEventListener('click', (event) => {
      event.stopPropagation(); // Prevent click from bubbling to document

      // Toggle dropdown visibility
      const isShowing = settingsDropdownMenu.classList.contains('show');
      settingsDropdownMenu.classList.toggle('show');

      // Toggle active state on button for visual feedback
      if (isShowing) {
        mainSettingsButton.classList.remove('active');
      } else {
        mainSettingsButton.classList.add('active');
      }
    });

    // Enhanced close dropdown when clicking outside
    document.addEventListener('click', (event) => {
      if (!mainSettingsButton.contains(event.target) && !settingsDropdownMenu.contains(event.target)) {
        settingsDropdownMenu.classList.remove('show');
        mainSettingsButton.classList.remove('active'); // Remove active state
      }
    });

    // Close dropdown when pressing Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && settingsDropdownMenu.classList.contains('show')) {
        settingsDropdownMenu.classList.remove('show');
        mainSettingsButton.classList.remove('active');
        mainSettingsButton.focus(); // Return focus to button
      }
    });
  }

  // Voice Settings Option
  if (voiceSettingsOption && voiceSettingsPanel) {
    voiceSettingsOption.addEventListener('click', () => {
      console.log('Popup: Voice settings panel opened');

      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide other panels
      if (noteSettingsPanel) {
        noteSettingsPanel.style.display = 'none';
        noteSettingsPanel.classList.add('hidden');
      }
      if (aiSettingsPanel) {
        aiSettingsPanel.style.display = 'none';
        aiSettingsPanel.classList.add('hidden');
      }
      if (screenshotSettingsPanel) {
        screenshotSettingsPanel.style.display = 'none';
        screenshotSettingsPanel.classList.add('hidden');
      }
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) {
        uiCustomizationPanel.style.display = 'none';
        uiCustomizationPanel.classList.add('hidden');
      }
      const helpInfoPanel = document.getElementById('help-info-panel');
      if (helpInfoPanel) {
        helpInfoPanel.style.display = 'none';
        helpInfoPanel.classList.add('hidden');
      }

      // Show voice settings panel
      voiceSettingsPanel.classList.remove('hidden');
      voiceSettingsPanel.style.display = 'flex';

      // Check if enhanced voice functions are available
      console.log('Popup: Enhanced voice functions available:', {
        getDefaultLanguagesForProvider: typeof window.getDefaultLanguagesForProvider,
        getLanguageDisplayName: typeof window.getLanguageDisplayName
      });

      // Call initVoiceSettings if it exists
      if (typeof initVoiceSettings === 'function') {
        console.log('Popup: Calling initVoiceSettings');
        initVoiceSettings(voiceSettingsPanel); // Pass the panel reference
      } else {
        console.error('Popup: initVoiceSettings function not found');
      }
    });
  }

  // Enhanced Note Settings Option
  if (noteSettingsOption && noteSettingsPanel) {
    noteSettingsOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide other panels
      if (voiceSettingsPanel) {
        voiceSettingsPanel.style.display = 'none';
        voiceSettingsPanel.classList.add('hidden');
      }
      if (aiSettingsPanel) {
        aiSettingsPanel.style.display = 'none';
        aiSettingsPanel.classList.add('hidden');
      }
      if (screenshotSettingsPanel) {
        screenshotSettingsPanel.style.display = 'none';
        screenshotSettingsPanel.classList.add('hidden');
      }
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) {
        uiCustomizationPanel.style.display = 'none';
        uiCustomizationPanel.classList.add('hidden');
      }
      const helpInfoPanel = document.getElementById('help-info-panel');
      if (helpInfoPanel) {
        helpInfoPanel.style.display = 'none';
        helpInfoPanel.classList.add('hidden');
      }

      // Show note settings panel
      noteSettingsPanel.classList.remove('hidden');
      noteSettingsPanel.style.display = 'flex';

      // Call initNoteSettings if it exists
      if (typeof initNoteSettings === 'function') {
        initNoteSettings(noteSettingsPanel); // Pass the panel reference
      }
    });
  }

  // UI Customization Settings Option
  const uiCustomizationOption = document.getElementById('ui-customization-option');
  const uiCustomizationPanel = document.getElementById('ui-customization-panel');

  if (uiCustomizationOption && uiCustomizationPanel) {
    uiCustomizationOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide other panels
      if (voiceSettingsPanel) {
        voiceSettingsPanel.style.display = 'none';
        voiceSettingsPanel.classList.add('hidden');
      }
      if (noteSettingsPanel) {
        noteSettingsPanel.style.display = 'none';
        noteSettingsPanel.classList.add('hidden');
      }
      if (aiSettingsPanel) {
        aiSettingsPanel.style.display = 'none';
        aiSettingsPanel.classList.add('hidden');
      }
      if (screenshotSettingsPanel) {
        screenshotSettingsPanel.style.display = 'none';
        screenshotSettingsPanel.classList.add('hidden');
      }
      const helpInfoPanel = document.getElementById('help-info-panel');
      if (helpInfoPanel) {
        helpInfoPanel.style.display = 'none';
        helpInfoPanel.classList.add('hidden');
      }

      // Show UI customization panel
      uiCustomizationPanel.classList.remove('hidden');
      uiCustomizationPanel.style.display = 'flex';

      // Initialize UI customization if it exists
      if (typeof initUICustomization === 'function') {
        initUICustomization(uiCustomizationPanel);
      }
    });
  }



  // Enhanced Screenshot Settings Option
  if (screenshotSettingsOption) {
    screenshotSettingsOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide all panels
      if (voiceSettingsPanel) {
        voiceSettingsPanel.style.display = 'none';
        voiceSettingsPanel.classList.add('hidden');
      }
      if (noteSettingsPanel) {
        noteSettingsPanel.style.display = 'none';
        noteSettingsPanel.classList.add('hidden');
      }
      if (aiSettingsPanel) {
        aiSettingsPanel.style.display = 'none';
        aiSettingsPanel.classList.add('hidden');
      }
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) {
        uiCustomizationPanel.style.display = 'none';
        uiCustomizationPanel.classList.add('hidden');
      }
      const helpInfoPanel = document.getElementById('help-info-panel');
      if (helpInfoPanel) {
        helpInfoPanel.style.display = 'none';
        helpInfoPanel.classList.add('hidden');
      }

      // Show screenshot settings panel
      if (screenshotSettingsPanel) {
        // Remove hidden class and set display to flex
        screenshotSettingsPanel.classList.remove('hidden');
        screenshotSettingsPanel.style.display = 'flex';

        // Panel is positioned via CSS

        // Initialize screenshot settings if the function exists
        if (typeof initScreenshotSettings === 'function') {
          initScreenshotSettings();
        }
      }
    });
  }

  // AI Settings Option
  if (aiSettingsOption && aiSettingsPanel) {
    aiSettingsOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide all other panels
      if (voiceSettingsPanel) {
        voiceSettingsPanel.style.display = 'none';
        voiceSettingsPanel.classList.add('hidden');
      }
      if (noteSettingsPanel) {
        noteSettingsPanel.style.display = 'none';
        noteSettingsPanel.classList.add('hidden');
      }
      if (screenshotSettingsPanel) {
        screenshotSettingsPanel.style.display = 'none';
        screenshotSettingsPanel.classList.add('hidden');
      }
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) {
        uiCustomizationPanel.style.display = 'none';
        uiCustomizationPanel.classList.add('hidden');
      }
      const helpInfoPanel = document.getElementById('help-info-panel');
      if (helpInfoPanel) {
        helpInfoPanel.style.display = 'none';
        helpInfoPanel.classList.add('hidden');
      }

      // Show AI settings panel
      aiSettingsPanel.classList.remove('hidden');
      aiSettingsPanel.style.display = 'flex';

      // Initialize AI settings if the function exists
      if (typeof initAiSettings === 'function') {
        initAiSettings();
      }
    });
  }

  // Help & Info Option
  const helpInfoOption = document.getElementById('help-info-option');
  const helpInfoPanel = document.getElementById('help-info-panel');

  if (helpInfoOption && helpInfoPanel) {
    helpInfoOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide all other panels
      if (voiceSettingsPanel) {
        voiceSettingsPanel.style.display = 'none';
        voiceSettingsPanel.classList.add('hidden');
      }
      if (noteSettingsPanel) {
        noteSettingsPanel.style.display = 'none';
        noteSettingsPanel.classList.add('hidden');
      }
      if (aiSettingsPanel) {
        aiSettingsPanel.style.display = 'none';
        aiSettingsPanel.classList.add('hidden');
      }
      if (screenshotSettingsPanel) {
        screenshotSettingsPanel.style.display = 'none';
        screenshotSettingsPanel.classList.add('hidden');
      }
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) {
        uiCustomizationPanel.style.display = 'none';
        uiCustomizationPanel.classList.add('hidden');
      }

      // Show help & info panel
      helpInfoPanel.classList.remove('hidden');
      helpInfoPanel.style.display = 'flex';

      // Add a fade-in animation
      helpInfoPanel.style.opacity = '0';
      setTimeout(() => {
        helpInfoPanel.style.opacity = '1';
      }, 10);
    });
  }


});

// Initialize AI settings function
async function initAiSettings() {
  console.log('Popup: Initializing AI settings...');

  try {
    // Update AI status display (universal)
    await updateUniversalAiStatus();

    // Load existing settings
    await loadAiSettings();

    // Set up event handlers
    setupAiEventHandlers();

  } catch (error) {
    console.error('Popup: Error initializing AI settings:', error);
  }
}

// Update AI status display
async function updateAiStatus() {
  console.log('Popup: Checking AI status...');

  try {
    const statusElement = document.getElementById('google-ai-status');
    const statusIndicator = document.getElementById('ai-status-indicator');
    const statusIcon = statusIndicator?.querySelector('.status-icon');
    const statusText = statusIndicator?.querySelector('.status-text');

    console.log('Popup: Found status elements:', {
      statusElement: !!statusElement,
      statusIndicator: !!statusIndicator,
      statusIcon: !!statusIcon,
      statusText: !!statusText
    });

    // Check if API key is configured using both storage methods
    try {
      let apiKey = null;

      // Try secure storage first
      if (window.secureApiStorage) {
        try {
          apiKey = await window.secureApiStorage.retrieveApiKey('googleAi');
          if (apiKey) {
            console.log('Popup: Found API key in secure storage');
          }
        } catch (secureError) {
          console.warn('Popup: Error retrieving from secure storage:', secureError);
        }
      }

      // Fallback to regular storage
      if (!apiKey) {
        const result = await chrome.storage.local.get(['googleAiApiKey']);
        apiKey = result.googleAiApiKey;
        if (apiKey) {
          console.log('Popup: Found API key in regular storage');
        }
      }

      if (apiKey && apiKey.trim()) {
        console.log('Popup: Google AI API key is configured');

        // API key is configured
        if (statusElement) {
          statusElement.textContent = 'Configured';
          statusElement.style.color = '#4CAF50';
        }

        if (statusIcon && statusText) {
          statusIcon.textContent = '✅';
          statusText.textContent = 'Google AI API key configured';
        }

        // Hide help message when API key is configured
        const helpMessage = document.getElementById('api-key-help');
        if (helpMessage) {
          helpMessage.style.display = 'none';
        }
      } else {
        console.log('Popup: Google AI API key not configured');

        // API key is not configured
        if (statusElement) {
          statusElement.textContent = 'Not configured';
          statusElement.style.color = '#4CAF50'; // Green as per user preference
        }

        if (statusIcon && statusText) {
          statusIcon.textContent = '⚙️';
          statusText.textContent = 'Configure your API key to enable AI features';
        }

        // Show help message when no API key is configured
        const helpMessage = document.getElementById('api-key-help');
        if (helpMessage) {
          helpMessage.style.display = 'block';
        }
      }
    } catch (error) {
      console.log('Popup: Error checking API key:', error);

      // Error checking API key
      if (statusElement) {
        statusElement.textContent = 'Error';
        statusElement.style.color = '#f44336';
      }

      if (statusIcon && statusText) {
        statusIcon.textContent = '❌';
        statusText.textContent = 'Error checking API key configuration';
      }
    }
  } catch (error) {
    console.error('Popup: Error updating AI status:', error);
  }
}

// Set up AI event handlers
function setupAiEventHandlers() {
  // Save AI settings button
  const saveAiSettingsBtn = document.getElementById('save-ai-settings');
  if (saveAiSettingsBtn) {
    saveAiSettingsBtn.addEventListener('click', async () => {
      await saveAiSettings();
    });
  }

  // API key visibility toggle (updated for universal AI)
  const toggleVisibilityBtn = document.getElementById('toggle-universal-ai-key-visibility');
  const apiKeyInput = document.getElementById('universal-ai-api-key');

  if (toggleVisibilityBtn && apiKeyInput) {
    toggleVisibilityBtn.addEventListener('click', () => {
      if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        toggleVisibilityBtn.textContent = '🙈';
        toggleVisibilityBtn.title = 'Hide API Key';
      } else {
        apiKeyInput.type = 'password';
        toggleVisibilityBtn.textContent = '👁️';
        toggleVisibilityBtn.title = 'Show API Key';
      }
    });
  }

  // API key input change handler
  if (apiKeyInput) {
    console.log('Popup: Setting up API key input change handler');
    apiKeyInput.addEventListener('input', () => {
      console.log('Popup: API key input changed, current value length:', apiKeyInput.value.length);
      // Update status when API key is entered/changed
      updateAiStatus();
    });
  } else {
    console.error('Popup: API key input element not found!');
  }

  // Test AI button
  const testAiButton = document.getElementById('test-ai-button');
  if (testAiButton) {
    testAiButton.addEventListener('click', async () => {
      await testAiConnection();
    });
  }



  // Detect and Save button (new primary button)
  const detectAndSaveButton = document.getElementById('detect-and-save-button');
  if (detectAndSaveButton) {
    detectAndSaveButton.addEventListener('click', async () => {
      await detectAndSaveApiKey();
    });
  }

  // Manual save button (now secondary)
  const manualSaveButton = document.getElementById('manual-save-button');
  if (manualSaveButton) {
    manualSaveButton.addEventListener('click', async () => {
      await saveApiKeyWithoutDetection();
    });
  }

  // Provider configuration save button
  const saveProviderConfigButton = document.getElementById('save-provider-config');
  if (saveProviderConfigButton) {
    saveProviderConfigButton.addEventListener('click', async () => {
      await saveProviderConfiguration();
    });
  }

  // Clear API key button
  const clearApiKeyButton = document.getElementById('clear-api-key-button');
  if (clearApiKeyButton) {
    clearApiKeyButton.addEventListener('click', async () => {
      await clearApiKey();
    });
  }
}







// Popup Script Loaded (v1.9 - Settings Dropdown Support)

// Initialize voice settings function
function initVoiceSettings(panel) {
  // Store the panel reference
  const voiceSettingsPanel = panel;

  // DOM Elements within the voice settings panel
  const providerSelect = document.getElementById('voice-provider'); // Assuming ID inside panel
  const apiKeyContainer = document.getElementById('api-key-container'); // Assuming ID inside panel
  const apiKeyInput = document.getElementById('voice-api-key'); // Assuming ID inside panel
  const togglePasswordButton = document.getElementById('toggle-password'); // Assuming ID inside panel
  const languageSelect = document.getElementById('voice-language'); // Assuming ID inside panel
  const silenceThresholdInput = document.getElementById('voice-silence-threshold'); // Assuming ID inside panel
  const silenceValue = document.getElementById('silence-value'); // Assuming ID inside panel
  const saveButton = document.getElementById('save-voice-settings'); // Assuming ID inside panel
  const resetButton = document.getElementById('reset-settings'); // Assuming ID inside panel
  const providerInfo = document.getElementById('provider-info'); // Assuming ID inside panel

  // Check if essential elements exist before proceeding
  if (!providerSelect || !apiKeyContainer || !apiKeyInput || !languageSelect || !saveButton) {
      console.error("Voice settings panel elements not found. Cannot initialize.");
      // Optionally hide the panel or show an error within it
      if (voiceSettingsPanel) voiceSettingsPanel.innerHTML = "<p style='color: red;'>Error loading settings UI.</p>";
      return;
  }

  // Provider language maps - Comprehensive language support
  const providerLanguages = {
    // Browser API - Common languages that work well with browser API
    browser: [
      'en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU',
      'zh-CN', 'ja-JP', 'ko-KR', 'nl-NL', 'pl-PL', 'sv-SE', 'tr-TR', 'ar-SA',
      'cs-CZ', 'da-DK', 'fi-FI', 'hi-IN', 'hu-HU', 'id-ID', 'nb-NO', 'pt-PT',
      'th-TH', 'vi-VN', 'el-GR', 'he-IL', 'ro-RO', 'sk-SK', 'uk-UA'
    ].sort(), // Sort alphabetically

    // Google Speech API - Comprehensive language support
    google: [
      // English variants
      'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-SG', 'en-ZA',
      // Spanish variants
      'es', 'es-ES', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN',
      'es-MX', 'es-NI', 'es-PA', 'es-PE', 'es-PR', 'es-PY', 'es-SV', 'es-US', 'es-UY', 'es-VE',
      // French variants
      'fr', 'fr-FR', 'fr-BE', 'fr-CA', 'fr-CH',
      // German variants
      'de', 'de-DE', 'de-AT', 'de-CH',
      // Italian variants
      'it', 'it-IT', 'it-CH',
      // Portuguese variants
      'pt', 'pt-BR', 'pt-PT',
      // Russian
      'ru', 'ru-RU',
      // Chinese variants
      'zh', 'zh-CN', 'zh-HK', 'zh-TW',
      // Japanese
      'ja', 'ja-JP',
      // Korean
      'ko', 'ko-KR',
      // Dutch variants
      'nl', 'nl-NL', 'nl-BE',
      // Polish
      'pl', 'pl-PL',
      // Swedish
      'sv', 'sv-SE',
      // Turkish
      'tr', 'tr-TR',
      // Arabic variants
      'ar', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB',
      'ar-MA', 'ar-OM', 'ar-QA', 'ar-SA', 'ar-TN',
      // Other European languages
      'cs', 'cs-CZ', 'da', 'da-DK', 'fi', 'fi-FI', 'el', 'el-GR', 'hu', 'hu-HU',
      'no', 'nb-NO', 'ro', 'ro-RO', 'sk', 'sk-SK', 'sl', 'sl-SI', 'bg', 'bg-BG',
      'hr', 'hr-HR', 'lt', 'lt-LT', 'lv', 'lv-LV', 'sr', 'sr-RS', // Added base codes
      // Asian languages
      'hi', 'hi-IN', 'id', 'id-ID', 'ms', 'ms-MY', 'fil', 'fil-PH', 'vi', 'vi-VN',
      'th', 'th-TH', 'bn', 'bn-IN', 'ta', 'ta-IN', 'te', 'te-IN', 'ml', 'ml-IN', 'kn', 'kn-IN', // Added base codes
      'mr', 'mr-IN', 'gu', 'gu-IN', 'pa', 'pa-IN', // Added base codes
      // Middle Eastern languages
      'fa', 'fa-IR', 'he', 'he-IL', 'ur', 'ur-PK'
    ].sort(), // Sort alphabetically

    // Azure Speech Service - Most comprehensive language support
    azure: [
      // English variants
      'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-SG', 'en-ZA',
      // Spanish variants
      'es', 'es-ES', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN',
      'es-MX', 'es-NI', 'es-PA', 'es-PE', 'es-PR', 'es-PY', 'es-SV', 'es-US', 'es-UY', 'es-VE',
      // French variants
      'fr', 'fr-FR', 'fr-BE', 'fr-CA', 'fr-CH',
      // German variants
      'de', 'de-DE', 'de-AT', 'de-CH',
      // Italian variants
      'it', 'it-IT', 'it-CH',
      // Portuguese variants
      'pt', 'pt-BR', 'pt-PT',
      // Russian
      'ru', 'ru-RU',
      // Chinese variants
      'zh', 'zh-CN', 'zh-HK', 'zh-TW',
      // Japanese
      'ja', 'ja-JP',
      // Korean
      'ko', 'ko-KR',
      // Dutch variants
      'nl', 'nl-NL', 'nl-BE',
      // Polish
      'pl', 'pl-PL',
      // Swedish
      'sv', 'sv-SE',
      // Turkish
      'tr', 'tr-TR',
      // Arabic variants
      'ar', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB',
      'ar-MA', 'ar-OM', 'ar-QA', 'ar-SA', 'ar-TN',
      // Other European languages
      'cs', 'cs-CZ', 'da', 'da-DK', 'fi', 'fi-FI', 'el', 'el-GR', 'hu', 'hu-HU',
      'no', 'nb-NO', 'ro', 'ro-RO', 'sk', 'sk-SK', 'sl', 'sl-SI', 'bg', 'bg-BG',
      'hr', 'hr-HR', 'lt', 'lt-LT', 'lv', 'lv-LV', 'sr', 'sr-RS', 'et', 'et-EE', 'gl', 'gl-ES', 'is', 'is-IS', // Added base codes
      // Asian languages
      'hi', 'hi-IN', 'id', 'id-ID', 'ms', 'ms-MY', 'fil', 'fil-PH', 'vi', 'vi-VN',
      'th', 'th-TH', 'bn', 'bn-IN', 'ta', 'ta-IN', 'te', 'te-IN', 'ml', 'ml-IN', 'kn', 'kn-IN', // Added base codes
      'mr', 'mr-IN', 'gu', 'gu-IN', 'pa', 'pa-IN', 'jv', 'jv-ID', // Added base codes
      // Middle Eastern languages
      'fa', 'fa-IR', 'he', 'he-IL', 'ur', 'ur-PK',
      // Other languages
      'uk', 'uk-UA', 'af', 'af-ZA', 'sq', 'sq-AL', 'hy', 'hy-AM', 'az', 'az-AZ', 'eu', 'eu-ES', // Added base codes
      'be', 'be-BY', 'bs', 'bs-BA', 'ca', 'ca-ES', 'cy', 'cy-GB', 'kk', 'kk-KZ', 'ky', 'ky-KG', 'lb', 'lb-LU', 'mk', 'mk-MK', // Added base codes
      'mt', 'mt-MT', 'mn', 'mn-MN' // Added base codes
    ].sort(), // Sort alphabetically

    // AssemblyAI - Focused on high-quality transcription for major languages
    assembly: [
      // English variants
      'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ',
      // Spanish variants
      'es', 'es-ES', 'es-MX', 'es-AR',
      // French variants
      'fr', 'fr-FR', 'fr-CA',
      // German variants
      'de', 'de-DE', 'de-AT', 'de-CH',
      // Italian variants
      'it', 'it-IT',
      // Portuguese variants
      'pt', 'pt-BR', 'pt-PT',
      // Dutch variants
      'nl', 'nl-NL', 'nl-BE',
      // Other major languages
      'hi', 'hi-IN', 'ja', 'ja-JP', 'zh', 'zh-CN', 'zh-TW', 'fi', 'fi-FI',
      'ko', 'ko-KR', 'pl', 'pl-PL', 'ru', 'ru-RU', 'tr', 'tr-TR', 'uk', 'uk-UA',
      'vi', 'vi-VN', 'ar', 'ar-SA', 'ar-EG', 'cs', 'cs-CZ', 'da', 'da-DK',
      'el', 'el-GR', 'he', 'he-IL', 'hu', 'hu-HU', 'id', 'id-ID', 'no', 'nb-NO',
      'ro', 'ro-RO', 'sv', 'sv-SE', 'th', 'th-TH', 'bg', 'bg-BG', 'hr', 'hr-HR',
      'sk', 'sk-SK', 'sl', 'sl-SI', 'sr', 'sr-RS', 'fa', 'fa-IR', 'ms', 'ms-MY',
      'fil', 'fil-PH', 'ta', 'ta-IN', 'bn', 'bn-IN', 'ur', 'ur-PK'
    ].sort() // Sort alphabetically
  };

  // Language display names - Comprehensive list
  const languageDisplayNames = {
    // English variants
    'en': 'English', 'en-US': 'English (US)', 'en-GB': 'English (UK)', 'en-AU': 'English (Australia)', 'en-CA': 'English (Canada)', 'en-IN': 'English (India)',
    'en-IE': 'English (Ireland)', 'en-NZ': 'English (New Zealand)', 'en-PH': 'English (Philippines)', 'en-SG': 'English (Singapore)', 'en-ZA': 'English (South Africa)',
    // Spanish variants
    'es': 'Spanish', 'es-ES': 'Spanish (Spain)', 'es-MX': 'Spanish (Mexico)', 'es-AR': 'Spanish (Argentina)', 'es-BO': 'Spanish (Bolivia)', 'es-CL': 'Spanish (Chile)',
    'es-CO': 'Spanish (Colombia)', 'es-CR': 'Spanish (Costa Rica)', 'es-DO': 'Spanish (Dominican Republic)', 'es-EC': 'Spanish (Ecuador)', 'es-GT': 'Spanish (Guatemala)',
    'es-HN': 'Spanish (Honduras)', 'es-NI': 'Spanish (Nicaragua)', 'es-PA': 'Spanish (Panama)', 'es-PE': 'Spanish (Peru)', 'es-PR': 'Spanish (Puerto Rico)',
    'es-PY': 'Spanish (Paraguay)', 'es-SV': 'Spanish (El Salvador)', 'es-US': 'Spanish (United States)', 'es-UY': 'Spanish (Uruguay)', 'es-VE': 'Spanish (Venezuela)',
    // French variants
    'fr': 'French', 'fr-FR': 'French (France)', 'fr-CA': 'French (Canada)', 'fr-BE': 'French (Belgium)', 'fr-CH': 'French (Switzerland)',
    // German variants
    'de': 'German', 'de-DE': 'German (Germany)', 'de-AT': 'German (Austria)', 'de-CH': 'German (Switzerland)',
    // Italian variants
    'it': 'Italian', 'it-IT': 'Italian (Italy)', 'it-CH': 'Italian (Switzerland)',
    // Portuguese variants
    'pt': 'Portuguese', 'pt-BR': 'Portuguese (Brazil)', 'pt-PT': 'Portuguese (Portugal)',
    // Russian
    'ru': 'Russian', 'ru-RU': 'Russian (Russia)',
    // Chinese variants
    'zh': 'Chinese', 'zh-CN': 'Chinese (Simplified)', 'zh-HK': 'Chinese (Hong Kong)', 'zh-TW': 'Chinese (Traditional)',
    // Japanese
    'ja': 'Japanese', 'ja-JP': 'Japanese (Japan)',
    // Korean
    'ko': 'Korean', 'ko-KR': 'Korean (South Korea)',
    // Dutch variants
    'nl': 'Dutch', 'nl-NL': 'Dutch (Netherlands)', 'nl-BE': 'Dutch (Belgium)',
    // Polish
    'pl': 'Polish', 'pl-PL': 'Polish (Poland)',
    // Swedish
    'sv': 'Swedish', 'sv-SE': 'Swedish (Sweden)',
    // Turkish
    'tr': 'Turkish', 'tr-TR': 'Turkish (Turkey)',
    // Arabic variants
    'ar': 'Arabic', 'ar-SA': 'Arabic (Saudi Arabia)', 'ar-EG': 'Arabic (Egypt)', 'ar-AE': 'Arabic (UAE)', 'ar-BH': 'Arabic (Bahrain)',
    'ar-DZ': 'Arabic (Algeria)', 'ar-IQ': 'Arabic (Iraq)', 'ar-JO': 'Arabic (Jordan)', 'ar-KW': 'Arabic (Kuwait)', 'ar-LB': 'Arabic (Lebanon)',
    'ar-MA': 'Arabic (Morocco)', 'ar-OM': 'Arabic (Oman)', 'ar-QA': 'Arabic (Qatar)', 'ar-TN': 'Arabic (Tunisia)',
    // Hindi
    'hi': 'Hindi', 'hi-IN': 'Hindi (India)',
    // Other European languages
    'cs': 'Czech', 'cs-CZ': 'Czech (Czech Republic)', 'da': 'Danish', 'da-DK': 'Danish (Denmark)', 'fi': 'Finnish', 'fi-FI': 'Finnish (Finland)',
    'el': 'Greek', 'el-GR': 'Greek (Greece)', 'hu': 'Hungarian', 'hu-HU': 'Hungarian (Hungary)', 'no': 'Norwegian', 'nb-NO': 'Norwegian (Norway)',
    'ro': 'Romanian', 'ro-RO': 'Romanian (Romania)', 'sk': 'Slovak', 'sk-SK': 'Slovak (Slovakia)', 'sl': 'Slovenian', 'sl-SI': 'Slovenian (Slovenia)',
    'bg': 'Bulgarian', 'bg-BG': 'Bulgarian (Bulgaria)', 'hr': 'Croatian', 'hr-HR': 'Croatian (Croatia)', 'lt': 'Lithuanian', 'lt-LT': 'Lithuanian (Lithuania)',
    'lv': 'Latvian', 'lv-LV': 'Latvian (Latvia)', 'sr': 'Serbian', 'sr-RS': 'Serbian (Serbia)', 'et': 'Estonian', 'et-EE': 'Estonian (Estonia)',
    'gl': 'Galician', 'gl-ES': 'Galician (Spain)', 'is': 'Icelandic', 'is-IS': 'Icelandic (Iceland)',
    // Asian languages
    'id': 'Indonesian', 'id-ID': 'Indonesian (Indonesia)', 'ms': 'Malay', 'ms-MY': 'Malay (Malaysia)', 'fil': 'Filipino', 'fil-PH': 'Filipino (Philippines)',
    'vi': 'Vietnamese', 'vi-VN': 'Vietnamese (Vietnam)', 'th': 'Thai', 'th-TH': 'Thai (Thailand)', 'bn': 'Bengali', 'bn-IN': 'Bengali (India)',
    'ta': 'Tamil', 'ta-IN': 'Tamil (India)', 'te': 'Telugu', 'te-IN': 'Telugu (India)', 'ml': 'Malayalam', 'ml-IN': 'Malayalam (India)',
    'kn': 'Kannada', 'kn-IN': 'Kannada (India)', 'mr': 'Marathi', 'mr-IN': 'Marathi (India)', 'gu': 'Gujarati', 'gu-IN': 'Gujarati (India)',
    'pa': 'Punjabi', 'pa-IN': 'Punjabi (India)', 'jv': 'Javanese', 'jv-ID': 'Javanese (Indonesia)',
    // Middle Eastern languages
    'fa': 'Persian', 'fa-IR': 'Persian (Iran)', 'he': 'Hebrew', 'he-IL': 'Hebrew (Israel)', 'ur': 'Urdu', 'ur-PK': 'Urdu (Pakistan)',
    // Other languages
    'uk': 'Ukrainian', 'uk-UA': 'Ukrainian (Ukraine)', 'af': 'Afrikaans', 'af-ZA': 'Afrikaans (South Africa)', 'sq': 'Albanian', 'sq-AL': 'Albanian (Albania)',
    'hy': 'Armenian', 'hy-AM': 'Armenian (Armenia)', 'az': 'Azerbaijani', 'az-AZ': 'Azerbaijani (Azerbaijan)', 'eu': 'Basque', 'eu-ES': 'Basque (Spain)',
    'be': 'Belarusian', 'be-BY': 'Belarusian (Belarus)', 'bs': 'Bosnian', 'bs-BA': 'Bosnian (Bosnia and Herzegovina)', 'ca': 'Catalan', 'ca-ES': 'Catalan (Spain)',
    'cy': 'Welsh', 'cy-GB': 'Welsh (United Kingdom)', 'kk': 'Kazakh', 'kk-KZ': 'Kazakh (Kazakhstan)', 'ky': 'Kyrgyz', 'ky-KG': 'Kyrgyz (Kyrgyzstan)',
    'lb': 'Luxembourgish', 'lb-LU': 'Luxembourgish (Luxembourg)', 'mk': 'Macedonian', 'mk-MK': 'Macedonian (North Macedonia)', 'mt': 'Maltese', 'mt-MT': 'Maltese (Malta)',
    'mn': 'Mongolian', 'mn-MN': 'Mongolian (Mongolia)',
    // Simplified auto-generation fallback
    'auto': (code) => {
        try {
            if (code.includes('-')) {
                const [langPart, regionPart] = code.split('-');
                const langName = (new Intl.DisplayNames(['en'], { type: 'language' })).of(langPart) || langPart;
                const regionName = (new Intl.DisplayNames(['en'], { type: 'region' })).of(regionPart.toUpperCase()) || regionPart;
                return `${langName} (${regionName})`;
            } else {
                return (new Intl.DisplayNames(['en'], { type: 'language' })).of(code) || code;
            }
        } catch (e) {
            // Fallback for environments without Intl.DisplayNames or invalid codes
            return code;
        }
    }
  };

  // Function to get display name, using map first, then auto-generation
  function getDisplayName(code) {
      if (languageDisplayNames[code] && typeof languageDisplayNames[code] === 'string') {
          return languageDisplayNames[code];
      }
      return languageDisplayNames['auto'](code); // Use the auto-generator
  }

  // Provider info text
  const providerInfoText = {
    browser: '<strong>Browser Speech API (Default)</strong><br>Uses your browser\'s built-in speech recognition. No API key required. Quality varies by browser and language support may be limited.',
    google: '<strong>Google Cloud Speech-to-Text</strong><br>High-accuracy transcription with extensive language support. Requires a Google Cloud API key. <a href="https://cloud.google.com/speech-to-text" target="_blank">Get API key →</a>',
    azure: '<strong>Microsoft Azure Speech</strong><br>Enterprise-grade speech recognition with advanced features. Requires an Azure Cognitive Services API key. <a href="https://azure.microsoft.com/en-us/services/cognitive-services/speech-services/" target="_blank">Get API key →</a>',
    assembly: '<strong>AssemblyAI</strong><br>Advanced speech recognition with features like summarization and sentiment analysis. Requires an AssemblyAI API key. <a href="https://www.assemblyai.com/" target="_blank">Get API key →</a>'
  };

  // === Functions defined within initVoiceSettings ===

  function loadVoiceSettings() {
      chrome.storage.local.get(['voiceApiPreferences', 'voiceApiKeys'], function(result) {
          const prefs = result.voiceApiPreferences || {}; // Default to empty object
          const keys = result.voiceApiKeys || {}; // Default to empty object

          // Set provider (default to browser if not set)
          const currentProvider = prefs.provider || 'browser';
          providerSelect.value = currentProvider;
          updateProviderUI(currentProvider, keys); // Pass keys to potentially fill API input

           // Set quality (default to standard)
          const qualitySelect = document.getElementById('voice-quality');
           if (qualitySelect) {
               qualitySelect.value = prefs.quality || 'standard';
           }

          // Set language AFTER updating options for the loaded provider
          updateLanguageOptions(currentProvider); // Populate language options first

          // Set the saved language after a small delay to ensure options are populated
          setTimeout(() => {
              if (prefs.language && languageSelect.querySelector(`option[value="${prefs.language}"]`)) {
                  languageSelect.value = prefs.language;
                  console.log(`Popup: Set language to saved value: ${prefs.language}`);
              } else if (languageSelect.options.length > 0) {
                  // If saved language is invalid for the provider, select the first option
                  languageSelect.selectedIndex = 0;
                  console.log(`Popup: Saved language ${prefs.language} not available, using: ${languageSelect.value}`);
              }
          }, 50);

          // Set checkboxes (provide defaults)
          setCheckbox('voice-punctuation', prefs.enablePunctuation !== false); // Default true
          setCheckbox('voice-capitalization', prefs.enableAutomaticCapitalization !== false); // Default true
          setCheckbox('voice-profanity-filter', prefs.enableProfanityFilter === true); // Default false
          setCheckbox('voice-interim-results', prefs.enableInterimResults !== false); // Default true
          setCheckbox('voice-auto-detect', prefs.autoDetectLanguage === true); // Default false
          setCheckbox('voice-background-noise-reduction', prefs.backgroundNoiseReduction === true); // Default false
          setCheckbox('voice-speaker-diarization', prefs.speakerDiarization === true); // Default false

          // Set silence threshold (default 15 seconds)
          if (silenceThresholdInput) {
              const defaultSeconds = 15;
              const savedMillis = prefs.silenceThreshold;
              // Use saved value if it's a valid number, otherwise default
              const seconds = (typeof savedMillis === 'number' && savedMillis >= 1000) ? Math.floor(savedMillis / 1000) : defaultSeconds;
              silenceThresholdInput.value = seconds;
              if (silenceValue) silenceValue.textContent = seconds + ' seconds';
          }
      });
  }

  function updateProviderUI(provider, apiKeys = {}) {
      // Show/hide API key input - properly handle the hidden class
      if (provider === 'browser') {
          apiKeyContainer.style.display = 'none';
          apiKeyContainer.classList.add('hidden');
      } else {
          apiKeyContainer.classList.remove('hidden');
          apiKeyContainer.style.display = 'block';
      }

      // Update provider info text
      if (providerInfo) {
          providerInfo.innerHTML = providerInfoText[provider] || 'Select a provider to see details.';
      }

      // Fill API key input if needed and available
      apiKeyInput.value = ''; // Clear first
      if (provider === 'google' && apiKeys.googleSpeechApiKey) {
          apiKeyInput.value = apiKeys.googleSpeechApiKey;
      } else if (provider === 'azure' && apiKeys.azureSpeechApiKey) {
          apiKeyInput.value = apiKeys.azureSpeechApiKey;
      } else if (provider === 'assembly' && apiKeys.assemblyAiApiKey) {
          apiKeyInput.value = apiKeys.assemblyAiApiKey;
      }

      // Ensure password visibility toggle is correct
      if (togglePasswordButton) {
          apiKeyInput.setAttribute('type', 'password');
          togglePasswordButton.innerHTML = '<span class="popup-icon">👁️</span>'; // Show icon
      }

      // Note: Language options are updated separately by calling updateLanguageOptions
      // Note: Feature availability (checkboxes etc.) is currently assumed to be universal,
      // but could be adjusted here based on the provider if needed in the future.
  }

  function updateLanguageOptions(provider) {
      console.log(`Popup: updateLanguageOptions called with provider: ${provider}`);
      const currentLang = languageSelect.value; // Save current selection

      // Clear existing options
      languageSelect.innerHTML = '';

      // Get languages for the provider (default to browser)
      const languages = providerLanguages[provider] || providerLanguages.browser;
      console.log(`Popup: Found ${languages.length} languages for provider ${provider}`);

      // Add new options sorted by display name
      languages
          .map(code => ({ code: code, name: getDisplayName(code) })) // Create objects with code and display name
          .sort((a, b) => a.name.localeCompare(b.name)) // Sort by display name
          .forEach(lang => {
              const option = document.createElement('option');
              option.value = lang.code;
              option.textContent = lang.name; // Use display name
              languageSelect.appendChild(option);
          });

      // Restore selection if it exists in new options
      if (languageSelect.querySelector(`option[value="${currentLang}"]`)) {
          languageSelect.value = currentLang;
      } else if (languageSelect.options.length > 0) {
          // If previous selection invalid, select the first available option (which is now sorted)
          languageSelect.selectedIndex = 0;
      }

      // Update the language count hint (optional)
      const formGroup = languageSelect.closest('.form-group');
      if (formGroup) {
          formGroup.setAttribute('data-count', `${languages.length} languages`);
      }

      // Show a status message about loaded languages (optional)
      // Use the main popup status bar for simplicity
      const providerName = provider.charAt(0).toUpperCase() + provider.slice(1);
      if (typeof showStatus === 'function') {
          showStatus(`${providerName} provider: ${languages.length} languages loaded.`, 'info');
      }
  }

  // This is the actual implementation, defined inside initVoiceSettings
  function saveVoiceSettings() {
      const provider = providerSelect.value;
      const language = languageSelect.value; // Value should be valid now
      const apiKey = apiKeyInput.value.trim();

      // Prepare preferences object
      const preferences = {
          provider: provider,
          language: language,
          enablePunctuation: getCheckboxValue('voice-punctuation', true),
          enableAutomaticCapitalization: getCheckboxValue('voice-capitalization', true),
          enableProfanityFilter: getCheckboxValue('voice-profanity-filter', false),
          enableInterimResults: getCheckboxValue('voice-interim-results', true),
          silenceThreshold: parseInt(silenceThresholdInput ? silenceThresholdInput.value : 15, 10) * 1000, // Base 10

          // Enhanced options
          autoDetectLanguage: getCheckboxValue('voice-auto-detect', false),
          backgroundNoiseReduction: getCheckboxValue('voice-background-noise-reduction', false),
          speakerDiarization: getCheckboxValue('voice-speaker-diarization', false),
          quality: document.getElementById('voice-quality') ? document.getElementById('voice-quality').value : 'standard'
      };

      // Get existing keys first to preserve other keys
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          let apiKeys = result.voiceApiKeys || {}; // Start with existing or empty

          // Update key for selected provider if provided
          if (provider !== 'browser') {
              // Store the key even if empty, allowing user to clear it
              if (provider === 'google') {
                  apiKeys.googleSpeechApiKey = apiKey;
              } else if (provider === 'azure') {
                  apiKeys.azureSpeechApiKey = apiKey;
              } else if (provider === 'assembly') {
                  apiKeys.assemblyAiApiKey = apiKey;
              }
          }

          // Save to storage
          chrome.storage.local.set({
              voiceApiPreferences: preferences,
              voiceApiKeys: apiKeys
          }, function() {
               if (chrome.runtime.lastError) {
                   console.error("Error saving voice settings:", chrome.runtime.lastError);
                   if (typeof showStatus === 'function') {
                       showStatus(`Error saving: ${chrome.runtime.lastError.message}`, 'error');
                   }
               } else {
                  if (typeof showStatus === 'function') {
                      showStatus('Voice settings saved!', 'success');
                  }
                  console.log('Voice settings saved successfully');
               }
          });
      });
  }

  // Helper functions used by initVoiceSettings
  function setCheckbox(id, checked) {
      const checkbox = document.getElementById(id);
      if (checkbox) checkbox.checked = !!checked; // Ensure boolean
  }

  function getCheckboxValue(id, defaultValue) {
      const checkbox = document.getElementById(id);
      // Ensure defaultValue is returned if element doesn't exist
      return checkbox ? checkbox.checked : defaultValue;
  }

  // === Event Listeners Setup inside initVoiceSettings ===
  providerSelect.addEventListener('change', function() {
      const provider = this.value;
      // Get current keys to pass to updateProviderUI
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          updateProviderUI(provider, result.voiceApiKeys || {});
          updateLanguageOptions(provider); // Update languages when provider changes
      });
  });

  if (silenceThresholdInput) {
      silenceThresholdInput.addEventListener('input', function() {
          if(silenceValue) silenceValue.textContent = this.value + ' seconds';
      });
  }

  if (togglePasswordButton) {
      togglePasswordButton.addEventListener('click', function() {
          const type = apiKeyInput.getAttribute('type') === 'password' ? 'text' : 'password';
          apiKeyInput.setAttribute('type', type);
          // Toggle icon based on type (using simple text fallback if icons fail)
          togglePasswordButton.innerHTML = type === 'password' ?
              '<span class="popup-icon">👁️</span><span class="sr-only">Show API Key</span>' :
              '<span class="popup-icon">🔒</span><span class="sr-only">Hide API Key</span>';
      });
  }

  saveButton.addEventListener('click', saveVoiceSettings);

  if (resetButton) {
      resetButton.addEventListener('click', function() {
          if (confirm('Reset all voice settings to default values? This will clear any entered API keys.')) {
              // Reset logic would go here
              console.log('Voice settings reset requested');
          }
      });
  }

  // === Initial Load Call inside initVoiceSettings ===
  loadVoiceSettings();

} // End of initVoiceSettings function

// Initialize screenshot settings function
function initScreenshotSettings() {
  console.log("Initializing screenshot settings");

  // Get DOM elements
  const storageLocalRadio = document.getElementById('storage-local');
  const storageDriveRadio = document.getElementById('storage-drive');
  const storageBothRadio = document.getElementById('storage-both');
  const driveFolderNameInput = document.getElementById('drive-folder-name');
  const privacyPrivateRadio = document.getElementById('privacy-private');
  const privacyVisibleRadio = document.getElementById('privacy-visible');
  const autoDeleteAfterInput = document.getElementById('auto-delete-after');
  const autoDeleteDisplay = document.getElementById('auto-delete-display');
  const autoDeleteTimeline = document.getElementById('auto-delete-timeline');
  const timelineMarker = document.querySelector('.timeline-marker');
  const askBeforeUploadCheckbox = document.getElementById('ask-before-upload');
  const imageQualitySelect = document.getElementById('image-quality');
  const enableHighDpiCheckbox = document.getElementById('enable-high-dpi');
  const enableAntiAliasingCheckbox = document.getElementById('enable-anti-aliasing');

  const driveSettingsSection = document.getElementById('drive-settings-section');
  const resetButton = document.getElementById('reset-screenshot-settings');
  const saveButton = document.getElementById('save-screenshot-settings');
  const statusMessage = document.getElementById('screenshot-status-message');

  // Check if essential elements exist
  if (!storageLocalRadio || !storageDriveRadio || !storageBothRadio || !saveButton) {
    console.error("Screenshot settings panel elements not found");
    return;
  }

  // Load settings
  loadScreenshotSettings();

  // Add event listeners
  storageLocalRadio.addEventListener('change', toggleDriveSettings);
  storageDriveRadio.addEventListener('change', toggleDriveSettings);
  storageBothRadio.addEventListener('change', toggleDriveSettings);

  autoDeleteAfterInput.addEventListener('input', updateAutoDeleteDisplay);

  resetButton.addEventListener('click', resetScreenshotSettings);
  saveButton.addEventListener('click', saveScreenshotSettings);

  /**
   * Toggles the visibility of Drive settings based on storage location
   */
  function toggleDriveSettings() {
    const showDriveSettings = storageDriveRadio.checked || storageBothRadio.checked;
    driveSettingsSection.style.display = showDriveSettings ? 'block' : 'none';
  }

  /**
   * Updates the auto-delete display and timeline marker
   */
  function updateAutoDeleteDisplay() {
    const days = parseInt(autoDeleteAfterInput.value);

    if (days === 0) {
      autoDeleteDisplay.textContent = 'Never';
      timelineMarker.style.display = 'none';
    } else {
      autoDeleteDisplay.textContent = days === 1 ? '1 day' : `${days} days`;
      timelineMarker.style.display = 'block';

      // Calculate position (0-100%)
      const maxDays = parseInt(autoDeleteAfterInput.max);
      const position = (days / maxDays) * 100;
      timelineMarker.style.left = `${position}%`;
    }
  }

  /**
   * Loads screenshot settings from storage
   */
  function loadScreenshotSettings() {
    chrome.storage.local.get(['screenshotStoragePrefs', 'screenshotQualityPrefs'], (result) => {
      const storagePrefs = result.screenshotStoragePrefs || getDefaultStorageSettings();
      const qualityPrefs = result.screenshotQualityPrefs || getDefaultQualitySettings();

      // Apply storage settings to form
      switch (storagePrefs.storageLocation) {
        case 'local':
          storageLocalRadio.checked = true;
          break;
        case 'drive':
          storageDriveRadio.checked = true;
          break;
        case 'both':
          storageBothRadio.checked = true;
          break;
      }

      driveFolderNameInput.value = storagePrefs.driveFolderName;

      if (storagePrefs.useAppDataFolder) {
        privacyPrivateRadio.checked = true;
      } else {
        privacyVisibleRadio.checked = true;
      }

      autoDeleteAfterInput.value = storagePrefs.autoDeleteAfter;
      askBeforeUploadCheckbox.checked = storagePrefs.askBeforeUpload;

      // Apply quality settings to form
      imageQualitySelect.value = qualityPrefs.quality;
      enableHighDpiCheckbox.checked = qualityPrefs.enableHighDpi;
      enableAntiAliasingCheckbox.checked = qualityPrefs.enableAntiAliasing;


      // Update UI state
      toggleDriveSettings();
      updateAutoDeleteDisplay();
    });
  }

  /**
   * Saves screenshot settings to storage
   */
  function saveScreenshotSettings() {
    // Get storage location
    let storageLocation = 'local';
    if (storageDriveRadio.checked) storageLocation = 'drive';
    if (storageBothRadio.checked) storageLocation = 'both';

    // Create storage preferences object
    const storagePrefs = {
      storageLocation: storageLocation,
      driveFolderName: driveFolderNameInput.value.trim() || 'Stashy Screenshots',
      useAppDataFolder: privacyPrivateRadio.checked,
      autoDeleteAfter: parseInt(autoDeleteAfterInput.value) || 0,
      askBeforeUpload: askBeforeUploadCheckbox.checked
    };

    // Create quality preferences object
    const qualityPrefs = {
      quality: imageQualitySelect.value,
      enableHighDpi: enableHighDpiCheckbox.checked,
      enableAntiAliasing: enableAntiAliasingCheckbox.checked,

    };

    // Save both preferences objects
    chrome.storage.local.set({
      screenshotStoragePrefs: storagePrefs,
      screenshotQualityPrefs: qualityPrefs
    }, () => {
      showStatusMessage(statusMessage, 'Screenshot settings saved successfully!', 'success');
    });
  }

  /**
   * Resets screenshot settings to defaults
   */
  function resetScreenshotSettings() {
    const defaultStorageSettings = getDefaultStorageSettings();
    const defaultQualitySettings = getDefaultQualitySettings();

    // Apply default storage settings to form
    storageLocalRadio.checked = defaultStorageSettings.storageLocation === 'local';
    storageDriveRadio.checked = defaultStorageSettings.storageLocation === 'drive';
    storageBothRadio.checked = defaultStorageSettings.storageLocation === 'both';

    driveFolderNameInput.value = defaultStorageSettings.driveFolderName;

    privacyPrivateRadio.checked = defaultStorageSettings.useAppDataFolder;
    privacyVisibleRadio.checked = !defaultStorageSettings.useAppDataFolder;

    autoDeleteAfterInput.value = defaultStorageSettings.autoDeleteAfter;
    askBeforeUploadCheckbox.checked = defaultStorageSettings.askBeforeUpload;

    // Apply default quality settings to form
    imageQualitySelect.value = defaultQualitySettings.quality;
    enableHighDpiCheckbox.checked = defaultQualitySettings.enableHighDpi;
    enableAntiAliasingCheckbox.checked = defaultQualitySettings.enableAntiAliasing;


    // Update UI state
    toggleDriveSettings();
    updateAutoDeleteDisplay();

    showStatusMessage(statusMessage, 'Settings reset to defaults', 'info');
  }

  /**
   * Returns default storage settings
   */
  function getDefaultStorageSettings() {
    return {
      storageLocation: 'local',
      driveFolderName: 'Stashy Screenshots',
      useAppDataFolder: true,
      autoDeleteAfter: 0,
      askBeforeUpload: true
    };
  }

  /**
   * Returns default quality settings
   */
  function getDefaultQualitySettings() {
    return {
      quality: 'standard',
      enableHighDpi: true,
      enableAntiAliasing: true,

    };
  }

  /**
   * Shows a status message in the screenshot settings panel
   * @param {HTMLElement} element - The status message element
   * @param {string} message - The message to display
   * @param {string} type - The type of message (success, error, info)
   */
  function showStatusMessage(element, message, type) {
    if (!element) return;

    const statusText = element.querySelector('#screenshot-status-text');
    if (!statusText) return;

    statusText.textContent = message;
    element.className = `status-message ${type}`;
    element.style.display = 'block';

    // Hide after 3 seconds
    setTimeout(() => {
      element.style.opacity = '0';
      setTimeout(() => {
        element.style.display = 'none';
        element.style.opacity = '1';
      }, 300);
    }, 3000);
  }
}

// Load AI settings (updated for universal AI)
async function loadAiSettings() {
  try {
    const result = await chrome.storage.local.get(['Stashy_ai_settings', 'universalAiConfig']);
    const settings = result.Stashy_ai_settings || {};
    const universalConfig = result.universalAiConfig;

    // Load API key using universal AI system
    const apiKeyInput = document.getElementById('universal-ai-api-key');
    if (apiKeyInput) {
      let apiKeyLoaded = false;

      // Try universal AI config first
      if (universalConfig && universalConfig.apiKey) {
        apiKeyInput.value = universalConfig.apiKey;
        console.log('Popup: Loaded API key from universal AI config');

        // Update provider display
        if (universalConfig.providerConfig) {
          updateProviderDisplay(universalConfig.providerId, universalConfig.providerConfig);

          // Initialize universal AI adapter with saved configuration
          if (window.universalAiAdapter) {
            try {
              await window.universalAiAdapter.initialize(
                universalConfig.providerId,
                universalConfig.apiKey,
                universalConfig.providerConfig
              );

              // Initialize migration bridge
              if (window.StashyAI && window.StashyAI.init) {
                await window.StashyAI.init();
                console.log('Popup: Migration bridge initialized with saved universal AI config');
              }
            } catch (initError) {
              console.warn('Popup: Error initializing universal AI with saved config:', initError.message);
            }
          }
        }

        apiKeyLoaded = true;
      }

      // Try secure storage as fallback
      if (!apiKeyLoaded && window.secureApiStorage) {
        try {
          const secureKey = await window.secureApiStorage.retrieveApiKey('universalAi');
          if (secureKey) {
            apiKeyInput.value = secureKey;
            console.log('Popup: Loaded API key from secure storage');
            apiKeyLoaded = true;
          }
        } catch (secureError) {
          console.warn('Popup: Error retrieving from secure storage:', secureError.message);
        }
      }

      // Legacy fallback for Google AI keys
      if (!apiKeyLoaded) {
        try {
          const fallbackResult = await chrome.storage.local.get(['googleAiApiKey']);
          if (fallbackResult.googleAiApiKey) {
            apiKeyInput.value = fallbackResult.googleAiApiKey;
            console.log('Popup: Loaded API key from legacy Google AI storage');

            // Try to migrate to universal system
            try {
              if (window.aiProviderDetector) {
                const detections = window.aiProviderDetector.detectProviderByPattern(fallbackResult.googleAiApiKey);
                if (detections.length > 0) {
                  const detection = detections[0];
                  await saveUniversalAiConfiguration(fallbackResult.googleAiApiKey, detection.providerId, detection.provider);
                  updateProviderDisplay(detection.providerId, detection.provider);
                  console.log('Popup: Successfully migrated legacy API key to universal system');
                }
              }
            } catch (migrationError) {
              console.warn('Popup: Migration to universal system failed:', migrationError.message);
            }
          } else {
            console.log('Popup: No API key found in any storage');
          }
        } catch (fallbackError) {
          console.error('Popup: Error loading from fallback storage:', fallbackError);
        }
      }
    }

    // Load model preference
    const modelPreference = document.getElementById('ai-model-preference');
    if (modelPreference && settings.model) {
      modelPreference.value = settings.model;
    }

    // Load auto-save setting
    const autoSave = document.getElementById('ai-auto-save');
    if (autoSave) {
      autoSave.checked = settings.autoSave !== false; // Default to true
    }

    // Load provider preference
    const providerPreference = document.getElementById('ai-provider-preference');
    if (providerPreference && settings.provider) {
      providerPreference.value = settings.provider;
    }

  } catch (error) {
    console.error('Popup: Error loading AI settings:', error);
  }
}

// Save AI settings
async function saveAiSettings() {
  try {
    const saveBtn = document.getElementById('save-ai-settings');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.textContent = '💾 Saving...';
    }

    // Get API key from UI
    const apiKeyInput = document.getElementById('google-ai-api-key');
    const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';

    // Validate API key
    if (!apiKey) {
      if (typeof showStatus === 'function') {
        showStatus('❌ Please enter a Google AI API key', 'error');
      }
      return;
    }

    // Validate API key format using secure storage
    if (window.secureApiStorage && !window.secureApiStorage.validateApiKeyFormat(apiKey, 'googleAi')) {
      if (typeof showStatus === 'function') {
        showStatus('❌ Invalid Google AI API key format. Please check your key.', 'error');
      }
      return;
    }

    // Test API key before saving
    if (typeof showStatus === 'function') {
      showStatus('🔍 Validating API key...', 'info');
    }

    try {
      // Test the API key by making a simple request
      const testResponse = await chrome.runtime.sendMessage({
        action: 'googleAiRequest',
        model: 'models',
        requestBody: null
      });

      if (!testResponse || !testResponse.success) {
        throw new Error(testResponse?.error || 'API key validation failed');
      }
    } catch (testError) {
      console.error('Popup: API key validation failed:', testError);
      if (typeof showStatus === 'function') {
        showStatus('❌ Invalid API key: ' + testError.message, 'error');
      }
      return;
    }

    // Get other settings from UI
    const settings = {};

    const modelPreference = document.getElementById('ai-model-preference');
    if (modelPreference) {
      settings.model = modelPreference.value;
    }

    const autoSave = document.getElementById('ai-auto-save');
    if (autoSave) {
      settings.autoSave = autoSave.checked;
    }

    const providerPreference = document.getElementById('ai-provider-preference');
    if (providerPreference) {
      settings.provider = providerPreference.value;
    }

    // Save API key using both secure storage and fallback
    let secureStorageSuccess = false;
    let secureStorageError = null;

    // Try secure storage first
    if (window.secureApiStorage) {
      try {
        await window.secureApiStorage.storeApiKey('googleAi', apiKey);
        console.log('Popup: API key stored securely');
        secureStorageSuccess = true;
      } catch (storageError) {
        secureStorageError = storageError.message;
        console.warn('Popup: Secure storage failed, using fallback:', storageError.message);
      }
    }

    // Always store in regular storage as fallback (for background script compatibility)
    await chrome.storage.local.set({ 'googleAiApiKey': apiKey });
    console.log('Popup: API key stored in fallback storage');

    // Verify storage
    const verification = await chrome.storage.local.get(['googleAiApiKey']);
    console.log('Popup: Verification - API key stored:', !!verification.googleAiApiKey);

    // Log final status
    if (secureStorageSuccess) {
      console.log('Popup: API key stored in both secure and fallback storage');
    } else {
      console.log('Popup: API key stored in fallback storage only' + (secureStorageError ? ` (secure storage error: ${secureStorageError})` : ''));
    }

    // Save other settings
    await chrome.storage.local.set({
      'Stashy_ai_settings': settings
    });

    // Update status display
    await updateAiStatus();

    if (typeof showStatus === 'function') {
      showStatus('✅ AI settings saved securely!', 'success');
    }

  } catch (error) {
    console.error('Popup: Error saving AI settings:', error);
    if (typeof showStatus === 'function') {
      showStatus('❌ Error saving AI settings', 'error');
    }
  } finally {
    const saveBtn = document.getElementById('save-ai-settings');
    if (saveBtn) {
      saveBtn.disabled = false;
      saveBtn.textContent = '💾 Save Settings';
    }
  }
}



// Test AI connection function (updated for universal AI)
async function testAiConnection() {
  const testButton = document.getElementById('test-ai-button');
  const testResult = document.getElementById('ai-test-result');

  if (testButton) {
    testButton.disabled = true;
    testButton.textContent = '🔄 Testing...';
  }

  if (testResult) {
    testResult.style.display = 'block';
    testResult.innerHTML = '<div style="color: #007bff;">Testing AI connection...</div>';
  }

  try {
    // Check for API key using universal system
    let apiKey = null;
    let providerConfig = null;

    // Method 1: Check current input field
    const apiKeyInput = document.getElementById('universal-ai-api-key');
    const inputValue = apiKeyInput ? apiKeyInput.value.trim() : '';
    console.log('Popup: Input field value length:', inputValue.length);

    if (inputValue) {
      apiKey = inputValue;
      console.log('Popup: Using API key from input field');
    } else {
      // Method 2: Try universal AI config
      const result = await chrome.storage.local.get(['universalAiConfig']);
      const universalConfig = result.universalAiConfig;

      if (universalConfig && universalConfig.apiKey) {
        apiKey = universalConfig.apiKey;
        providerConfig = universalConfig.providerConfig;
        console.log('Popup: Retrieved API key from universal AI config');
      } else {
        // Method 3: Try secure storage
        if (window.secureApiStorage) {
          try {
            apiKey = await window.secureApiStorage.retrieveApiKey('universalAi');
            if (apiKey) {
              console.log('Popup: Retrieved API key from secure storage');
            }
          } catch (secureError) {
            console.warn('Popup: Error retrieving from secure storage:', secureError);
          }
        }

        // Method 4: Legacy fallback to Google AI storage
        if (!apiKey) {
          const legacyResult = await chrome.storage.local.get(['googleAiApiKey']);
          apiKey = legacyResult.googleAiApiKey;
          if (apiKey) {
            console.log('Popup: Retrieved API key from legacy Google AI storage');
          } else {
            console.log('Popup: No API key found in any storage');
          }
        }
      }
    }

    if (!apiKey || !apiKey.trim()) {
      // Show helpful message with instructions
      if (testResult) {
        testResult.innerHTML = `
          <div style="color: #dc3545; background-color: #f8d7da; padding: 12px; border-radius: 4px; border: 1px solid #f5c6cb;">
            <h4 style="margin: 0 0 10px 0;">❌ No API Key Found</h4>
            <div style="margin-bottom: 10px;">
              No Google AI API key is configured for this Chrome profile/account.
            </div>
            <div style="font-size: 12px; color: #721c24;">
              <strong>To fix this:</strong><br>
              1. Get your API key from <a href="https://aistudio.google.com/app/apikey" target="_blank" style="color: #721c24;">Google AI Studio</a><br>
              2. Paste it in the "Google AI API Key" field above<br>
              3. Click "Save Settings"<br>
              4. Try testing again
            </div>
          </div>
        `;
      }
      throw new Error('No API key configured. Please enter your Google AI API key first.');
    }

    console.log('Popup: Testing AI connection with API key configured');

    // Test using universal AI system
    let testResponse = null;
    let testMethod = 'universal';

    // Try universal AI adapter first with minimal test
    if (window.universalAiAdapter && window.universalAiAdapter.isReady()) {
      let currentProvider = null;
      try {
        console.log('Popup: Testing with universal AI adapter');

        currentProvider = window.universalAiAdapter.getCurrentProvider();
        const providerId = currentProvider?.id;

        // Provider-specific rate limit checking and optimization
        const rateLimit = localStorage.getItem(`stashy_${providerId}_rate_limit`);
        const now = Date.now();

        if (rateLimit && (now - parseInt(rateLimit)) < 300000) { // 5 minutes cooldown
          console.log(`Popup: Skipping ${providerId} test due to recent rate limit`);
          testResponse = {
            success: true,
            data: { response: `Test skipped - ${currentProvider.config?.displayName} configured successfully (avoiding rate limits)` },
            provider: currentProvider,
            skipped: true
          };
          testMethod = 'universal-skipped';
        } else {
          // Provider-specific test options
          let testOptions = { temperature: 0.1 };

          switch (providerId) {
            case 'google':
              testOptions = {
                maxTokens: 5, // Minimal for Google AI
                temperature: 0.1,
                model: 'gemini-1.5-flash' // Force flash model
              };
              break;
            case 'openai':
              testOptions = {
                maxTokens: 10, // Small for OpenAI
                temperature: 0.1,
                model: 'gpt-3.5-turbo' // Most cost-effective
              };
              break;
            case 'anthropic':
              testOptions = {
                maxTokens: 10, // Small for Anthropic
                temperature: 0.1,
                model: 'claude-3-haiku-20240307' // Most cost-effective
              };
              break;
            case 'cohere':
              testOptions = {
                maxTokens: 5, // Very small for Cohere
                temperature: 0.1
              };
              break;
            case 'huggingface':
              testOptions = {
                maxTokens: 5, // Very small for HuggingFace
                temperature: 0.1
              };
              break;
            default:
              testOptions = {
                maxTokens: 10,
                temperature: 0.1
              };
          }

          console.log(`Popup: Using optimized test options for ${providerId}:`, testOptions);
          const testText = await window.universalAiAdapter.generateText('Hi', testOptions);

          testResponse = {
            success: true,
            data: { response: testText },
            provider: currentProvider
          };
          testMethod = 'universal';
          console.log(`Popup: ${providerId} test successful`);
        }
      } catch (universalError) {
        console.error('Popup: Universal AI test failed with detailed error:', {
          message: universalError.message,
          stack: universalError.stack,
          providerId: currentProvider?.id,
          providerName: currentProvider?.config?.displayName
        });

        // If it's a rate limit error, remember it for any provider
        if (universalError.message.includes('429') || universalError.message.includes('quota') || universalError.message.includes('rate')) {
          if (currentProvider && currentProvider.id) {
            localStorage.setItem(`stashy_${currentProvider.id}_rate_limit`, Date.now().toString());
            console.log(`Popup: Stored rate limit timestamp for ${currentProvider.id}`);
          }
        }

        // Store the detailed error for better debugging
        testResponse = {
          success: false,
          error: universalError.message,
          provider: currentProvider,
          detailedError: {
            originalMessage: universalError.message,
            providerId: currentProvider?.id,
            providerName: currentProvider?.config?.displayName
          }
        };
      }
    }

    // Only fallback to legacy Google AI test if we have a Google AI key and universal failed
    if ((!testResponse || !testResponse.success) && providerConfig && providerConfig.name === 'Google') {
      try {
        console.log('Popup: Falling back to legacy Google AI test for Google AI key');
        const legacyResponse = await chrome.runtime.sendMessage({
          action: 'googleAiRequest',
          model: 'models',
          requestBody: null
        });
        testResponse = legacyResponse;
        testMethod = 'legacy';
      } catch (legacyError) {
        console.error('Popup: Legacy AI test also failed:', legacyError.message);
        testResponse = { success: false, error: legacyError.message };
      }
    }

    // If we still don't have a successful response and we're using a non-Google provider,
    // make sure we preserve the original error from the universal adapter
    if ((!testResponse || !testResponse.success) && window.universalAiAdapter && window.universalAiAdapter.isReady()) {
      const currentProvider = window.universalAiAdapter.getCurrentProvider();
      if (currentProvider && currentProvider.id !== 'google') {
        // Don't fallback to Google AI for non-Google providers
        console.log(`Popup: Preserving ${currentProvider.config.displayName} error, not falling back to Google AI`);
      }
    }

    console.log('Popup: AI test response:', testResponse);

    if (testResponse && testResponse.success) {
      const providerInfo = testResponse.provider || { config: { displayName: 'Google AI (Legacy)' } };
      const responseText = testResponse.data?.response || '';
      const modelCount = testResponse.data?.models?.length || 0;
      const wasSkipped = testResponse.skipped || testMethod === 'universal-skipped';

      if (testResult) {
        testResult.innerHTML = `
          <div style="color: #28a745; background-color: #d4edda; padding: 12px; border-radius: 4px; border: 1px solid #c3e6cb;">
            <h4 style="margin: 0 0 10px 0;">✅ AI ${wasSkipped ? 'Configured Successfully' : 'Connection Successful!'}</h4>
            <div style="margin-bottom: 10px;">
              <strong>Provider:</strong> ${providerInfo.config?.displayName || 'Unknown'}<br>
              <strong>Test Method:</strong> ${testMethod === 'universal' ? 'Universal AI System' : testMethod === 'universal-skipped' ? 'Universal AI (Test Skipped)' : 'Legacy Google AI'}<br>
              ${modelCount > 0 ? `<strong>Models Available:</strong> ${modelCount}<br>` : ''}
              ${responseText ? `<strong>Test Response:</strong> "${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}"` : ''}
            </div>
            <div style="font-size: 12px; color: #155724;">
              ${wasSkipped ?
                'AI provider configured successfully. Test was skipped to avoid rate limits - your AI features are ready to use.' :
                'All AI features are working correctly and ready to use.'
              }
            </div>
          </div>
        `;
      }
      console.log('Popup: AI connection test successful');
    } else {
      const errorMsg = testResponse?.error || 'Unknown error';

      // Get the actual provider being tested
      let providerName = 'AI Provider';
      if (window.universalAiAdapter && window.universalAiAdapter.isReady()) {
        const currentProvider = window.universalAiAdapter.getCurrentProvider();
        if (currentProvider && currentProvider.config) {
          providerName = currentProvider.config.displayName;
        }
      }

      if (testResult) {
        testResult.innerHTML = `
          <div style="color: #dc3545; background-color: #f8d7da; padding: 12px; border-radius: 4px; border: 1px solid #f5c6cb;">
            <h4 style="margin: 0 0 10px 0;">❌ ${providerName} Connection Failed</h4>
            <div style="margin-bottom: 10px;">
              <strong>Provider:</strong> ${providerName}<br>
              <strong>Error:</strong> ${errorMsg}
            </div>
            <div style="font-size: 12px; color: #721c24;">
              <strong>Troubleshooting:</strong><br>
              • Verify your ${providerName} API key is correct and active<br>
              • Check your internet connection<br>
              • Ensure the API key has necessary permissions for ${providerName}<br>
              • Try using "🔍 Detect & Save" to reconfigure your provider<br>
              • Check ${providerName} service status and rate limits
            </div>
          </div>
        `;
      }
      console.error(`Popup: ${providerName} connection test failed:`, errorMsg);
    }

  } catch (error) {
    console.error('Popup: AI connection test error:', error);
    if (testResult) {
      testResult.innerHTML = '<div style="color: #dc3545; background-color: #f8d7da; padding: 8px; border-radius: 4px;">❌ Error: ' + error.message + '</div>';
    }
  } finally {
    if (testButton) {
      testButton.disabled = false;
      testButton.textContent = '🧪 Test Connection';
    }
  }
}

// Removed usage statistics function - no longer needed

// Removed security audit function - no longer needed

// Removed debug storage function - no longer needed

// Save API key function
async function saveApiKey() {
  const saveButton = document.getElementById('manual-save-button');
  const testResult = document.getElementById('ai-test-result');

  if (saveButton) {
    saveButton.disabled = true;
    saveButton.textContent = '💾 Saving...';
  }

  try {
    if (testResult) {
      testResult.style.display = 'block';
      testResult.innerHTML = '<div style="color: #007bff;">Manually saving API key...</div>';
    }

    // Get API key from input field
    const apiKeyInput = document.getElementById('google-ai-api-key');
    const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';

    console.log('Popup: Manual save - API key length:', apiKey.length);

    if (!apiKey) {
      throw new Error('Please enter an API key in the input field first.');
    }

    // Validate API key format
    if (window.secureApiStorage && !window.secureApiStorage.validateApiKeyFormat(apiKey, 'googleAi')) {
      throw new Error('Invalid Google AI API key format. Please check your key.');
    }

    // Save to both storage methods
    let secureStorageSuccess = false;
    let secureStorageError = null;

    if (window.secureApiStorage) {
      try {
        await window.secureApiStorage.storeApiKey('googleAi', apiKey);
        console.log('Popup: Manual save - stored in secure storage');
        secureStorageSuccess = true;
      } catch (storageError) {
        secureStorageError = storageError.message;
        console.warn('Popup: Manual save - secure storage failed:', storageError.message);
      }
    }

    // Always store in regular storage as fallback
    await chrome.storage.local.set({ 'googleAiApiKey': apiKey });
    console.log('Popup: Manual save - stored in fallback storage');

    // Verify storage
    const verification = await chrome.storage.local.get(['googleAiApiKey']);
    console.log('Popup: Manual save - verification:', !!verification.googleAiApiKey);

    // Update status
    await updateAiStatus();

    if (testResult) {
      testResult.innerHTML = `
        <div style="color: #28a745; background-color: #d4edda; padding: 12px; border-radius: 4px; border: 1px solid #c3e6cb;">
          <h4 style="margin: 0 0 10px 0;">✅ API Key Saved Successfully</h4>
          <div style="font-size: 12px;">
            • Secure storage: ${secureStorageSuccess ? 'Success' : 'Failed' + (secureStorageError ? ` (${secureStorageError})` : '')}<br>
            • Fallback storage: Success<br>
            • Verification: ${verification.googleAiApiKey ? 'Confirmed' : 'Failed'}<br>
            • Status: ${secureStorageSuccess ? 'Fully secure' : 'Using fallback (still functional)'}
          </div>
        </div>
      `;
    }

  } catch (error) {
    console.error('Popup: Manual save error:', error);
    if (testResult) {
      testResult.innerHTML = `
        <div style="color: #dc3545; background-color: #f8d7da; padding: 12px; border-radius: 4px; border: 1px solid #f5c6cb;">
          <h4 style="margin: 0 0 10px 0;">❌ Manual Save Failed</h4>
          <div>${error.message}</div>
        </div>
      `;
    }
  } finally {
    if (saveButton) {
      saveButton.disabled = false;
      saveButton.textContent = '💾 Save';
    }
  }
}

// Clear API key function
async function clearApiKey() {
  const clearButton = document.getElementById('clear-api-key-button');
  const testResult = document.getElementById('ai-test-result');

  // Confirm action
  const confirmed = confirm(
    'Are you sure you want to clear your API key and disable AI features?\n\n' +
    'This will:\n' +
    '• Remove your API key from secure storage\n' +
    '• Remove your API key from regular storage\n' +
    '• Disable all AI features\n' +
    '• Clear the input field\n\n' +
    'You can re-enable AI features by entering a new API key.'
  );

  if (!confirmed) {
    return;
  }

  if (clearButton) {
    clearButton.disabled = true;
    clearButton.textContent = '🗑️ Clearing...';
  }

  try {
    if (testResult) {
      testResult.style.display = 'block';
      testResult.innerHTML = '<div style="color: #007bff;">Clearing API key and disabling AI features...</div>';
    }

    // Clear from secure storage (both universal and legacy)
    let secureStorageCleared = false;
    if (window.secureApiStorage) {
      try {
        await window.secureApiStorage.removeApiKey('universalAi');
        await window.secureApiStorage.removeApiKey('googleAi'); // Legacy cleanup
        console.log('Popup: API key removed from secure storage');
        secureStorageCleared = true;
      } catch (secureError) {
        console.warn('Popup: Error removing from secure storage:', secureError.message);
      }
    }

    // Clear from regular storage (both universal and legacy)
    await chrome.storage.local.remove(['universalAiConfig', 'googleAiApiKey']);
    console.log('Popup: API key removed from regular storage');

    // Clear input field
    const apiKeyInput = document.getElementById('universal-ai-api-key');
    if (apiKeyInput) {
      apiKeyInput.value = '';
    }

    // Reset provider display
    const providerIcon = document.getElementById('detected-provider-icon');
    const providerName = document.getElementById('detected-provider-name');
    const providerStatus = document.getElementById('ai-provider-status');

    if (providerIcon) providerIcon.textContent = '🤖';
    if (providerName) providerName.textContent = 'AI Provider';
    if (providerStatus) {
      providerStatus.textContent = 'Not configured';
      providerStatus.className = 'provider-status not-configured';
    }

    // Reset universal AI adapter
    if (window.universalAiAdapter) {
      window.universalAiAdapter.reset();
    }

    // Update AI status
    await updateAiStatus();

    // Verify removal
    const verification = await chrome.storage.local.get(['googleAiApiKey']);
    const isCleared = !verification.googleAiApiKey;

    if (testResult) {
      testResult.innerHTML = `
        <div style="color: #28a745; background-color: #d4edda; padding: 12px; border-radius: 4px; border: 1px solid #c3e6cb;">
          <h4 style="margin: 0 0 10px 0;">✅ API Key Cleared Successfully</h4>
          <div style="font-size: 12px;">
            • Secure storage: ${secureStorageCleared ? 'Cleared' : 'Not found'}<br>
            • Regular storage: Cleared<br>
            • Input field: Cleared<br>
            • AI features: Disabled<br>
            • Verification: ${isCleared ? 'Confirmed' : 'Warning - some data may remain'}
          </div>
          <div style="margin-top: 10px; font-size: 12px; color: #155724;">
            <strong>AI features are now disabled.</strong> To re-enable, enter a new API key and click Save.
          </div>
        </div>
      `;
    }

  } catch (error) {
    console.error('Popup: Error clearing API key:', error);
    if (testResult) {
      testResult.innerHTML = `
        <div style="color: #dc3545; background-color: #f8d7da; padding: 12px; border-radius: 4px; border: 1px solid #f5c6cb;">
          <h4 style="margin: 0 0 10px 0;">❌ Error Clearing API Key</h4>
          <div>${error.message}</div>
        </div>
      `;
    }
  } finally {
    if (clearButton) {
      clearButton.disabled = false;
      clearButton.textContent = '🗑️ Clear & Disable';
    }
  }
}

// Universal AI Provider Detection Functions

/**
 * Detects AI provider and saves the API key
 */
async function detectAndSaveApiKey() {
  const detectButton = document.getElementById('detect-and-save-button');
  const progressDiv = document.getElementById('detection-progress');
  const resultsDiv = document.getElementById('provider-detection-results');
  const testResult = document.getElementById('ai-test-result');

  // Reset UI
  if (testResult) {
    testResult.style.display = 'none';
  }

  try {
    // Disable button and show progress
    if (detectButton) {
      detectButton.disabled = true;
      detectButton.textContent = '🔄 Detecting...';
    }

    // Get API key from input
    const apiKeyInput = document.getElementById('universal-ai-api-key');
    const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';

    if (!apiKey) {
      throw new Error('Please enter an API key first');
    }

    // Show progress
    if (progressDiv) {
      progressDiv.style.display = 'block';
    }

    // Load AI provider detector if not already loaded
    if (!window.aiProviderDetector) {
      console.log('Popup: Loading AI provider detector...');
      // The detector should be loaded via manifest, but let's check
      await new Promise(resolve => setTimeout(resolve, 100));

      if (!window.aiProviderDetector) {
        throw new Error('AI provider detector not loaded. Please refresh the page.');
      }
    }

    // Detect and validate provider (with minimal validation to avoid rate limits)
    console.log('Popup: Starting provider detection...');
    const detectionResult = await window.aiProviderDetector.detectAndValidateProvider(apiKey, {
      skipValidation: true, // Skip validation during detection to avoid rate limits
      testAllMatches: false
    });

    // Hide progress
    if (progressDiv) {
      progressDiv.style.display = 'none';
    }

    if (!detectionResult.success) {
      throw new Error(detectionResult.error || 'Provider detection failed');
    }

    // Show detection results
    if (resultsDiv) {
      resultsDiv.style.display = 'block';
      const detectedProviderDisplay = document.getElementById('detected-provider-display');
      if (detectedProviderDisplay) {
        detectedProviderDisplay.textContent = detectionResult.providerConfig.displayName;
      }

      // Show validation progress
      const validationResult = document.getElementById('validation-result');
      if (validationResult) {
        validationResult.style.display = 'block';
      }
    }

    // Update provider display
    updateProviderDisplay(detectionResult.detectedProvider, detectionResult.providerConfig);

    // Save the configuration
    await saveUniversalAiConfiguration(apiKey, detectionResult.detectedProvider, detectionResult.providerConfig);

    // Initialize the universal AI adapter
    if (window.universalAiAdapter) {
      await window.universalAiAdapter.initialize(
        detectionResult.detectedProvider,
        apiKey,
        detectionResult.providerConfig
      );

      // Initialize the migration bridge to use universal mode
      if (window.StashyAI && window.StashyAI.init) {
        await window.StashyAI.init();
        console.log('Popup: Migration bridge initialized with universal AI');
      }
    }

    // Update status
    await updateUniversalAiStatus();

    // Show success message
    if (testResult) {
      testResult.style.display = 'block';
      testResult.innerHTML = `
        <div style="color: #28a745; background-color: #d4edda; padding: 12px; border-radius: 4px; border: 1px solid #c3e6cb;">
          <h4 style="margin: 0 0 10px 0;">✅ AI Provider Detected & Configured</h4>
          <div style="margin-bottom: 10px;">
            <strong>Provider:</strong> ${detectionResult.providerConfig.displayName}<br>
            <strong>Confidence:</strong> ${Math.round(detectionResult.confidence * 100)}%<br>
            <strong>Validation:</strong> ${detectionResult.finalValidation ? 'Successful' : 'Skipped'}
          </div>
          <div style="font-size: 12px; color: #155724;">
            Your API key has been securely stored and all AI features are now enabled.
          </div>
        </div>
      `;
    }

    console.log('Popup: Provider detection and configuration completed successfully');

  } catch (error) {
    console.error('Popup: Error during provider detection:', error);

    // Hide progress and results
    if (progressDiv) {
      progressDiv.style.display = 'none';
    }
    if (resultsDiv) {
      resultsDiv.style.display = 'none';
    }

    // Show error message
    if (testResult) {
      testResult.style.display = 'block';
      testResult.innerHTML = `
        <div style="color: #dc3545; background-color: #f8d7da; padding: 12px; border-radius: 4px; border: 1px solid #f5c6cb;">
          <h4 style="margin: 0 0 10px 0;">❌ Provider Detection Failed</h4>
          <div style="margin-bottom: 10px;">
            ${error.message}
          </div>
          <div style="font-size: 12px; color: #721c24;">
            <strong>Troubleshooting:</strong><br>
            • Verify your API key is correct and complete<br>
            • Check that your API key has the necessary permissions<br>
            • Ensure you have an active internet connection<br>
            • Try using the "💾 Save Only" button if detection continues to fail
          </div>
        </div>
      `;
    }

    if (typeof showStatus === 'function') {
      showStatus('❌ Provider detection failed: ' + error.message, 'error');
    }

  } finally {
    // Re-enable button
    if (detectButton) {
      detectButton.disabled = false;
      detectButton.textContent = '🔍 Detect & Save';
    }
  }
}

/**
 * Saves API key without provider detection (manual save)
 */
async function saveApiKeyWithoutDetection() {
  const saveButton = document.getElementById('manual-save-button');
  const testResult = document.getElementById('ai-test-result');

  try {
    if (saveButton) {
      saveButton.disabled = true;
      saveButton.textContent = '💾 Saving...';
    }

    const apiKeyInput = document.getElementById('universal-ai-api-key');
    const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';

    if (!apiKey) {
      throw new Error('Please enter an API key first');
    }

    // Try to detect provider by pattern only (no validation)
    let detectedProvider = 'unknown';
    let providerConfig = null;

    if (window.aiProviderDetector) {
      const patternDetections = window.aiProviderDetector.detectProviderByPattern(apiKey);
      if (patternDetections.length > 0) {
        detectedProvider = patternDetections[0].providerId;
        providerConfig = patternDetections[0].provider;
      }
    }

    // Save configuration
    await saveUniversalAiConfiguration(apiKey, detectedProvider, providerConfig);

    // Update provider display if detected
    if (providerConfig) {
      updateProviderDisplay(detectedProvider, providerConfig);

      // Initialize migration bridge
      if (window.StashyAI && window.StashyAI.init) {
        await window.StashyAI.init();
        console.log('Popup: Migration bridge initialized with manual save');
      }
    }

    // Update status
    await updateUniversalAiStatus();

    // Show success message
    if (testResult) {
      testResult.style.display = 'block';
      testResult.innerHTML = `
        <div style="color: #28a745; background-color: #d4edda; padding: 12px; border-radius: 4px; border: 1px solid #c3e6cb;">
          <h4 style="margin: 0 0 10px 0;">✅ API Key Saved</h4>
          <div style="margin-bottom: 10px;">
            ${providerConfig ? `<strong>Detected Provider:</strong> ${providerConfig.displayName}<br>` : ''}
            <strong>Status:</strong> Saved successfully
          </div>
          <div style="font-size: 12px; color: #155724;">
            API key has been stored securely. Use "🧪 Test Connection" to verify functionality.
          </div>
        </div>
      `;
    }

  } catch (error) {
    console.error('Popup: Error saving API key:', error);

    if (testResult) {
      testResult.style.display = 'block';
      testResult.innerHTML = `
        <div style="color: #dc3545; background-color: #f8d7da; padding: 12px; border-radius: 4px; border: 1px solid #f5c6cb;">
          <h4 style="margin: 0 0 10px 0;">❌ Save Failed</h4>
          <div>${error.message}</div>
        </div>
      `;
    }

    if (typeof showStatus === 'function') {
      showStatus('❌ Save failed: ' + error.message, 'error');
    }

  } finally {
    if (saveButton) {
      saveButton.disabled = false;
      saveButton.textContent = '💾 Save Only';
    }
  }
}

/**
 * Updates the provider display in the UI
 */
function updateProviderDisplay(providerId, providerConfig) {
  const providerIcon = document.getElementById('detected-provider-icon');
  const providerName = document.getElementById('detected-provider-name');
  const providerStatus = document.getElementById('ai-provider-status');

  if (providerIcon && providerConfig) {
    providerIcon.textContent = providerConfig.icon;
  }

  if (providerName && providerConfig) {
    providerName.textContent = providerConfig.displayName;
  }

  if (providerStatus) {
    providerStatus.textContent = 'Configured';
    providerStatus.className = 'provider-status configured';
  }

  // Show and populate provider configuration
  showProviderConfiguration(providerId, providerConfig);
}

/**
 * Shows and populates the provider configuration interface
 */
function showProviderConfiguration(providerId, providerConfig) {
  const configDiv = document.getElementById('provider-configuration');
  const modelSelect = document.getElementById('provider-model-select');
  const temperatureSlider = document.getElementById('provider-temperature');
  const temperatureValue = document.getElementById('temperature-value');
  const maxTokensInput = document.getElementById('provider-max-tokens');

  if (!configDiv || !providerConfig) return;

  // Show configuration section
  configDiv.style.display = 'block';

  // Populate model options
  if (modelSelect && providerConfig.models) {
    modelSelect.innerHTML = '<option value="">Loading models...</option>';

    // For Google AI, try to fetch models dynamically from API
    if (providerId === 'google' && window.universalAiAdapter && window.universalAiAdapter.isReady()) {
      populateModelsFromApi(modelSelect, providerId, providerConfig);
    } else {
      // Use static models for other providers or when API is not available
      populateStaticModels(modelSelect, providerConfig);
    }
  }

  // Set default values
  if (temperatureSlider && temperatureValue) {
    temperatureSlider.value = '0.7';
    temperatureValue.textContent = '0.7';

    temperatureSlider.addEventListener('input', (e) => {
      temperatureValue.textContent = e.target.value;
    });
  }

  if (maxTokensInput) {
    // Set provider-specific default max tokens
    const defaultTokens = getProviderDefaultTokens(providerId);
    maxTokensInput.value = defaultTokens;
    maxTokensInput.max = getProviderMaxTokens(providerId);
  }
}

/**
 * Populates model select with models fetched from API
 */
async function populateModelsFromApi(modelSelect, providerId, providerConfig) {
  try {
    const models = await window.universalAiAdapter.getAvailableModels(true);
    modelSelect.innerHTML = '<option value="">Select a model...</option>';

    models.forEach(modelId => {
      const option = document.createElement('option');
      option.value = modelId;

      // Try to get model info from static config, or use model ID as display name
      const modelInfo = providerConfig.models[modelId];
      if (modelInfo) {
        option.textContent = `${modelInfo.name} (${modelInfo.maxTokens} tokens)`;
      } else {
        // For dynamically fetched models not in static config
        let displayName = modelId;
        if (modelId.startsWith('gemini-')) {
          displayName = modelId
            .replace('gemini-', 'Gemini ')
            .replace('-', '.')
            .replace('flash', 'Flash')
            .replace('pro', 'Pro')
            .replace('exp', 'Experimental')
            .replace('preview', 'Preview')
            .replace('tts', 'TTS');
        }
        option.textContent = displayName;
      }

      if (modelId === providerConfig.defaultModel) {
        option.selected = true;
      }
      modelSelect.appendChild(option);
    });

    console.log('Popup: Populated models from API for', providerId, ':', models);
  } catch (error) {
    console.warn('Popup: Failed to fetch models from API, falling back to static models:', error);
    populateStaticModels(modelSelect, providerConfig);
  }
}

/**
 * Populates model select with static models from provider config
 */
function populateStaticModels(modelSelect, providerConfig) {
  modelSelect.innerHTML = '<option value="">Select a model...</option>';

  Object.entries(providerConfig.models).forEach(([modelId, modelInfo]) => {
    const option = document.createElement('option');
    option.value = modelId;
    option.textContent = `${modelInfo.name} (${modelInfo.maxTokens} tokens)`;
    if (modelId === providerConfig.defaultModel) {
      option.selected = true;
    }
    modelSelect.appendChild(option);
  });
}

/**
 * Gets provider-specific default token limits
 */
function getProviderDefaultTokens(providerId) {
  switch (providerId) {
    case 'google': return 3000; // Increased by 2000
    case 'openai': return 3000; // Increased by 2000
    case 'anthropic': return 3000; // Increased by 2000
    case 'cohere': return 2500; // Increased by 2000
    case 'huggingface': return 2200; // Increased by 2000
    default: return 3000; // Increased by 2000
  }
}

/**
 * Gets provider-specific maximum token limits
 */
function getProviderMaxTokens(providerId) {
  switch (providerId) {
    case 'google': return 10192; // Increased by 2000
    case 'openai': return 6096; // Increased by 2000
    case 'anthropic': return 6096; // Increased by 2000
    case 'cohere': return 6096; // Increased by 2000
    case 'huggingface': return 3024; // Increased by 2000
    default: return 6096; // Increased by 2000
  }
}

/**
 * Migrates legacy AI configurations to the new universal system
 */
async function migrateLegacyConfiguration(legacyGoogleKey, legacyAiSettings) {
  try {
    console.log('Popup: Starting legacy configuration migration...');

    let apiKey = null;
    let migrationSource = '';

    // Determine which legacy configuration to use
    if (legacyGoogleKey) {
      apiKey = legacyGoogleKey;
      migrationSource = 'googleAiApiKey';
    } else if (legacyAiSettings && legacyAiSettings.apiKey) {
      apiKey = legacyAiSettings.apiKey;
      migrationSource = 'Stashy_ai_settings';
    }

    if (!apiKey) {
      console.log('Popup: No valid legacy API key found for migration');
      return;
    }

    console.log(`Popup: Migrating from ${migrationSource}...`);

    // Detect provider for the legacy key
    if (window.aiProviderDetector) {
      const detectionResult = await window.aiProviderDetector.detectAndValidateProvider(apiKey, {
        skipValidation: true // Skip validation during migration to avoid rate limits
      });

      if (detectionResult.success && detectionResult.provider) {
        console.log('Popup: Legacy key detected as:', detectionResult.provider.config.displayName);

        // Save as universal configuration
        await saveUniversalAiConfiguration(
          apiKey,
          detectionResult.provider.id,
          detectionResult.provider.config
        );

        // Update UI
        const apiKeyInput = document.getElementById('universal-ai-api-key');
        if (apiKeyInput) {
          apiKeyInput.value = apiKey;
        }

        updateProviderDisplay(detectionResult.provider.id, detectionResult.provider.config);
        updateAiStatus('configured');

        // Show migration success message
        showMigrationSuccessMessage(detectionResult.provider.config.displayName, migrationSource);

        console.log('Popup: Legacy configuration migration completed successfully');
      } else {
        console.warn('Popup: Failed to detect provider for legacy key');
        updateAiStatus('error');
      }
    } else {
      console.error('Popup: AI provider detector not available for migration');
      updateAiStatus('error');
    }

  } catch (error) {
    console.error('Popup: Error during legacy configuration migration:', error);
    updateAiStatus('error');
  }
}

/**
 * Shows a success message for completed migration
 */
function showMigrationSuccessMessage(providerName, migrationSource) {
  const statusDiv = document.getElementById('ai-status-message');
  if (statusDiv) {
    statusDiv.innerHTML = `
      <div style="color: #28a745; background-color: #d4edda; padding: 12px; border-radius: 4px; border: 1px solid #c3e6cb; margin-top: 10px;">
        <h4 style="margin: 0 0 10px 0;">✅ Configuration Migrated Successfully!</h4>
        <div style="margin-bottom: 10px;">
          <strong>Provider:</strong> ${providerName}<br>
          <strong>Migrated from:</strong> ${migrationSource === 'googleAiApiKey' ? 'Legacy Google AI' : 'Legacy AI Settings'}
        </div>
        <div style="font-size: 12px; color: #155724;">
          Your existing API key has been automatically upgraded to the new universal AI system. All features will continue to work as before.
        </div>
      </div>
    `;
  }
}

/**
 * Saves provider-specific configuration
 */
async function saveProviderConfiguration() {
  const saveButton = document.getElementById('save-provider-config');
  const modelSelect = document.getElementById('provider-model-select');
  const temperatureSlider = document.getElementById('provider-temperature');
  const maxTokensInput = document.getElementById('provider-max-tokens');

  try {
    if (saveButton) {
      saveButton.disabled = true;
      saveButton.textContent = '💾 Saving...';
    }

    // Get current provider
    const currentProvider = window.universalAiAdapter?.getCurrentProvider();
    if (!currentProvider) {
      throw new Error('No provider configured');
    }

    // Get configuration values
    const config = {
      model: modelSelect?.value || currentProvider.config.defaultModel,
      temperature: parseFloat(temperatureSlider?.value || '0.7'),
      maxTokens: parseInt(maxTokensInput?.value || '1000'),
      timestamp: Date.now()
    };

    // Validate configuration
    if (config.temperature < 0 || config.temperature > 1) {
      throw new Error('Temperature must be between 0 and 1');
    }

    if (config.maxTokens < 10 || config.maxTokens > getProviderMaxTokens(currentProvider.id)) {
      throw new Error(`Max tokens must be between 10 and ${getProviderMaxTokens(currentProvider.id)}`);
    }

    // Save to storage
    const storageKey = `providerConfig_${currentProvider.id}`;
    await chrome.storage.local.set({
      [storageKey]: config
    });

    // Update universal AI adapter with new configuration
    if (window.universalAiAdapter) {
      // Update the provider config with user preferences
      const updatedConfig = {
        ...currentProvider.config,
        defaultModel: config.model,
        userPreferences: config
      };

      // Re-initialize with updated config
      const result = await chrome.storage.local.get(['universalAiConfig']);
      const universalConfig = result.universalAiConfig;
      if (universalConfig) {
        universalConfig.providerConfig = updatedConfig;
        await chrome.storage.local.set({ universalAiConfig: universalConfig });

        await window.universalAiAdapter.initialize(
          currentProvider.id,
          universalConfig.apiKey,
          updatedConfig
        );

        // Notify all content scripts to reinitialize their AI adapters
        try {
          const tabs = await chrome.tabs.query({});
          for (const tab of tabs) {
            try {
              await chrome.tabs.sendMessage(tab.id, {
                type: 'REINITIALIZE_AI_ADAPTER',
                providerId: currentProvider.id,
                apiKey: universalConfig.apiKey,
                providerConfig: updatedConfig
              });
            } catch (tabError) {
              // Ignore errors for tabs that don't have content scripts
              console.log(`Popup: Could not notify tab ${tab.id} (normal for non-content tabs)`);
            }
          }
          console.log('Popup: Notified all content scripts to reinitialize AI adapter');
        } catch (notifyError) {
          console.warn('Popup: Error notifying content scripts:', notifyError.message);
        }
      }
    }

    // Show success message
    if (saveButton) {
      saveButton.textContent = '✅ Saved!';
      setTimeout(() => {
        if (saveButton) {
          saveButton.textContent = '💾 Save Configuration';
        }
      }, 2000);
    }

    console.log('Provider configuration saved:', config);

  } catch (error) {
    console.error('Error saving provider configuration:', error);

    if (saveButton) {
      saveButton.textContent = '❌ Error';
      setTimeout(() => {
        if (saveButton) {
          saveButton.textContent = '💾 Save Configuration';
        }
      }, 2000);
    }
  } finally {
    if (saveButton) {
      saveButton.disabled = false;
    }
  }
}

/**
 * Saves universal AI configuration to storage
 */
async function saveUniversalAiConfiguration(apiKey, providerId, providerConfig) {
  // Save to secure storage
  if (window.secureApiStorage) {
    try {
      await window.secureApiStorage.storeApiKey('universalAi', apiKey);
      console.log('Popup: Universal AI key stored in secure storage');
    } catch (error) {
      console.warn('Popup: Secure storage failed:', error.message);
    }
  }

  // Save to regular storage as fallback
  const configData = {
    apiKey: apiKey,
    providerId: providerId,
    providerConfig: providerConfig,
    timestamp: Date.now()
  };

  await chrome.storage.local.set({
    'universalAiConfig': configData,
    'googleAiApiKey': apiKey // Maintain backward compatibility
  });

  // Initialize universal AI adapter with the saved configuration
  if (window.universalAiAdapter && providerConfig) {
    try {
      await window.universalAiAdapter.initialize(providerId, apiKey, providerConfig);
      console.log('Popup: Universal AI adapter initialized with saved configuration');
    } catch (initError) {
      console.warn('Popup: Error initializing universal AI adapter:', initError.message);
    }
  }

  console.log('Popup: Universal AI configuration saved');
}

/**
 * Updates the universal AI status display
 */
async function updateUniversalAiStatus() {
  const statusIcon = document.querySelector('#ai-status-indicator .status-icon');
  const statusText = document.querySelector('#ai-status-indicator .status-text');
  const providerStatus = document.getElementById('ai-provider-status');

  try {
    // Check for saved configuration
    const result = await chrome.storage.local.get(['universalAiConfig', 'googleAiApiKey', 'Stashy_ai_settings']);
    const config = result.universalAiConfig;
    const legacyGoogleKey = result.googleAiApiKey;
    const legacyAiSettings = result.Stashy_ai_settings;

    if (config && config.apiKey) {
      // Universal configuration exists
      if (statusIcon) statusIcon.textContent = '✅';
      if (statusText) statusText.textContent = 'AI features enabled';
      if (providerStatus) {
        providerStatus.textContent = 'Configured';
        providerStatus.className = 'provider-status configured';
      }

      // Update provider display
      if (config.providerConfig) {
        updateProviderDisplay(config.providerId, config.providerConfig);
      }

      // Load API key into input field
      const apiKeyInput = document.getElementById('universal-ai-api-key');
      if (apiKeyInput && !apiKeyInput.value) {
        apiKeyInput.value = config.apiKey;
      }

    } else if (legacyGoogleKey || legacyAiSettings) {
      // Legacy configuration exists - trigger migration
      if (statusIcon) statusIcon.textContent = '🔄';
      if (statusText) statusText.textContent = 'Migrating configuration...';
      if (providerStatus) {
        providerStatus.textContent = 'Migrating';
        providerStatus.className = 'provider-status migrating';
      }

      // Perform migration asynchronously to avoid blocking UI
      setTimeout(async () => {
        await migrateLegacyConfiguration(legacyGoogleKey, legacyAiSettings);
      }, 100);

    } else {
      // API key not configured
      if (statusIcon) statusIcon.textContent = '⚙️';
      if (statusText) statusText.textContent = 'Configure your API key to enable AI features';
      if (providerStatus) {
        providerStatus.textContent = 'Not configured';
        providerStatus.className = 'provider-status not-configured';
      }
    }

  } catch (error) {
    console.error('Popup: Error updating AI status:', error);
  }
}

// Initialize voice settings function
function initVoiceSettings(panel) {
  console.log('Initializing voice settings...');

  // Store the panel reference
  const voiceSettingsPanel = panel;

  // DOM Elements within the voice settings panel
  const providerSelect = document.getElementById('voice-provider');
  const apiKeyContainer = document.getElementById('api-key-container');
  const apiKeyInput = document.getElementById('voice-api-key');
  const togglePasswordButton = document.getElementById('toggle-password');
  const languageSelect = document.getElementById('voice-language');
  const silenceThresholdInput = document.getElementById('voice-silence-threshold');
  const silenceValue = document.getElementById('silence-value');
  const saveButton = document.getElementById('save-voice-settings');
  const resetButton = document.getElementById('reset-settings');
  const providerInfo = document.getElementById('provider-info');

  // Check if essential elements exist before proceeding
  if (!providerSelect || !apiKeyContainer || !apiKeyInput || !languageSelect || !saveButton) {
      console.error("Voice settings panel elements not found. Cannot initialize.");
      console.error("Missing elements:", {
          providerSelect: !!providerSelect,
          apiKeyContainer: !!apiKeyContainer,
          apiKeyInput: !!apiKeyInput,
          languageSelect: !!languageSelect,
          saveButton: !!saveButton
      });
      if (voiceSettingsPanel) voiceSettingsPanel.innerHTML = "<p style='color: red;'>Error loading settings UI.</p>";
      return;
  }

  console.log('All voice settings elements found successfully');

  // Provider info text
  const providerInfoText = {
    browser: '<strong>Browser Speech API (Default)</strong><br>Uses your browser\'s built-in speech recognition. No API key required. Quality varies by browser and language support may be limited.',
    google: '<strong>Google Cloud Speech-to-Text</strong><br>High-accuracy transcription with extensive language support. Requires a Google Cloud API key. <a href="https://cloud.google.com/speech-to-text" target="_blank">Get API key →</a>',
    azure: '<strong>Microsoft Azure Speech</strong><br>Enterprise-grade speech recognition with advanced features. Requires an Azure Cognitive Services API key. <a href="https://azure.microsoft.com/en-us/services/cognitive-services/speech-services/" target="_blank">Get API key →</a>',
    assembly: '<strong>AssemblyAI</strong><br>Advanced speech recognition with features like summarization and sentiment analysis. Requires an AssemblyAI API key. <a href="https://www.assemblyai.com/" target="_blank">Get API key →</a>'
  };

  // Update provider UI based on selection
  function updateProviderUI(provider, apiKeys = {}) {
      console.log(`Updating provider UI for: ${provider}`);

      // Show/hide API key input - properly handle the hidden class
      if (provider === 'browser') {
          apiKeyContainer.style.display = 'none';
          apiKeyContainer.classList.add('hidden');
      } else {
          apiKeyContainer.classList.remove('hidden');
          apiKeyContainer.style.display = 'block';
      }

      // Update provider info text
      if (providerInfo) {
          providerInfo.innerHTML = providerInfoText[provider] || 'Select a provider to see details.';
      }

      // Fill API key input if needed and available
      apiKeyInput.value = ''; // Clear first
      if (provider === 'google' && apiKeys.googleSpeechApiKey) {
          apiKeyInput.value = apiKeys.googleSpeechApiKey;
      } else if (provider === 'azure' && apiKeys.azureSpeechApiKey) {
          apiKeyInput.value = apiKeys.azureSpeechApiKey;
      } else if (provider === 'assembly' && apiKeys.assemblyAiApiKey) {
          apiKeyInput.value = apiKeys.assemblyAiApiKey;
      }

      // Ensure password visibility toggle is correct
      if (togglePasswordButton) {
          apiKeyInput.setAttribute('type', 'password');
          togglePasswordButton.innerHTML = '<span class="popup-icon">👁️</span>';
      }
  }

  // Update language options for the selected provider
  function updateLanguageOptionsForProvider(provider) {
      console.log(`Updating language options for provider: ${provider}`);

      // Clear existing options first
      languageSelect.innerHTML = '';

      // Call the global updateLanguageOptions function
      updateLanguageOptions(provider);
  }

  // Load voice settings from storage
  function loadVoiceSettings() {
      console.log('Loading voice settings from storage...');

      chrome.storage.local.get(['voiceApiPreferences', 'voiceApiKeys'], function(result) {
          const prefs = result.voiceApiPreferences || {};
          const keys = result.voiceApiKeys || {};

          console.log('Loaded preferences:', prefs);
          console.log('Loaded API keys:', Object.keys(keys));

          // Set provider (default to browser if not set)
          const currentProvider = prefs.provider || 'browser';
          providerSelect.value = currentProvider;
          updateProviderUI(currentProvider, keys);

          // Update language options for the current provider
          updateLanguageOptionsForProvider(currentProvider);

          // Set the saved language after a small delay to ensure options are populated
          setTimeout(() => {
              if (prefs.language && languageSelect.querySelector(`option[value="${prefs.language}"]`)) {
                  languageSelect.value = prefs.language;
                  console.log(`Set language to saved value: ${prefs.language}`);
              } else if (languageSelect.options.length > 0) {
                  languageSelect.selectedIndex = 0;
                  console.log(`Saved language ${prefs.language} not available, using: ${languageSelect.value}`);
              }
          }, 100);

          // Set quality (default to standard)
          const qualitySelect = document.getElementById('voice-quality');
          if (qualitySelect) {
              qualitySelect.value = prefs.quality || 'standard';
          }

          // Set checkboxes (provide defaults)
          setCheckbox('voice-punctuation', prefs.enablePunctuation !== false);
          setCheckbox('voice-capitalization', prefs.enableAutomaticCapitalization !== false);
          setCheckbox('voice-profanity-filter', prefs.enableProfanityFilter === true);
          setCheckbox('voice-interim-results', prefs.enableInterimResults !== false);
          setCheckbox('voice-auto-detect', prefs.autoDetectLanguage === true);
          setCheckbox('voice-background-noise-reduction', prefs.backgroundNoiseReduction === true);
          setCheckbox('voice-speaker-diarization', prefs.speakerDiarization === true);

          // Set silence threshold (default 15 seconds)
          if (silenceThresholdInput && silenceValue) {
              const defaultSeconds = 15;
              const savedMillis = prefs.silenceThreshold;
              const seconds = (typeof savedMillis === 'number' && savedMillis >= 1000) ? Math.floor(savedMillis / 1000) : defaultSeconds;
              silenceThresholdInput.value = seconds;
              silenceValue.textContent = seconds + ' seconds';
              console.log(`Set silence threshold to: ${seconds} seconds`);
          }
      });
  }

  // Helper function to set checkbox values
  function setCheckbox(id, checked) {
      const checkbox = document.getElementById(id);
      if (checkbox) checkbox.checked = !!checked;
  }

  // Helper function to get checkbox values
  function getCheckboxValue(id, defaultValue) {
      const checkbox = document.getElementById(id);
      return checkbox ? checkbox.checked : defaultValue;
  }

  // Save voice settings to storage
  function saveVoiceSettings() {
      console.log('Saving voice settings...');

      const provider = providerSelect.value;
      const language = languageSelect.value;
      const apiKey = apiKeyInput.value.trim();

      // Prepare preferences object
      const preferences = {
          provider: provider,
          language: language,
          enablePunctuation: getCheckboxValue('voice-punctuation', true),
          enableAutomaticCapitalization: getCheckboxValue('voice-capitalization', true),
          enableProfanityFilter: getCheckboxValue('voice-profanity-filter', false),
          enableInterimResults: getCheckboxValue('voice-interim-results', true),
          silenceThreshold: parseInt(silenceThresholdInput ? silenceThresholdInput.value : 15, 10) * 1000,
          autoDetectLanguage: getCheckboxValue('voice-auto-detect', false),
          backgroundNoiseReduction: getCheckboxValue('voice-background-noise-reduction', false),
          speakerDiarization: getCheckboxValue('voice-speaker-diarization', false),
          quality: document.getElementById('voice-quality') ? document.getElementById('voice-quality').value : 'standard'
      };

      // Get existing keys first to preserve other keys
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          let apiKeys = result.voiceApiKeys || {};

          // Update key for selected provider if provided
          if (provider !== 'browser') {
              if (provider === 'google') {
                  apiKeys.googleSpeechApiKey = apiKey;
              } else if (provider === 'azure') {
                  apiKeys.azureSpeechApiKey = apiKey;
              } else if (provider === 'assembly') {
                  apiKeys.assemblyAiApiKey = apiKey;
              }
          }

          // Save to storage
          chrome.storage.local.set({
              voiceApiPreferences: preferences,
              voiceApiKeys: apiKeys
          }, function() {
               if (chrome.runtime.lastError) {
                   console.error("Error saving voice settings:", chrome.runtime.lastError);
                   if (typeof showStatus === 'function') {
                       showStatus(`Error saving: ${chrome.runtime.lastError.message}`, 'error');
                   }
               } else {
                  console.log('Voice settings saved successfully');
                  if (typeof showStatus === 'function') {
                      showStatus('Voice settings saved!', 'success');
                  }
               }
          });
      });
  }

  // Set up event listeners
  console.log('Setting up voice settings event listeners...');

  // Provider change event
  providerSelect.addEventListener('change', function() {
      const provider = this.value;
      console.log(`Provider changed to: ${provider}`);

      // Get current keys to pass to updateProviderUI
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          updateProviderUI(provider, result.voiceApiKeys || {});
          updateLanguageOptionsForProvider(provider);
      });
  });

  // Silence threshold slider event
  if (silenceThresholdInput && silenceValue) {
      silenceThresholdInput.addEventListener('input', function() {
          silenceValue.textContent = this.value + ' seconds';
          console.log(`Silence threshold changed to: ${this.value} seconds`);
      });
  }

  // Password toggle event
  if (togglePasswordButton) {
      togglePasswordButton.addEventListener('click', function() {
          const type = apiKeyInput.getAttribute('type') === 'password' ? 'text' : 'password';
          apiKeyInput.setAttribute('type', type);
          togglePasswordButton.innerHTML = type === 'password' ?
              '<span class="popup-icon">👁️</span>' :
              '<span class="popup-icon">🔒</span>';
      });
  }

  // Save button event
  saveButton.addEventListener('click', saveVoiceSettings);

  // Reset button event (if exists)
  if (resetButton) {
      resetButton.addEventListener('click', function() {
          if (confirm('Reset all voice settings to default values? This will clear any entered API keys.')) {
              console.log('Resetting voice settings to defaults...');
              // Reset logic would go here - for now just reload defaults
              loadVoiceSettings();
          }
      });
  }

  // Load initial settings
  console.log('Loading initial voice settings...');
  loadVoiceSettings();

  console.log('Voice settings initialization completed successfully');
}

/**
 * Initialize UI Customization Panel
 */
function initUICustomization(panel) {

  // Get all the form elements
  const saveButton = document.getElementById('save-ui-customization');
  const resetButton = document.getElementById('reset-ui-customization');
  const statusMessage = document.getElementById('ui-customization-status-text');

  // Check if essential elements exist
  if (!saveButton || !resetButton) {
    console.error("UI Customization panel elements not found. Cannot initialize.");
    if (panel) panel.innerHTML = "<p style='color: red;'>Error loading UI customization.</p>";
    return;
  }

  // Load current settings
  loadUICustomizationSettings();

  // Set up event listeners
  saveButton.addEventListener('click', saveUICustomizationSettings);
  resetButton.addEventListener('click', resetUICustomizationSettings);

  // Set up real-time checkbox listeners
  setupRealTimeUpdates();

  // Set up reorder button handlers
  setupReorderControls();

  // Set up drag and drop functionality
  setupDragAndDrop();

  /**
   * Load UI customization settings from storage
   */
  function loadUICustomizationSettings() {
    chrome.storage.local.get(['Stashy_ui_customization'], (result) => {
      const settings = result.Stashy_ui_customization || getDefaultUICustomizationSettings();

      // Apply settings to checkboxes
      applySettingsToUI(settings);
    });
  }

  /**
   * Get default UI customization settings
   */
  function getDefaultUICustomizationSettings() {
    return {
      toolbarDropdowns: {
        order: ['format-dropdown', 'style-dropdown', 'insert-dropdown', 'tools-dropdown', 'view-dropdown'],
        visibility: {
          'format-dropdown': true,
          'style-dropdown': true,
          'insert-dropdown': true,
          'tools-dropdown': true,
          'view-dropdown': true
        }
      },
      uiElements: {
        visibility: {
          'note-title': true,
          'tags-input': true,
          'notebook-selector': true,
          'reminder-input': true,
          'note-switcher': true,
          'timestamp-display': true
        }
      },
      snippetButtons: {
        order: ['datetime-button', 'pageinfo-button', 'highlight-button', 'timestamp-button', 'ai-webpage-button', 'ai-video-button', 'ai-note-button', 'ai-academic-button', 'ai-search-button'],
        visibility: {
          'datetime-button': true,
          'pageinfo-button': true,
          'highlight-button': true,
          'timestamp-button': true,
          'ai-webpage-button': true,
          'ai-video-button': true,
          'ai-note-button': true,
          'ai-academic-button': true,
          'ai-search-button': true
        }
      }
    };
  }

  /**
   * Apply settings to the UI elements
   */
  function applySettingsToUI(settings) {
    // Apply toolbar dropdown settings
    const toolbarList = document.getElementById('toolbar-dropdowns-list');
    if (toolbarList && settings.toolbarDropdowns) {
      reorderListItems(toolbarList, settings.toolbarDropdowns.order);
      applyVisibilitySettings(settings.toolbarDropdowns.visibility);
    }

    // Apply UI elements settings
    if (settings.uiElements) {
      applyVisibilitySettings(settings.uiElements.visibility);
    }

    // Apply snippet buttons settings
    const snippetList = document.getElementById('snippet-buttons-list');
    if (snippetList && settings.snippetButtons) {
      reorderListItems(snippetList, settings.snippetButtons.order);
      applyVisibilitySettings(settings.snippetButtons.visibility);
    }
  }

  /**
   * Apply visibility settings to checkboxes
   */
  function applyVisibilitySettings(visibilitySettings) {
    Object.entries(visibilitySettings).forEach(([element, isVisible]) => {
      const checkboxId = element + '-toggle';
      const checkbox = document.getElementById(checkboxId);
      if (checkbox) {
        checkbox.checked = isVisible;
      }
    });
  }

  /**
   * Reorder list items based on saved order
   */
  function reorderListItems(container, order) {
    const items = Array.from(container.children);
    const itemMap = new Map();

    // Create a map of data-element to DOM element
    items.forEach(item => {
      const element = item.dataset.element;
      if (element) {
        itemMap.set(element, item);
      }
    });

    // Clear container and re-add items in the specified order
    container.innerHTML = '';
    order.forEach(elementName => {
      const item = itemMap.get(elementName);
      if (item) {
        container.appendChild(item);
      }
    });
  }

  /**
   * Save UI customization settings
   */
  function saveUICustomizationSettings() {
    try {
      const settings = {
        toolbarDropdowns: {
          order: getListOrder('toolbar-dropdowns-list'),
          visibility: getVisibilitySettings('toolbar-dropdowns-list')
        },
        uiElements: {
          visibility: getVisibilitySettings('ui-elements-list')
        },
        snippetButtons: {
          order: getListOrder('snippet-buttons-list'),
          visibility: getVisibilitySettings('snippet-buttons-list')
        }
      };

      // Save to storage
      chrome.storage.local.set({ 'Stashy_ui_customization': settings }, () => {
        if (chrome.runtime.lastError) {
          showUICustomizationStatus('Error saving settings: ' + chrome.runtime.lastError.message, 'error');
        } else {
          showUICustomizationStatus('Settings saved successfully!', 'success');

          // Send message to content scripts to apply changes
          chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
              chrome.tabs.sendMessage(tabs[0].id, {
                action: 'applyUICustomization',
                settings: settings
              }).catch(() => {
                // Ignore errors if content script is not loaded
              });
            }
          });
        }
      });
    } catch (error) {
      showUICustomizationStatus('Error saving settings: ' + error.message, 'error');
    }
  }

  /**
   * Get the current order of items in a list
   */
  function getListOrder(listId) {
    const list = document.getElementById(listId);
    if (!list) return [];

    return Array.from(list.children).map(item => item.dataset.element).filter(Boolean);
  }

  /**
   * Get visibility settings from checkboxes
   */
  function getVisibilitySettings(listId) {
    const list = document.getElementById(listId);
    if (!list) return {};

    const settings = {};
    Array.from(list.children).forEach(item => {
      const element = item.dataset.element;
      if (element) {
        // All checkboxes follow the pattern: element-toggle
        const checkboxId = element + '-toggle';

        const checkbox = document.getElementById(checkboxId);
        if (checkbox) {
          settings[element] = checkbox.checked;
        }
      }
    });

    return settings;
  }

  /**
   * Reset to default settings
   */
  function resetUICustomizationSettings() {
    if (confirm('Reset all UI customization settings to default values?')) {
      const defaultSettings = getDefaultUICustomizationSettings();

      // Apply default settings to UI
      applySettingsToUI(defaultSettings);

      // Save default settings
      chrome.storage.local.set({ 'Stashy_ui_customization': defaultSettings }, () => {
        if (chrome.runtime.lastError) {
          showUICustomizationStatus('Error resetting settings: ' + chrome.runtime.lastError.message, 'error');
        } else {
          showUICustomizationStatus('Settings reset to defaults!', 'success');
        }
      });
    }
  }

  /**
   * Show status message
   */
  function showUICustomizationStatus(message, type = 'info') {
    if (!statusMessage) return;

    const statusContainer = statusMessage.parentElement;
    if (!statusContainer) return;

    statusMessage.textContent = message;
    statusContainer.className = `status-message visible ${type}`;

    // Hide after 3 seconds
    setTimeout(() => {
      statusContainer.classList.remove('visible');
    }, 3000);
  }

  /**
   * Set up reorder button controls
   */
  function setupReorderControls() {
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('reorder-btn')) {
        const direction = e.target.dataset.direction;
        const item = e.target.closest('.customization-item');
        const container = item.parentElement;

        if (direction === 'up') {
          const prevItem = item.previousElementSibling;
          if (prevItem) {
            container.insertBefore(item, prevItem);
          }
        } else if (direction === 'down') {
          const nextItem = item.nextElementSibling;
          if (nextItem) {
            container.insertBefore(nextItem, item);
          }
        }

        // Update button states
        updateReorderButtonStates(container);

        // Apply changes immediately
        const settings = {
          toolbarDropdowns: {
            order: getListOrder('toolbar-dropdowns-list'),
            visibility: getVisibilitySettings('toolbar-dropdowns-list')
          },
          uiElements: {
            visibility: getVisibilitySettings('ui-elements-list')
          },
          snippetButtons: {
            order: getListOrder('snippet-buttons-list'),
            visibility: getVisibilitySettings('snippet-buttons-list')
          }
        };

        // Send message to content scripts to apply changes immediately
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'applyUICustomization',
              settings: settings
            }).catch(() => {
              // Ignore errors if content script is not loaded
            });
          }
        });
      }
    });
  }

  /**
   * Update reorder button states (disable up/down when at edges)
   */
  function updateReorderButtonStates(container) {
    const items = Array.from(container.children);

    items.forEach((item, index) => {
      const upBtn = item.querySelector('.reorder-btn[data-direction="up"]');
      const downBtn = item.querySelector('.reorder-btn[data-direction="down"]');

      if (upBtn) upBtn.disabled = index === 0;
      if (downBtn) downBtn.disabled = index === items.length - 1;
    });
  }

  /**
   * Set up real-time updates for checkboxes
   */
  function setupRealTimeUpdates() {
    // Get all checkboxes in the UI customization panel
    const checkboxes = document.querySelectorAll('#ui-customization-panel .ui-checkbox');

    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        // Apply changes immediately without saving
        const settings = {
          toolbarDropdowns: {
            order: getListOrder('toolbar-dropdowns-list'),
            visibility: getVisibilitySettings('toolbar-dropdowns-list')
          },
          uiElements: {
            visibility: getVisibilitySettings('ui-elements-list')
          },
          snippetButtons: {
            order: getListOrder('snippet-buttons-list'),
            visibility: getVisibilitySettings('snippet-buttons-list')
          }
        };

        // Send message to content scripts to apply changes immediately
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'applyUICustomization',
              settings: settings
            }).catch(() => {
              // Ignore errors if content script is not loaded
            });
          }
        });
      });
    });
  }

  /**
   * Set up drag and drop functionality
   */
  function setupDragAndDrop() {
    // Get all draggable items
    const draggableItems = document.querySelectorAll('#ui-customization-panel .customization-item');

    draggableItems.forEach(item => {
      const dragHandle = item.querySelector('.drag-handle');
      if (dragHandle) {
        // Make the item draggable when drag handle is used
        dragHandle.addEventListener('mousedown', (e) => {
          e.preventDefault();
          startDrag(item, e);
        });

        // Add visual feedback
        dragHandle.style.cursor = 'grab';
        dragHandle.addEventListener('mousedown', () => {
          dragHandle.style.cursor = 'grabbing';
        });

        document.addEventListener('mouseup', () => {
          dragHandle.style.cursor = 'grab';
        });
      }
    });
  }

  /**
   * Start drag operation
   */
  function startDrag(draggedItem, startEvent) {
    const container = draggedItem.parentElement;
    const items = Array.from(container.children);
    const draggedIndex = items.indexOf(draggedItem);

    // Create visual feedback
    draggedItem.classList.add('dragging');
    draggedItem.style.opacity = '0.5';
    draggedItem.style.transform = 'scale(0.95)';

    let placeholder = null;

    function onMouseMove(e) {
      e.preventDefault();

      // Find the item we're hovering over
      const afterElement = getDragAfterElement(container, e.clientY);

      if (afterElement == null) {
        container.appendChild(draggedItem);
      } else {
        container.insertBefore(draggedItem, afterElement);
      }
    }

    function onMouseUp(e) {
      e.preventDefault();

      // Remove visual feedback
      draggedItem.classList.remove('dragging');
      draggedItem.style.opacity = '';
      draggedItem.style.transform = '';

      // Clean up event listeners
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);

      // Check if order changed
      const newItems = Array.from(container.children);
      const newIndex = newItems.indexOf(draggedItem);

      if (newIndex !== draggedIndex) {
        // Update button states
        updateReorderButtonStates(container);

        // Apply changes immediately
        const settings = {
          toolbarDropdowns: {
            order: getListOrder('toolbar-dropdowns-list'),
            visibility: getVisibilitySettings('toolbar-dropdowns-list')
          },
          uiElements: {
            visibility: getVisibilitySettings('ui-elements-list')
          },
          snippetButtons: {
            order: getListOrder('snippet-buttons-list'),
            visibility: getVisibilitySettings('snippet-buttons-list')
          }
        };

        // Send message to content scripts to apply changes immediately
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'applyUICustomization',
              settings: settings
            }).catch(() => {
              // Ignore errors if content script is not loaded
            });
          }
        });
      }
    }

    // Add event listeners
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  /**
   * Get the element that should come after the dragged element
   */
  function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.customization-item:not(.dragging)')];

    return draggableElements.reduce((closest, child) => {
      const box = child.getBoundingClientRect();
      const offset = y - box.top - box.height / 2;

      if (offset < 0 && offset > closest.offset) {
        return { offset: offset, element: child };
      } else {
        return closest;
      }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
  }

  // Initial setup
  updateReorderButtonStates(document.getElementById('toolbar-dropdowns-list'));
  updateReorderButtonStates(document.getElementById('snippet-buttons-list'));
}

// --- END OF FILE popup.js ---