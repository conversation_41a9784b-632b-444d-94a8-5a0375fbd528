// --- START OF FILE content-main.js ---

// --- Premium Manager Initialization ---
(function initializePremiumManager() {
    if (typeof window.StashyPremium === 'undefined') {
        console.log('Stashy: Loading premium manager in content-main...');
        try {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('lib/premium-manager.js');
            script.onload = () => {
                console.log('Stashy: Premium manager loaded successfully in content-main');
            };
            script.onerror = () => {
                console.error('Stashy: Failed to load premium manager in content-main');
            };
            (document.head || document.documentElement).appendChild(script);
        } catch (error) {
            console.error('Stashy: Error loading premium manager in content-main:', error);
        }
    } else {
        console.log('Stashy: Premium manager already available in content-main');
    }
})();

// CRITICAL: Define seekToTimestamp function IMMEDIATELY to prevent "not defined" errors
// This must be the first thing that runs to ensure transcript timestamps work
window.seekToTimestamp = function(timeInSeconds) {
    console.log(`Stashy: Seeking to timestamp: ${timeInSeconds} seconds`);

    try {
        // Find video element using multiple methods
        let mediaElement = null;

        // Method 1: Look for playing videos first
        const videos = document.querySelectorAll('video');
        console.log(`Stashy: Found ${videos.length} video elements`);

        for (const video of videos) {
            try {
                // Prefer playing videos with valid duration
                if (!video.paused && video.duration > 0 && video.currentTime >= 0) {
                    const rect = video.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        mediaElement = video;
                        console.log('Stashy: Found playing video with valid dimensions');
                        break;
                    }
                }
            } catch (e) {
                console.warn('Stashy: Error checking video:', e);
            }
        }

        // Method 2: YouTube specific selectors
        if (!mediaElement) {
            const youtubeSelectors = [
                '#movie_player video',
                '.html5-video-player video',
                '.video-stream',
                'video[src*="youtube"]',
                'video[src*="googlevideo"]'
            ];

            for (const selector of youtubeSelectors) {
                mediaElement = document.querySelector(selector);
                if (mediaElement) {
                    console.log(`Stashy: Found video with selector: ${selector}`);
                    break;
                }
            }
        }

        // Method 3: Any video with valid duration
        if (!mediaElement) {
            for (const video of videos) {
                try {
                    if (video.duration > 0 && video.offsetWidth > 0) {
                        mediaElement = video;
                        console.log('Stashy: Found video with valid duration');
                        break;
                    }
                } catch (e) {
                    console.warn('Stashy: Error checking video duration:', e);
                }
            }
        }

        // Method 4: Any video as absolute last resort
        if (!mediaElement && videos.length > 0) {
            mediaElement = videos[0];
            console.log('Stashy: Using first available video as last resort');
        }

        if (mediaElement) {
            console.log(`Stashy: Found media element, seeking to ${timeInSeconds} seconds`);
            mediaElement.currentTime = timeInSeconds;

            // Show feedback
            const formattedTime = `${Math.floor(timeInSeconds / 60)}:${(timeInSeconds % 60).toString().padStart(2, '0')}`;

            const feedback = document.createElement('div');
            feedback.style.cssText = `
                position: fixed; top: 20px; right: 20px; background: rgba(25, 118, 210, 0.9);
                color: white; padding: 8px 12px; border-radius: 4px; font-size: 14px;
                z-index: 10000; transition: opacity 0.3s ease;
            `;
            feedback.textContent = `⏰ Jumped to ${formattedTime}`;
            document.body.appendChild(feedback);

            setTimeout(() => {
                feedback.style.opacity = '0';
                setTimeout(() => feedback.remove(), 300);
            }, 2000);

            // Focus the media element for keyboard controls
            if (mediaElement.focus) {
                mediaElement.focus();
            }

            console.log(`Stashy: Successfully seeked to ${timeInSeconds} seconds`);
        } else {
            console.warn('Stashy: Could not find media element for seeking');
            console.log('Available videos:', document.querySelectorAll('video').length);
            alert("Stashy: Could not find the active video/audio element on the page to seek.");
        }
    } catch (error) {
        console.error('Stashy: Error seeking to timestamp:', error);
        alert(`Stashy: An error occurred while trying to seek to timestamp.`);
    }
};

console.log('Stashy: seekToTimestamp function defined in content-main.js');

/**
 * Checks if the current site is ChatGPT and applies specific handling
 * @returns {boolean} True if this is a ChatGPT site
 */
function isChatGPTSite() {
    const url = window.location.href;
    return url.includes('chatgpt.com') || url.includes('chat.openai.com');
}

/**
 * Waits for ChatGPT to finish loading before initializing Stashy
 * @returns {Promise} Promise that resolves when ChatGPT is ready
 */
function waitForChatGPTReady() {
    return new Promise((resolve) => {
        console.log("Stashy: Waiting for ChatGPT to finish loading...");

        // Check if ChatGPT's main container is ready
        const checkReady = () => {
            // Look for ChatGPT's main content area
            const mainContent = document.querySelector('main') ||
                               document.querySelector('[role="main"]') ||
                               document.querySelector('.flex.h-full.flex-col');

            if (mainContent && document.readyState === 'complete') {
                console.log("Stashy: ChatGPT appears to be ready");
                resolve();
            } else {
                setTimeout(checkReady, 500);
            }
        };

        // Start checking after a short delay to let React settle
        setTimeout(checkReady, 1000);
    });
}

/**
 * Applies ChatGPT-specific fixes and styling
 */
function applyChatGPTFixes() {
    console.log("Stashy: Applying ChatGPT-specific fixes");

    // Add ChatGPT-specific CSS
    const style = document.createElement('style');
    style.id = 'Stashy-chatgpt-fixes';
    style.textContent = `
        /* ChatGPT specific fixes - only essential changes for compatibility */
        #Stashy-toggle {
            z-index: 2147483647 !important;
        }

        #Stashy-container {
            z-index: 2147483647 !important;
        }

        /* Ensure Stashy elements don't interfere with ChatGPT's React rendering */
        #Stashy-toggle,
        #Stashy-container,
        #Stashy-flashcard-modal {
            pointer-events: auto !important;
            isolation: isolate !important;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Main initialization function for the Stashy content script.
 * Creates UI elements, loads data, and sets up observers/listeners.
 */
function initStashy() {
    console.log("Stashy: Initializing..."); // DEBUG

    // Check if this is ChatGPT and apply specific fixes
    if (isChatGPTSite()) {
        console.log("Stashy: ChatGPT site detected");
        applyChatGPTFixes();
    }

    // Standard initialization for all sites
    console.log("Stashy: Using standard initialization");

    // 1. Create core UI elements if they don't exist
    try {
        if (typeof createToggleButton === 'function') {
            createToggleButton(); // Defined in ui.js
        } else {
            console.error("Stashy: createToggleButton function not found!");
        }

        if (typeof createNoteUI === 'function') {
            createNoteUI(); // Defined in ui.js
        } else {
            console.error("Stashy: createNoteUI function not found!");
        }

        if (typeof createFlashcardModal === 'function') {
            createFlashcardModal(); // Defined in ui.js
        } else {
            console.error("Stashy: createFlashcardModal function not found!");
        }

    } catch (error) {
        console.error("Stashy: Error creating UI elements:", error);
    }

    // 2. Load saved state (notes data, visibility, current note)
    //    loadState will call loadCurrentNote internally.
    loadState(); // Defined in storage.js



    // Initialize enhanced voice module if available
    if (typeof initEnhancedVoice === 'function') {
        try {
            initEnhancedVoice(); // Defined in content-voice-enhanced.js
            console.log("Stashy: Enhanced voice module initialized");
        } catch (error) {
            console.error("Stashy: Failed to initialize enhanced voice module:", error);
        }
    }



    // 3. Load and apply highlights from storage with dynamic content change detection
    //    Wait for window.load AND an additional delay for dynamic content
    const applyHighlightsAfterLoad = () => {
         if (typeof highlightKey !== 'undefined') {
              console.log("Stashy: Applying highlights after load + delay with dynamic detection."); // DEBUG
              // Use enhanced version that includes dynamic content change detection
              if (typeof loadAndApplyHighlightsEnhanced === 'function') {
                  loadAndApplyHighlightsEnhanced(); // Enhanced version with dynamic detection
              } else {
                  loadAndApplyHighlights(); // Fallback to original version
              }
          } else {
              console.error("Stashy: highlightKey still not defined after delay."); // DEBUG
          }
    };

    // 4. Set up note URL update handling for SPA navigation (YouTube, etc.)
    if (typeof setupNoteURLUpdateHandling === 'function') {
        setupNoteURLUpdateHandling();
    }

    if (document.readyState === 'complete') {
         // If already complete, apply after a short delay
         console.log("Stashy: DOM already complete, scheduling highlight apply."); // DEBUG
         setTimeout(applyHighlightsAfterLoad, 500); // 500ms delay
     } else {
         // Wait for the 'load' event, then add a delay
         console.log("Stashy: Waiting for window.load to schedule highlight apply."); // DEBUG
         window.addEventListener('load', () => {
             console.log("Stashy: window.load fired, scheduling highlight apply."); // DEBUG
             setTimeout(applyHighlightsAfterLoad, 500); // 500ms delay after load
         }, { once: true });
     }


}


// --- Script Entry Point ---

// Add global error handler for extension context invalidation
window.addEventListener('error', (event) => {
    if (event.error && event.error.message && event.error.message.includes('Extension context invalidated')) {
        // Extension context invalidated, stopping operations
        return true; // Prevent default error handling
    }
});

// Use a self-executing function to avoid polluting the global scope unnecessarily,
(function() {
    // Check if the script has already run on this page (e.g., due to re-injection)
    if (window.StashyInitialized) {
        return;
    }
    window.StashyInitialized = true; // Set flag

    // Test element removed - script is working properly

    // Special handling for ChatGPT to avoid React conflicts
    if (isChatGPTSite()) {
        console.log("Stashy: ChatGPT detected, using delayed initialization");

        // Wait for ChatGPT to finish loading, then initialize
        const initWithDelay = async () => {
            try {
                await waitForChatGPTReady();
                console.log("Stashy: ChatGPT ready, initializing Stashy");
                initStashy();
            } catch (error) {
                console.error("Stashy: Error waiting for ChatGPT:", error);
                // Fallback to normal initialization
                setTimeout(initStashy, 2000);
            }
        };

        initWithDelay();
    } else {
        // Normal initialization for other sites
        // Wait for the DOM to be ready before initializing
        if (document.readyState === 'loading') {
             console.log("Stashy: DOM loading, waiting for DOMContentLoaded."); // DEBUG
            document.addEventListener('DOMContentLoaded', initStashy);
        } else {
            // DOM is already ready
             console.log("Stashy: DOM already ready, initializing now."); // DEBUG
            initStashy();
        }
    }
})();



// Listen for messages from the popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // Check for extension context invalidation
    if (chrome.runtime.lastError) {
        console.warn("Stashy: Extension context invalidated:", chrome.runtime.lastError.message);
        return false;
    }

    // Validate the sender is from our extension
    if (sender.id !== chrome.runtime.id) {
        console.warn("Stashy: Rejected message from unauthorized sender:", sender.id);
        return false;
    }


    return true; // Keep the message channel open for async response
});

// Initialize rendering optimization module if available
if (typeof window.SB_RENDERING_OPTIMIZATION_LOADED === 'undefined') {
    // The module will set SB_RENDERING_OPTIMIZATION_LOADED to true when loaded
}

// Initialize URL utilities if available
if (typeof window.StashyURLUtils === 'undefined') {
    // The module will be loaded from lib/url-utils.js
}

// --- Premium Status Integration ---
/**
 * Initialize and apply premium status to the current page
 */
async function initializePremiumStatus() {
    try {
        if (typeof window.StashyPremium !== 'undefined') {
            // Check and apply premium status
            await window.StashyPremium.refreshPremiumStatus();
            const isPremium = window.StashyPremium.isPremium();

            console.log(`Stashy: Premium status initialized - ${isPremium ? 'PREMIUM' : 'FREE'} user`);

            // Apply premium styling to the page
            window.StashyPremium.applyPremiumStyling(isPremium);

            // Listen for premium status changes
            document.addEventListener('stashyPremiumStatusChanged', (event) => {
                console.log('Stashy: Premium status changed:', event.detail);
                window.StashyPremium.applyPremiumStyling(event.detail.isPremium);

                // Update any existing UI elements
                updatePremiumUI(event.detail.isPremium);
            });

            // Initial UI update
            updatePremiumUI(isPremium);
        } else {
            console.warn('Stashy: Premium manager not available');
        }
    } catch (error) {
        console.error('Stashy: Error initializing premium status:', error);
    }
}

/**
 * Update UI elements based on premium status
 * @param {boolean} isPremium - Whether user is premium
 */
function updatePremiumUI(isPremium) {
    // Update toolbar if it exists
    const toolbar = document.querySelector('.Stashy-toolbar');
    if (toolbar) {
        // Remove existing premium status indicator
        const existingStatus = toolbar.querySelector('.premium-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        // Add new premium status indicator
        const statusIndicator = document.createElement('div');
        statusIndicator.className = `premium-status ${isPremium ? 'premium' : 'free'}`;
        statusIndicator.innerHTML = isPremium ?
            '✨ Pro' :
            '<a href="https://stashyapp.com/pricing.html" target="_blank" style="color: inherit; text-decoration: none;">Upgrade to Pro</a>';

        toolbar.appendChild(statusIndicator);
    }

    // Update any existing premium feature buttons
    const premiumButtons = document.querySelectorAll('[data-premium-feature]');
    premiumButtons.forEach(button => {
        const feature = button.getAttribute('data-premium-feature');
        const isAvailable = window.StashyPremium?.isFeatureAvailable(feature) || isPremium;

        if (!isAvailable) {
            button.classList.add('premium-locked');
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                window.StashyPremium?.showUpgradePrompt(feature, 'toolbar');
            });
        } else {
            button.classList.remove('premium-locked');
        }
    });
}

// Initialize premium status when the page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePremiumStatus);
} else {
    // DOM is already loaded
    setTimeout(initializePremiumStatus, 100); // Small delay to ensure all modules are loaded
}

// Main Script Loaded (v1.9 - Premium Integration Added)
// --- END OF FILE content-main.js ---