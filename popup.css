/* --- START OF FILE popup.css --- */
/* Using system fonts instead of Google Fonts to avoid CSP issues */

:root {
    /* --- Vibrant Color Palette --- */
    --primary-color: #34D399; /* Emerald Green */
    --primary-color-hover: #10B981;
    --primary-color-darker: #059669;
    --primary-color-light: #A7F3D0;
    --primary-color-lighter: #D1FAE5;
    --primary-color-rgb: 52, 211, 153; /* For RGBA */

    --secondary-color: #6B7280; /* Gray */
    --secondary-color-hover: #4B5563;
    --secondary-color-darker: #374151;
    --secondary-color-light: #E5E7EB;
    --secondary-color-lighter: #F3F4F6;

    --danger-color: #EF4444; /* Red */
    --danger-color-hover: #DC2626;
    --danger-color-darker: #B91C1C;
    --danger-color-light: #FEE2E2;

    --text-primary: #1F2937; /* Dark Gray */
    --text-secondary: #6B7280; /* Medium Gray */
    --text-light: #9CA3AF;
    --text-on-primary: #ffffff;
    --text-on-danger: #ffffff;
    --text-on-secondary-dark: #ffffff;
    --text-on-light-bg: #111827; /* Darker text on light backgrounds */

    --border-color: #D1D5DB; /* Lighter Gray */
    --card-bg: #ffffff;
    --popup-bg: #F9FAFB; /* Almost white */
    --white: #ffffff;

    /* Status Colors */
    --success-color: var(--primary-color);
    --error-color: var(--danger-color);
    --info-color: var(--text-secondary);
    --syncing-color: #3B82F6; /* Blue */
    --disconnected-color: var(--secondary-color);



    /* Shadows & Transitions */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-focus-ring: 0 0 0 3px rgba(var(--primary-color-rgb), 0.3); /* Primary focus */
    --shadow-inset: inset 0 1px 2px 0 rgb(0 0 0 / 0.05);

    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-round: 50%;

    --transition-speed: 0.2s; /* Faster for snappy feel */
    --transition-func: ease-out;

    /* Global Notes Styling */
    --global-note-bg: #EFF6FF; /* Lighter Blue */
    --global-note-border: #DBEAFE;
    --global-note-title: #2563EB; /* Standard Blue */
}

/* --- Base Styles --- */
html {
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
html::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; /* System fonts */
    padding: 18px;
    width: 370px; /* Adjusted width */
    min-height: 500px; /* Adjusted min-height */
    background-color: var(--popup-bg);
    color: var(--text-primary);
    box-sizing: border-box;
    margin: 0;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    line-height: 1.6; /* Slightly increased base line height */
    -webkit-font-smoothing: antialiased; /* Smoother fonts */
    -moz-osx-font-smoothing: grayscale;

    /* Hide scrollbar but keep functionality */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
body::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

h3 {
    margin: 0 0 10px;
    font-size: 18px; /* Slightly smaller H3 */
    color: var(--text-primary); /* Darker H3 */
    text-align: center;
    font-weight: 600;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
h3 .popup-icon {
    color: var(--primary-color); /* Color the icon */
    font-size: 1.2em;
}

p.description {
    font-size: 13px;
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-secondary);
    flex-shrink: 0;
}

/* --- User Welcome Section --- */
.user-welcome-section {
    margin-bottom: 18px;
    flex-shrink: 0;
}

.user-auth-area {
    text-align: center;
    padding: 16px;
    background: linear-gradient(135deg, var(--primary-color-lighter) 0%, var(--white) 100%);
    border: 1px solid var(--primary-color-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.user-auth-button {
    background: linear-gradient(145deg, var(--primary-color), var(--primary-color-hover));
    color: var(--text-on-primary);
    border: none;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 600;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-speed) var(--transition-func);
    min-width: 120px;
}

.user-auth-button:hover:not(:disabled) {
    background: linear-gradient(145deg, var(--primary-color-hover), var(--primary-color-darker));
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.auth-description {
    margin: 12px 0 0 0;
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.user-info-display {
    padding: 16px;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

.avatar-img {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-round);
    object-fit: cover;
    border: 2px solid var(--primary-color-light);
    box-shadow: var(--shadow-sm);
}

.avatar-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color-light), var(--primary-color-lighter));
    border-radius: var(--border-radius-round);
    font-size: 18px;
    color: var(--primary-color-darker);
    border: 2px solid var(--primary-color-light);
    box-shadow: var(--shadow-sm);
}

.user-details {
    flex-grow: 1;
    min-width: 0;
}

.user-name {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    font-size: 13px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sign-out-button {
    background: var(--secondary-color-lighter);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 8px;
    border-radius: var(--border-radius-md);
    font-size: 14px;
    min-width: auto;
    width: 36px;
    height: 36px;
    flex-shrink: 0;
    transition: all var(--transition-speed) var(--transition-func);
}

.sign-out-button:hover:not(:disabled) {
    background: var(--danger-color-light);
    color: var(--danger-color-darker);
    border-color: var(--danger-color);
    transform: translateY(-1px);
}

.sign-out-button:active:not(:disabled) {
    background: var(--danger-color);
    color: var(--text-on-danger);
    transform: translateY(0);
}

.hidden {
    display: none !important;
}

.auth-notice {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 8px;
    text-align: center;
    font-style: italic;
}

/* --- Search Area --- */
.search-area {
    display: flex;
    gap: 8px;
    margin-bottom: 18px;
    flex-shrink: 0;
    position: relative;
}

input[type="text"]#search-query {
    flex-grow: 1;
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: 14px;
    background-color: var(--white);
    color: var(--text-primary);
    box-sizing: border-box;
    transition: border-color var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func);
    box-shadow: var(--shadow-inset);
}
input[type="text"]#search-query:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-focus-ring), var(--shadow-inset);
}
input[type="text"]#search-query::placeholder {
    color: var(--text-light);
    font-style: normal;
    font-size: 14px;
}

/* --- Base Button Style --- */
button {
    padding: 9px 14px;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    transition: transform var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func),
                background-color var(--transition-speed) var(--transition-func),
                background-image var(--transition-speed) var(--transition-func),
                color var(--transition-speed) var(--transition-func),
                filter var(--transition-speed) var(--transition-func);
    box-shadow: var(--shadow-sm);
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-sizing: border-box;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    filter: brightness(1.03);
}
button:focus-visible {
    outline: none;
    box-shadow: var(--shadow-focus-ring), var(--shadow-sm);
}
button:active:not(:disabled) {
    transform: translateY(0px) scale(0.99);
    box-shadow: var(--shadow-inset);
    filter: brightness(0.97);
}
button:disabled {
    background-color: var(--secondary-color-lighter) !important;
    background-image: none !important;
    border-color: var(--secondary-color-lighter) !important;
    color: var(--text-light) !important;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    filter: none !important;
    opacity: 0.8;
}

/* Icon Styling within Buttons */
.popup-icon {
    font-size: 1.1em;
    line-height: 1;
    display: inline-block;
    transition: transform var(--transition-speed) var(--transition-func);
}
button:hover:not(:disabled) .popup-icon {
    transform: scale(1.1) rotate(-5deg);
}

/* --- Search Button (Primary) --- */
button#search-button {
    background-image: linear-gradient(145deg, var(--primary-color), var(--primary-color-hover));
    background-color: var(--primary-color); /* Fallback */
    color: var(--text-on-primary);
    min-width: 95px;
    padding: 10px 16px;
    font-weight: 600;
    flex-grow: 0;
    box-shadow: var(--shadow-md);
    border: none;
}
button#search-button:hover:not(:disabled) {
    background-image: linear-gradient(145deg, var(--primary-color-hover), var(--primary-color-darker));
    background-color: var(--primary-color-hover);
    box-shadow: var(--shadow-lg);
    filter: none;
    transform: translateY(-2px) scale(1.02);
}
button#search-button:active:not(:disabled) {
     background-image: none;
     background-color: var(--primary-color-darker);
     filter: brightness(0.95);
     transform: translateY(0px) scale(1);
     box-shadow: var(--shadow-inset);
}
button#search-button .popup-icon {
    transform-origin: center;
    transition: transform 0.3s cubic-bezier(.17,.67,.83,.67);
}
button#search-button:hover:not(:disabled) .popup-icon {
    transform: scale(1.2) rotate(15deg);
}

/* --- Action Buttons Container --- */
/* Base style for ALL action button containers */
.action-buttons {
    margin-top: 10px;
    margin-bottom: 5px;
    display: flex;
    gap: 8px; /* Space between items in the row */
    flex-shrink: 0;
    align-items: center; /* Vertically align items */
}

/* --- Common Export Controls Row --- */
.common-export-controls {
    margin-bottom: 10px; /* Add space below the select */
    justify-content: flex-start; /* Align label and select left */
    padding: 4px;
    background-color: var(--secondary-color-lighter); /* Subtle background */
    border-radius: var(--border-radius-md);
}
.export-format-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-right: 8px;
    flex-shrink: 0; /* Prevent label from shrinking */
}
select#common-export-format-select {
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--white);
    font-size: 13px; /* Slightly smaller */
    color: var(--text-primary);
    cursor: pointer;
    transition: border-color var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func),
                background-color var(--transition-speed) var(--transition-func);
    appearance: none; /* Remove default arrow */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236B7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e"); /* Use SVG color variable */
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 30px; /* Space for the arrow */
    flex-grow: 1; /* Allow select to take remaining space */
    min-width: 100px; /* Ensure it's not too small */
    box-shadow: var(--shadow-sm);
}
select#common-export-format-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus-ring), var(--shadow-sm);
}
select#common-export-format-select:hover {
    border-color: var(--secondary-color); /* Darker border on hover */
}
select#common-export-format-select:disabled {
    background-color: var(--secondary-color-lighter) !important;
    border-color: var(--border-color) !important;
    color: var(--text-light) !important;
    cursor: not-allowed;
    box-shadow: none !important;
    opacity: 0.8;
}

/* --- Export Action Buttons Row --- */


/* --- Export Buttons (Secondary Look) --- */
button#export-notes,
button#export-highlights {
    background: linear-gradient(to bottom, var(--white) 0%, #f9fafb 100%);
    color: var(--secondary-color-darker);
    border: 1px solid var(--border-color);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
    flex: 1; /* Make buttons fill space equally */
    padding: 9px 14px; /* Ensure standard padding */
    border-radius: var(--border-radius-md); /* Standard radius */
}
button#export-notes:hover:not(:disabled),
button#export-highlights:hover:not(:disabled) {
    border-color: var(--secondary-color-light);
    background: linear-gradient(to bottom, #fdfdfd 0%, #f0f2f4 100%);
    color: var(--secondary-color-darker);
    box-shadow: var(--shadow-md); /* Standard hover shadow */
    filter: none;
}
button#export-notes:active:not(:disabled),
button#export-highlights:active:not(:disabled) {
    background: var(--secondary-color-lighter);
    color: var(--secondary-color-darker);
    filter: brightness(0.98);
    box-shadow: var(--shadow-inset);
}

/* --- Clear Button (Danger Look) --- */
.other-page-actions button#clear-note {
    flex: 1; /* Allow clear button to take full width in its row */
    background: linear-gradient(to bottom, var(--danger-color-light) 0%, #FEE7E7 100%);
    color: var(--danger-color-darker);
    border: 1px solid #FECACA; /* Lighter red border */
    font-weight: 500;
    box-shadow: none;
}
.other-page-actions button#clear-note:hover:not(:disabled) {
    background: var(--danger-color);
    color: var(--text-on-danger);
    border-color: var(--danger-color-hover);
    box-shadow: var(--shadow-md);
    filter: none;
}
.other-page-actions button#clear-note:active:not(:disabled) {
     background: var(--danger-color-darker);
     color: var(--text-on-danger);
     filter: brightness(0.95);
     box-shadow: var(--shadow-inset);
}

/* --- Sync Buttons --- */
.sync-action {
    justify-content: space-between; /* Space out sync buttons/indicator */
}
button#connect-drive-button {
    flex-grow: 1; /* Allow connect button to take available space */
    background: linear-gradient(to bottom, var(--primary-color-lighter) 0%, #DFFEEE 100%);
    color: var(--primary-color-darker);
    border: 1px solid var(--primary-color-light);
    font-weight: 600;
    box-shadow: none;
}
button#connect-drive-button:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--text-on-primary);
    border-color: var(--primary-color-hover);
    box-shadow: var(--shadow-md);
    filter: none;
}
button#disconnect-drive-button {
    flex-grow: 1; /* Allow disconnect button to take available space */
    background: linear-gradient(to bottom, var(--danger-color-light) 0%, #FEE7E7 100%);
    color: var(--danger-color-darker);
    border: 1px solid var(--danger-color);
    font-weight: 600;
    box-shadow: none;
}
button#disconnect-drive-button:hover:not(:disabled) {
    background: var(--danger-color);
    color: var(--text-on-danger);
    border-color: var(--danger-color-hover);
    box-shadow: var(--shadow-md);
    filter: none;
}

/* Sync Status Indicator */
#sync-status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: var(--border-radius-round);
    background-color: var(--disconnected-color);
    transition: all var(--transition-speed) var(--transition-func);
    margin-left: 8px;
    box-shadow: var(--shadow-inset);
    vertical-align: middle;
    flex-shrink: 0; /* Prevent indicator from shrinking */
}
#sync-status-indicator.connected {
    background-color: var(--success-color);
    box-shadow: 0 0 8px 0px rgba(var(--primary-color-rgb), 0.6);
}
#sync-status-indicator.syncing {
    background-color: var(--syncing-color);
    animation: pulse-sync 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
#sync-status-indicator.error {
    background-color: var(--error-color);
    animation: pulse-error 1.2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
#sync-status-indicator.disconnected {
    background-color: var(--disconnected-color);
    opacity: 0.7;
    box-shadow: none;
}

@keyframes pulse-sync {
  0%, 100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); transform: scale(1); }
  50% { box-shadow: 0 0 0 6px rgba(59, 130, 246, 0); transform: scale(1.1); }
}
@keyframes pulse-error {
  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.5); transform: scale(1);}
  50% { box-shadow: 0 0 0 7px rgba(239, 68, 68, 0); transform: scale(1.15); }
}

/* --- Dashboard Button --- */
.dashboard-action button#view-dashboard-button {
    flex: 1; /* Allow to take full width */
    background: linear-gradient(to bottom, var(--white) 0%, #f9fafb 100%);
    color: var(--secondary-color-darker);
    border: 1px solid var(--border-color);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}
.dashboard-action button#view-dashboard-button:hover:not(:disabled) {
    border-color: var(--secondary-color-light);
    background: linear-gradient(to bottom, #fdfdfd 0%, #f0f2f4 100%);
    color: var(--secondary-color-darker);
    box-shadow: var(--shadow-md);
    filter: none;
}
.dashboard-action button#view-dashboard-button:active:not(:disabled) {
    background: var(--secondary-color-lighter);
    color: var(--secondary-color-darker);
    filter: brightness(0.98);
    box-shadow: var(--shadow-inset);
}

/* --- Help Links (in Help & Info Panel) --- */
.help-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 10px;
}

.help-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-speed) var(--transition-func);
    border: 1px solid var(--border-color);
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.help-link:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-lighter);
    border-color: var(--primary-color-light);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.help-link .popup-icon {
    font-size: 1.1em;
    transition: transform var(--transition-speed) var(--transition-func);
}

.help-link:hover .popup-icon {
    transform: scale(1.1);
}

/* --- Settings Buttons --- */
.settings-actions {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    width: 100%;
}

/* Enhanced Main Settings Button */
button#main-settings-button {
    background: linear-gradient(135deg, var(--primary-color-lighter), var(--primary-color-light));
    color: var(--primary-color-darker);
    border: 1px solid var(--primary-color);
    font-weight: 600;
    padding: 12px 18px; /* Increased padding for better touch target */
    border-radius: var(--border-radius-lg); /* Larger border radius */
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%; /* Full width */
}

button#main-settings-button:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
    color: var(--text-on-primary);
    border-color: var(--primary-color-hover);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(var(--primary-color-rgb), 0.3);
}

button#main-settings-button:active:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-color-hover), var(--primary-color-darker));
    transform: translateY(0px) scale(1);
    box-shadow: var(--shadow-inset);
}

/* Enhanced icon animation for settings button */
button#main-settings-button .popup-icon {
    transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

button#main-settings-button:hover:not(:disabled) .popup-icon {
    transform: rotate(180deg) scale(1.1);
}

/* Add subtle pulse animation when dropdown is open */
button#main-settings-button.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
    color: var(--text-on-primary);
    border-color: var(--primary-color-hover);
    box-shadow: var(--shadow-lg), 0 0 15px rgba(var(--primary-color-rgb), 0.4);
}

button#main-settings-button.active .popup-icon {
    transform: rotate(180deg) scale(1.1);
    animation: settings-pulse 2s ease-in-out infinite;
}

@keyframes settings-pulse {
    0%, 100% { transform: rotate(180deg) scale(1.1); }
    50% { transform: rotate(180deg) scale(1.2); }
}

/* Voice Settings Button */
.settings-actions button#voice-settings-button {
    flex: 1; /* Take equal width */
    background: linear-gradient(to bottom, #f0e6ff 0%, #e9d5ff 100%); /* Light purple gradient */
    color: #6b21a8; /* Darker purple text */
    border: 1px solid #d8b4fe; /* Purple border */
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}
.settings-actions button#voice-settings-button:hover:not(:disabled) {
    border-color: #c084fc;
    background: linear-gradient(to bottom, #e9d5ff 0%, #d8b4fe 100%);
    color: #581c87;
    box-shadow: var(--shadow-md);
    filter: none;
}
.settings-actions button#voice-settings-button:active:not(:disabled) {
    background: #c084fc;
    color: #581c87;
    filter: brightness(0.98);
    box-shadow: var(--shadow-inset);
}

/* Note Settings Button */
.settings-actions button#app-settings-button {
    flex: 1; /* Take equal width */
    background: linear-gradient(to bottom, #dbeafe 0%, #bfdbfe 100%); /* Light blue gradient */
    color: #1e40af; /* Darker blue text */
    border: 1px solid #93c5fd; /* Blue border */
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}
.settings-actions button#app-settings-button:hover:not(:disabled) {
    border-color: #60a5fa;
    background: linear-gradient(to bottom, #bfdbfe 0%, #93c5fd 100%);
    color: #1e3a8a;
    box-shadow: var(--shadow-md);
    filter: none;
}
.settings-actions button#app-settings-button:active:not(:disabled) {
    background: #93c5fd;
    color: #1e3a8a;
    filter: brightness(0.98);
    box-shadow: var(--shadow-inset);
}


/* --- Results Areas --- */
#search-results,
#global-notes-results .results-content {
    padding: 10px 12px;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-inset);
    font-size: 13px;
    overflow-y: auto;
    /* --- HIDE SCROLLBAR --- */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
#search-results::-webkit-scrollbar,
#global-notes-results .results-content::-webkit-scrollbar {
    display: none; /* Hide scrollbar for WebKit browsers */
}

/* Search Results Specific */
#search-results {
    margin-top: 18px;
    margin-bottom: 18px;
    flex-grow: 1; /* Allow search results to take remaining vertical space */
    min-height: 120px; /* Ensure it has some minimum height */
}
#search-results .result-item {
     margin-bottom: 8px;
     padding: 10px;
     border-bottom: 1px solid var(--secondary-color-lighter);
     line-height: 1.5;
     border-radius: var(--border-radius-sm);
     transition: background-color var(--transition-speed) var(--transition-func),
                 transform var(--transition-speed) var(--transition-func),
                 box-shadow var(--transition-speed) var(--transition-func);
     position: relative;
 }
#search-results .result-item:hover {
     background-color: var(--primary-color-lighter);
     border-bottom-color: transparent;
     transform: scale(1.01); /* Slight scale instead of translate */
     box-shadow: var(--shadow-sm);
}
#search-results .result-item:last-child {
     border-bottom: none; margin-bottom: 0; padding-bottom: 10px;
 }
#search-results .no-results {
     text-align: center;
     color: var(--text-light);
     padding: 25px 10px;
     font-style: normal;
     font-size: 14px;
 }

/* Global Notes Section Specific */
#global-notes-results {
    margin-top: 18px;
    margin-bottom: 18px;
    flex-shrink: 0; /* Don't let global notes shrink */
    border: 1px solid var(--global-note-border);
    border-radius: var(--border-radius-md);
    background-color: var(--global-note-bg);
    box-shadow: var(--shadow-sm);
}
#global-notes-results h3 {
    font-size: 14px;
    padding: 10px 12px;
    margin: 0;
    text-align: left;
    color: var(--global-note-title);
    border-bottom: 1px solid var(--global-note-border);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}
#global-notes-results .results-content {
    background-color: transparent;
    border: none;
    box-shadow: none;
    max-height: 140px; /* Limit height and make scrollable */
    padding: 8px 10px;
}
#global-notes-results .result-item {
     border-bottom: 1px solid #d1eafd;
     padding: 8px 4px;
     margin-bottom: 0;
     background-color: transparent;
     border-radius: var(--border-radius-sm);
     transition: background-color var(--transition-speed) var(--transition-func),
                 transform var(--transition-speed) var(--transition-func);
 }
 #global-notes-results .result-item:hover {
     background-color: #dcf1ff;
     border-bottom-color: transparent;
     transform: scale(1.01); /* Consistent scale */
}
#global-notes-results .result-item:last-child {
     border-bottom: none; padding-bottom: 8px;
 }
#global-notes-results .no-results {
     text-align: center;
     color: var(--global-note-title);
     opacity: 0.8;
     padding: 15px 10px;
     font-style: normal;
     font-size: 13px;
 }

/* Common Result Item Styles */
 .result-url {
     color: var(--primary-color-darker);
     font-weight: 500;
     cursor: pointer;
     display: flex;
     align-items: center;
     gap: 6px;
     margin-bottom: 2px;
     overflow: hidden;
     text-decoration: none;
     font-size: 13px;
     transition: color var(--transition-speed) var(--transition-func);
 }

 .result-url-text {
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap;
     flex: 1;
 }
 .result-url:hover,
 .result-url:focus-visible {
      color: var(--primary-color);
     text-decoration: underline;
     outline: none;
 }
 .result-details {
     font-size: 11px;
     color: var(--text-light);
     display: block;
     margin-bottom: 4px;
     white-space: nowrap;
     overflow: hidden;
     text-overflow: ellipsis;
 }
 .result-snippet {
    margin: 0;
    color: var(--text-secondary);
    word-break: break-word;
    line-height: 1.5;
    /* Limit snippet height in popup */
    max-height: 3em; /* Approx 2 lines */
    overflow: hidden;
  }
 mark {
      background-color: #FEF9C3; /* Even lighter yellow */
      color: #713F12; /* Brownish text */
      padding: 1px 3px;
      border-radius: var(--border-radius-sm);
      font-weight: 500; /* Less bold mark */
      box-shadow: 0 0 0 1px rgba(113, 63, 18, 0.1); /* Subtle border */
      transition: background-color var(--transition-speed) var(--transition-func);
  }
  mark:hover {
      background-color: #FEF08A; /* Slightly more vibrant on hover */
  }

/* --- Status message styling --- */
/* Base for BOTH status messages */
.status-message {
    padding: 8px 12px; /* Reduced padding */
    font-size: 13px;
    text-align: center;
    min-height: 1.5em;
    color: var(--info-color);
    font-weight: 500;
    transition: opacity 0.4s ease, color 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
    opacity: 0;
    transform: translateY(5px);
    flex-shrink: 0;
    border-radius: var(--border-radius-md); /* Match button radius */
    margin-bottom: 0; /* Remove bottom margin, rely on flex gap */
    margin-top: 10px; /* Add margin above */
    visibility: hidden; /* Hide initially */
}
.status-message.visible {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}
.status-message.success {
    color: var(--text-on-primary);
    background-color: var(--success-color);
}
.status-message.error {
    color: var(--text-on-danger);
    background-color: var(--error-color);
}
.status-message.info {
    color: var(--text-on-light-bg);
    background-color: var(--secondary-color-lighter);
}
.status-message.warning {
    color: #7c2d12; /* Dark orange */
    background-color: #fef3c7; /* Light yellow */
}

/* --- AI Authentication Styles --- */
.ai-auth-container {
    margin-bottom: 20px;
}

.auth-status-container {
    margin: 15px 0;
    padding: 12px;
    border-radius: 6px;
    background-color: var(--secondary-color-lighter);
    border: 1px solid var(--secondary-color-light);
}

.auth-section {
    margin-top: 20px;
    padding: 15px;
    border-radius: 6px;
    background-color: var(--secondary-color-lighter);
    border: 1px solid var(--secondary-color-light);
}

.auth-section.hidden {
    display: none;
}

/* API Key Groups */
.api-key-group {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 6px;
    background-color: #ffffff;
    border: 1px solid var(--secondary-color-light);
}

.api-provider-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 600;
}

.provider-icon {
    margin-right: 8px;
    font-size: 16px;
}

.provider-name {
    flex: 1;
    margin-right: 10px;
    color: var(--text-primary);
}

.provider-status {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    background-color: var(--secondary-color-lighter);
    color: var(--text-secondary);
}

/* API Key Input Container */
.api-key-input-container {
    display: flex;
    align-items: center;
    position: relative;
}

.api-key-input-container input[type="password"],
.api-key-input-container input[type="text"] {
    flex: 1;
    padding-right: 40px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    border: 1px solid var(--secondary-color-light);
    border-radius: var(--border-radius-sm);
    padding: 8px 12px;
}

.api-key-input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

/* Provider Detection Styles */
.detection-progress {
    margin-top: 12px;
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    animation: fadeIn 0.3s ease-in;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    animation: progressAnimation 2s ease-in-out infinite;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.provider-detection-results {
    margin-top: 12px;
    padding: 10px;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    animation: slideIn 0.4s ease-out;
}

.detection-result {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
}

.detection-icon {
    font-size: 14px;
}

.detection-text {
    font-size: 13px;
    color: #155724;
    font-weight: 500;
}

.validation-result {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #c3e6cb;
}

.validation-icon {
    font-size: 14px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.validation-text {
    font-size: 12px;
    color: #155724;
}

.api-key-help {
    margin-top: 12px;
    padding: 12px;
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    font-size: 12px;
    color: #0066cc;
    line-height: 1.4;
}

.api-key-help a {
    color: #0066cc;
    text-decoration: none;
    font-weight: 500;
}

.api-key-help a:hover {
    text-decoration: underline;
}

/* Provider Status Indicators */
.provider-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.provider-status.not-configured {
    background-color: #f8d7da;
    color: #721c24;
}

.provider-status.configured {
    background-color: #d4edda;
    color: #155724;
}

.provider-status.validating {
    background-color: #fff3cd;
    color: #856404;
}

.provider-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* Animation Classes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

.toggle-password {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    color: var(--text-secondary);
    font-size: 14px;
    transition: all 0.2s ease;
}

.toggle-password:hover {
    background-color: var(--secondary-color-lighter);
    color: var(--text-primary);
}

/* Security Information */
.security-info {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--primary-color-lighter);
    border-radius: 6px;
    border: 1px solid var(--primary-color-light);
}

.security-info h6 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color-darker);
}

.security-list {
    margin: 0;
    padding-left: 0;
    list-style: none;
}

.security-list li {
    margin-bottom: 5px;
    font-size: 12px;
    color: var(--primary-color-darker);
}

/* Danger Button */
.danger-button {
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
    color: var(--text-on-danger) !important;
    font-weight: 600;
    border: 2px solid var(--danger-color);
    transition: all 0.2s ease;
}

.danger-button:hover {
    background-color: var(--danger-color-hover) !important;
    border-color: var(--danger-color-hover) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.danger-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
}

/* AI Settings Actions */
.ai-settings-actions {
    display: flex;
    gap: 12px;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--secondary-color-light);
}

.ai-settings-actions button {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

/* Status Indicator */
.status-indicator {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: var(--secondary-color-lighter);
    border: 1px solid var(--secondary-color-light);
}

.status-icon {
    margin-right: 8px;
    font-size: 16px;
}

.status-text {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

/* Usage Statistics */
.usage-stats-container {
    margin-top: 20px;
    display: none;
}

.usage-stats {
    padding: 15px;
    background-color: var(--primary-color-lighter);
    border-radius: 6px;
    border: 1px solid var(--primary-color-light);
}

.usage-stats h6 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color-darker);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid var(--primary-color-light);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-value {
    font-size: 12px;
    color: var(--primary-color-darker);
    font-weight: 600;
}

/* Main status message specific styling (at the very bottom) */
#main-status-message {
    margin-top: 15px; /* More space above the final status */
}

/* --- Utility Classes --- */
.hidden {
    display: none !important;
}

/* --- Settings Panels --- */
/* Common styles for all settings panels */
.settings-panel {
    position: fixed; /* Fixed position to ensure it stays in place */
    width: 370px; /* Match popup width */
    padding: 0; /* Remove padding to match popup */
    max-height: calc(100vh - 10px); /* Ensure it doesn't go off-screen */
    background-color: var(--popup-bg); /* Use popup background */
    overflow-y: auto;
    box-sizing: border-box; /* Include padding in height/width */
    display: none; /* Hidden by default */
    flex-direction: column; /* Needed for scrolling */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Stronger shadow for depth */
    /* Hide scrollbar but keep functionality */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    /* Add transition for smooth fade in/out */
    transition: opacity 0.3s ease-out;
    /* Add border for better separation */
    border: 1px solid var(--border-color);
    left: 0; /* Ensure left alignment */
}

/* Panels that appear above the settings button */
#voice-settings-panel,
#note-settings-panel {
    top: 0;
    z-index: 1000;
    /* Add animation for appearing from above */
    animation: slide-down 0.3s ease-out forwards;
    height: 500px; /* Match popup height */
}

/* Screenshot settings panel appears below the settings button */
#screenshot-settings-panel {
    top: 60px; /* Position below the settings button */
    z-index: 999; /* Slightly lower z-index than other panels */
    max-height: 500px; /* Match popup height */
    height: 500px; /* Fixed height to match popup */
    /* Add animation for appearing from below */
    animation: slide-up 0.3s ease-out forwards;
    /* Center the panel horizontally */
    left: 50%;
    transform: translateX(-50%);
}

@keyframes slide-up {
    from {
        transform: translate(-50%, 20px);
        opacity: 0;
    }
    to {
        transform: translateX(-50%);
        opacity: 1;
    }
}

@keyframes slide-down {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.settings-panel::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    /* Remove top radius as panel covers everything */
    /* border-top-left-radius: 8px; */
    /* border-top-right-radius: 8px; */
    flex-shrink: 0; /* Prevent header shrinking */
    position: sticky; /* Make header sticky */
    top: 0; /* Stick to top */
    z-index: 1; /* Above content */
}

/* Special styling for screenshot panel header */
#screenshot-settings-panel .panel-header {
    border-top-left-radius: 8px; /* Add top radius for screenshot panel */
    border-top-right-radius: 8px;
}
.panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: inherit; /* Inherit from header */
    text-align: left; /* Align left */
    justify-content: flex-start; /* Align left */
}
.close-button {
    background: none;
    border: none;
    color: var(--text-on-primary);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
    line-height: 1;
}
.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.settings-container {
    padding: 18px; /* Match popup padding */
    flex-grow: 1; /* Allow container to grow */
    overflow-y: auto; /* Enable scrolling for content */
}

.settings-section {
    margin-bottom: 24px;
    padding: 16px;
    border-radius: 8px;
    background-color: var(--white);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.settings-section:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--secondary-color-light);
}



.form-group {
    margin-bottom: 16px;
}
.form-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
}
.form-group select,
.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="number"] {
    width: 100%;
    padding: 9px 12px; /* Consistent padding */
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: 14px;
    background-color: var(--white);
    box-sizing: border-box; /* Ensure padding is included */
    transition: border-color var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func);
}
/* Specific styling for selects in voice panel */
.form-group select {
    appearance: none; /* Remove default arrow */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236B7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 35px; /* Space for the arrow */
    cursor: pointer;
}
.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus-ring);
}

/* Language count styling */
.form-group[data-count]::after {
    content: attr(data-count);
    position: absolute;
    top: 4px; /* Adjust position */
    right: 4px;
    font-size: 11px;
    color: var(--text-light);
    background-color: var(--secondary-color-lighter);
    padding: 1px 5px;
    border-radius: 10px;
    pointer-events: none; /* Don't interfere with select */
}

/* Checkbox groups */
.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
}
.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
    cursor: pointer;
}
.checkbox-group label {
    font-size: 14px;
    color: var(--text-primary);
    user-select: none;
    cursor: pointer;
    margin-bottom: 0; /* Override form-group label margin */
}
/* Feature states */
.feature-disabled {
    opacity: 0.6;
    pointer-events: none; /* Disable interaction */
}
.feature-limited::after {
    content: "⚠️"; /* Warning symbol */
    margin-left: 8px;
    font-size: 12px;
    color: #F59E0B; /* Amber color */
    cursor: help; /* Indicate hover info might be available */
}

/* API Key input */
#api-key-container {
    transition: opacity 0.3s ease, transform 0.3s ease;
    margin-bottom: 15px;
}

#api-key-container.hidden {
    opacity: 0;
    transform: translateY(-10px);
}

.api-key-input-container {
    position: relative;
}

.api-key-input-container input[type="password"] {
    padding-right: 45px; /* Make room for the toggle button */
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 4px;
    font-size: 18px;
    line-height: 1;
    transition: color 0.2s ease, transform 0.2s ease;
    border-radius: 4px;
}

.toggle-password:hover {
    color: var(--text-secondary);
    transform: translateY(-50%) scale(1.1);
    background-color: rgba(0, 0, 0, 0.05);
}

.toggle-password:active {
    transform: translateY(-50%) scale(0.95);
}

/* Provider Info Text */
.info-text {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 8px;
    padding: 8px 10px;
    background-color: var(--secondary-color-lighter);
    border-radius: var(--border-radius-sm);
    line-height: 1.5;
    border: 1px solid var(--secondary-color-light);
}
/* Specific provider info styles */
#provider-info-google { background-color: #e8f0fe; border-left: 3px solid #4285f4; }
#provider-info-azure { background-color: #e6f3ff; border-left: 3px solid #0078d4; }
#provider-info-assembly { background-color: #f0f7ff; border-left: 3px solid #6b7fd7; }

/* Slider */
.slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 4px;
}
.slider {
    flex: 1;
    height: 6px; /* Slightly thicker */
    border-radius: 3px;
    background: var(--secondary-color-light);
    outline: none;
    appearance: none;
    cursor: pointer;
}
.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--white);
}
.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid var(--white);
    box-shadow: var(--shadow-sm);
}
#silence-value {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    min-width: 75px; /* Adjusted width */
    text-align: right;
    background-color: var(--secondary-color-lighter);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
}

/* Action Buttons at bottom of settings panels */
.settings-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 24px; /* More space above buttons */
    padding-top: 16px;
    border-top: 1px solid var(--secondary-color-light);
}

/* Enhanced Settings Panel Action Buttons */
.settings-actions button {
    padding: 14px 24px; /* Increased padding for better touch targets */
    font-size: 14px;
    font-weight: 600;
    border-radius: var(--border-radius-lg); /* Larger border radius */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px; /* Space for icons */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    border: none;
}

/* Enhanced Primary Button (Save) */
.settings-actions .primary-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover));
    color: var(--text-on-primary);
    flex: 1.5;
    position: relative;
}

.settings-actions .primary-button:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-color-hover), var(--primary-color-darker));
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(var(--primary-color-rgb), 0.3);
}

.settings-actions .primary-button:active:not(:disabled) {
    transform: translateY(0px) scale(0.98);
    box-shadow: var(--shadow-inset);
}

/* Enhanced Secondary Button (Reset) */
.settings-actions .secondary-button {
    background: linear-gradient(135deg, var(--white), var(--secondary-color-lighter));
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    margin-right: 12px;
    flex: 1;
    white-space: nowrap;
    min-width: 140px;
}

.settings-actions .secondary-button:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--secondary-color-lighter), var(--secondary-color-light));
    border-color: var(--secondary-color);
    color: var(--secondary-color-darker);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.settings-actions .secondary-button:active:not(:disabled) {
    transform: translateY(0px) scale(0.98);
    box-shadow: var(--shadow-inset);
}

/* Add ripple effect for settings buttons */
.settings-actions button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.4s ease, height 0.4s ease;
    pointer-events: none;
}

.settings-actions button:active::before {
    width: 300px;
    height: 300px;
}

/* Icon animations for settings buttons */
.settings-actions button .button-icon {
    transition: transform 0.3s ease;
}

.settings-actions button:hover:not(:disabled) .button-icon {
    transform: scale(1.1) rotate(-5deg);
}



/* Settings panels status messages */
#voice-settings-panel #status-message,
#note-settings-panel #note-status-message {
    margin-top: 15px;
    margin-bottom: 0; /* Remove bottom margin */
}

/* Settings icons */
.settings-icon {
    display: inline-block;
    margin-right: 8px;
    font-size: 16px;
}

.button-icon {
    display: inline-block;
    margin-right: 6px;
    font-size: 14px;
}

/* Setting descriptions */
.setting-description {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 5px 0 15px 0;
    line-height: 1.4;
}



/* Theme toggle button styling */
.theme-toggle-container {
    display: flex;
    align-items: center;
    margin: 15px 0;
}



.theme-label {
    margin-left: 15px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

body.dark-mode .theme-label {
    color: var(--dark-text-primary);
}

/* Input with unit styling */
.input-with-unit {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 5px;
}

.input-with-unit input[type="number"] {
    width: 80px;
    padding: 8px 10px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.input-with-unit input[type="number"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 211, 153, 0.2);
    outline: none;
}

.input-with-unit .unit {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}





/* Screenshot Settings Specific Styles */
.storage-location-group {
    margin-bottom: 20px;
}

.storage-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.storage-option {
    display: flex;
    align-items: flex-start;
}

.storage-option input[type="radio"] {
    margin-top: 4px;
    margin-right: 10px;
}

.storage-option label {
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.storage-icon {
    margin-right: 8px;
    font-size: 16px;
}

.storage-label {
    font-weight: bold;
    margin-bottom: 2px;
}

.storage-description {
    font-size: 12px;
    color: var(--text-secondary);
}

body.dark-mode .storage-description {
    color: var(--dark-text-secondary);
}

.privacy-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.privacy-option {
    display: flex;
    align-items: flex-start;
}

.privacy-option input[type="radio"] {
    margin-top: 4px;
    margin-right: 10px;
}

.privacy-option label {
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.privacy-icon {
    margin-right: 8px;
    font-size: 16px;
}

.privacy-label {
    font-weight: bold;
    margin-bottom: 2px;
}

.privacy-description {
    font-size: 12px;
    color: var(--text-secondary);
}

body.dark-mode .privacy-description {
    color: var(--dark-text-secondary);
}

.auto-delete-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.auto-delete-container input[type="range"] {
    flex: 1;
}

.auto-delete-value {
    min-width: 60px;
    text-align: center;
    font-weight: bold;
    color: var(--primary-color);
}

body.dark-mode .auto-delete-value {
    color: var(--dark-primary-color);
}

.auto-delete-timeline {
    display: flex;
    align-items: center;
    margin-top: 5px;
    margin-bottom: 10px;
    padding: 0 10px;
}

.timeline-now, .timeline-deletion {
    font-size: 12px;
    color: var(--text-secondary);
}

body.dark-mode .timeline-now,
body.dark-mode .timeline-deletion {
    color: var(--dark-text-secondary);
}

.timeline-bar {
    flex: 1;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    margin: 0 10px;
    position: relative;
}

body.dark-mode .timeline-bar {
    background-color: #444;
}

.timeline-marker {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: var(--primary-color);
    border-radius: 50%;
    top: -3px;
    left: 0%;
    transform: translateX(-50%);
}

body.dark-mode .timeline-marker {
    background-color: var(--dark-primary-color);
}



/* --- UI Customization Panel Styles --- */
/* Customization list container */
.customization-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
}

/* Individual customization item */
.customization-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-speed) var(--transition-func);
    box-shadow: var(--shadow-sm);
}

.customization-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* Item controls (icon, label, and checkbox) */
.item-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.item-controls label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    margin: 0;
    user-select: none;
    flex: 1;
}

/* Item icons */
.item-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

/* Blue checkboxes as per user preference */
.ui-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #3B82F6; /* Blue color */
    cursor: pointer;
    margin: 0;
}

.ui-checkbox:checked {
    accent-color: #2563EB; /* Darker blue when checked */
}

/* Reorder controls */
.reorder-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.reorder-btn {
    background: linear-gradient(135deg, var(--secondary-color-lighter), var(--secondary-color-light));
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 28px;
    height: 28px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) var(--transition-func);
    padding: 0;
    box-shadow: var(--shadow-sm);
}

.reorder-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-color-light), var(--primary-color));
    color: var(--text-on-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.reorder-btn:active:not(:disabled) {
    transform: translateY(0) scale(0.95);
    box-shadow: var(--shadow-inset);
}

.reorder-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--secondary-color-lighter);
    color: var(--text-light);
}

/* Drag handle */
.drag-handle {
    color: var(--text-light);
    font-size: 16px;
    cursor: grab;
    padding: 4px;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed) var(--transition-func);
    user-select: none;
}

.drag-handle:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-lighter);
}

.drag-handle:active {
    cursor: grabbing;
    color: var(--primary-color-darker);
}

/* Dragging state */
.customization-item.dragging {
    opacity: 0.7;
    transform: rotate(2deg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
}

.customization-item.drag-over {
    border-color: var(--primary-color);
    background-color: var(--primary-color-lighter);
}

/* UI Elements section - no reorder controls, different layout */
#ui-elements-list .customization-item {
    justify-content: space-between;
}

#ui-elements-list .reorder-controls {
    display: none;
}

#ui-elements-list .item-controls {
    justify-content: space-between;
    width: 100%;
}

#ui-elements-list .item-controls .ui-checkbox {
    margin-left: auto;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 400px) {
    .customization-item {
        padding: 10px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .reorder-controls {
        align-self: flex-end;
    }

    .reorder-btn {
        width: 24px;
        height: 24px;
        font-size: 11px;
    }
}

/* Animation for adding/removing items */
.customization-item.item-added {
    animation: item-slide-in 0.3s ease-out;
}

.customization-item.item-removed {
    animation: item-slide-out 0.3s ease-in forwards;
}

@keyframes item-slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes item-slide-out {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(20px);
    }
}

/* --- Settings Dropdown Menu --- */
.settings-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
}

.dropdown-menu {
    display: none;
    position: absolute;
    bottom: 100%; /* Changed from top: 100% to bottom: 100% to appear upward */
    left: 0;
    right: 0;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1001;
    margin-bottom: 8px; /* Changed from margin-top to margin-bottom */
    overflow: hidden;
    animation: dropdown-fade-in-upward 0.3s cubic-bezier(0.16, 1, 0.3, 1); /* Enhanced animation */
    transform-origin: bottom center; /* Set transform origin for upward animation */
}

@keyframes dropdown-fade-in-upward {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95); /* Start from below and smaller */
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1); /* End at normal position and size */
    }
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    padding: 12px 16px; /* Increased padding for better touch targets */
    cursor: pointer;
    transition: all var(--transition-speed) var(--transition-func);
    display: flex;
    align-items: center;
    gap: 10px; /* Increased gap for better spacing */
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    border-radius: 0; /* Remove border radius for seamless dropdown */
    position: relative;
    overflow: hidden;
}

/* Add subtle border between items */
.dropdown-item:not(:last-child) {
    border-bottom: 1px solid var(--secondary-color-lighter);
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color-lighter), var(--primary-color-light));
    color: var(--primary-color-darker);
    transform: translateX(2px); /* Subtle slide effect */
}

.dropdown-item:active {
    background: linear-gradient(135deg, var(--primary-color-light), var(--primary-color));
    color: var(--text-on-primary);
    transform: translateX(0px) scale(0.98);
}

/* Enhanced icon styling */
.dropdown-item .popup-icon {
    font-size: 1.2em;
    transition: transform var(--transition-speed) var(--transition-func);
    width: 20px; /* Fixed width for alignment */
    text-align: center;
}

.dropdown-item:hover .popup-icon {
    transform: scale(1.1) rotate(-5deg);
}

/* Add ripple effect on click */
.dropdown-item::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(var(--primary-color-rgb), 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.dropdown-item:active::before {
    width: 200px;
    height: 200px;
}





.settings-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
}

.setting-description {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 14px;
    color: var(--text-primary);
}

.input-with-unit {
    display: flex;
    align-items: center;
    gap: 5px;
}

.input-with-unit input {
    width: 80px;
    padding: 6px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--white);
    color: var(--text-primary);
}

.input-with-unit input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-focus-ring);
}

.unit {
    color: var(--text-secondary);
    font-size: 13px;
}

.setting-hint {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 4px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.checkbox-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.settings-icon {
    color: var(--primary-color);
}

/* Settings action buttons */
.settings-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.primary-button {
    background: linear-gradient(to bottom, var(--primary-color) 0%, var(--primary-color-hover) 100%);
    color: var(--text-on-primary);
    border: none;
    font-weight: 600;
    flex: 1;
}

.primary-button:hover:not(:disabled) {
    background: linear-gradient(to bottom, var(--primary-color-hover) 0%, var(--primary-color-darker) 100%);
}

.secondary-button {
    background: linear-gradient(to bottom, var(--white) 0%, var(--secondary-color-lighter) 100%);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    font-weight: 500;
    flex: 1;
}

.secondary-button:hover:not(:disabled) {
    background: linear-gradient(to bottom, var(--secondary-color-lighter) 0%, var(--secondary-color-light) 100%);
}

.button-icon {
    font-size: 1.1em;
}



/* --- Drag and Drop Styles --- */
.customization-item {
    transition: all 0.2s ease;
    position: relative;
}

.customization-item.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    border: 2px dashed var(--primary-color);
    background-color: var(--primary-color-lighter);
}

.drag-handle {
    cursor: grab;
    user-select: none;
    color: var(--text-secondary);
    font-weight: bold;
    padding: 2px 4px;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed) var(--transition-func);
}

.drag-handle:hover {
    background-color: var(--secondary-color-lighter);
    color: var(--text-primary);
    transform: scale(1.1);
}

.drag-handle:active {
    cursor: grabbing;
    background-color: var(--primary-color-light);
    color: var(--primary-color-darker);
}

/* Visual feedback for drag over */
.customization-item.drag-over {
    border-top: 3px solid var(--primary-color);
    margin-top: 3px;
}

.customization-item.drag-over-bottom {
    border-bottom: 3px solid var(--primary-color);
    margin-bottom: 3px;
}

/* Smooth transitions for reordering */
.customization-list {
    transition: all 0.3s ease;
}

.customization-item:not(.dragging) {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Enhanced visual feedback for drag handle */
.reorder-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.drag-handle {
    font-size: 16px;
    line-height: 1;
    min-width: 20px;
    text-align: center;
}

/* --- AI Features Styles --- */

/* AI Status Indicator */
.ai-status-container {
    margin-bottom: 15px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: var(--secondary-color-lighter);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    margin-bottom: 8px;
}

.status-icon {
    font-size: 16px;
    line-height: 1;
}

.status-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

/* AI Features List */
.ai-features-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ai-feature-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-speed) var(--transition-func);
}

.ai-feature-item:hover {
    border-color: var(--primary-color-light);
    box-shadow: var(--shadow-md);
}

.feature-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.feature-icon {
    font-size: 18px;
    line-height: 1;
    color: var(--primary-color);
}

.feature-details {
    flex: 1;
}

.feature-details label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
    cursor: pointer;
}

.feature-description {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

.ai-feature-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

/* AI Preferences */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--white);
    font-size: 14px;
    color: var(--text-primary);
    cursor: pointer;
    transition: border-color var(--transition-speed) var(--transition-func),
                box-shadow var(--transition-speed) var(--transition-func);
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236B7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 30px;
    box-shadow: var(--shadow-sm);
}

.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus-ring), var(--shadow-sm);
}

.form-group select:hover {
    border-color: var(--secondary-color);
}

/* Slider Container */
.slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 6px;
}

.slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--secondary-color-lighter);
    outline: none;
    appearance: none;
    cursor: pointer;
}

.slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-speed) var(--transition-func);
}

.slider::-webkit-slider-thumb:hover {
    background: var(--primary-color-hover);
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-speed) var(--transition-func);
}

.slider::-moz-range-thumb:hover {
    background: var(--primary-color-hover);
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

#creativity-value {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 100px;
    text-align: right;
}

/* Checkbox Groups */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 12px;
}

.checkbox-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
    cursor: pointer;
    margin-top: 2px;
    flex-shrink: 0;
}

.checkbox-group label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    flex: 1;
}

.checkbox-group .setting-description {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 2px 0 0 0;
    line-height: 1.4;
}

/* Privacy Section */
.privacy-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.privacy-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.privacy-icon {
    color: var(--success-color);
    font-size: 14px;
    line-height: 1;
    margin-top: 1px;
    flex-shrink: 0;
}

/* Settings Actions */
.settings-actions {
    display: flex;
    gap: 8px;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
}

.secondary-button {
    flex: 1;
    background: linear-gradient(to bottom, var(--white) 0%, #f9fafb 100%);
    color: var(--secondary-color-darker);
    border: 1px solid var(--border-color);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.secondary-button:hover:not(:disabled) {
    border-color: var(--secondary-color-light);
    background: linear-gradient(to bottom, #fdfdfd 0%, #f0f2f4 100%);
    color: var(--secondary-color-darker);
    box-shadow: var(--shadow-md);
    filter: none;
}

.secondary-button:active:not(:disabled) {
    background: var(--secondary-color-lighter);
    color: var(--secondary-color-darker);
    filter: brightness(0.98);
    box-shadow: var(--shadow-inset);
}

.primary-button {
    flex: 1;
    background-image: linear-gradient(145deg, var(--primary-color), var(--primary-color-hover));
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    border: none;
}

.primary-button:hover:not(:disabled) {
    background-image: linear-gradient(145deg, var(--primary-color-hover), var(--primary-color-darker));
    background-color: var(--primary-color-hover);
    box-shadow: var(--shadow-lg);
    filter: none;
    transform: translateY(-2px) scale(1.02);
}

.primary-button:active:not(:disabled) {
    background-image: none;
    background-color: var(--primary-color-darker);
    filter: brightness(0.95);
    transform: translateY(0px) scale(1);
    box-shadow: var(--shadow-inset);
}

/* AI Categories Styling */
.ai-category-description {
    margin-bottom: 20px;
}

.ai-category-description p {
    color: var(--text-secondary);
    font-size: 13px;
    margin-bottom: 8px;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    padding: 6px 0;
    font-size: 13px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--secondary-color-lighter);
}

.feature-list li:last-child {
    border-bottom: none;
}

/* Essential AI Settings */
#ai-essential-settings .form-group {
    margin-bottom: 16px;
}

#ai-essential-settings .checkbox-group {
    margin-bottom: 12px;
}

/* Dark mode adjustments for AI categories */
body.dark-mode .ai-category-description p {
    color: var(--dark-text-secondary);
}

body.dark-mode .feature-list li {
    color: var(--dark-text-primary);
    border-bottom-color: var(--dark-border-color);
}

/* AI Features Information Section */
.ai-features-info {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 20px;
    margin: 16px 0;
    box-shadow: var(--shadow-sm);
}

.ai-features-info h5 {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ai-features-info h6 {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 6px;
}

.info-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--secondary-color-lighter);
}

.info-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.info-section p {
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.5;
    margin: 0 0 8px 0;
}

.feature-benefits {
    list-style: none;
    padding: 0;
    margin: 8px 0 0 0;
}

.feature-benefits li {
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
    padding: 4px 0 4px 20px;
    position: relative;
}

.feature-benefits li::before {
    content: "✓";
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 4px;
}

/* Dark mode adjustments for AI features info */
body.dark-mode .ai-features-info {
    background: var(--dark-bg-secondary);
    border-color: var(--dark-border-color);
}

body.dark-mode .ai-features-info h5 {
    color: var(--dark-primary-color);
}

body.dark-mode .ai-features-info h6 {
    color: var(--dark-text-primary);
}

body.dark-mode .info-section {
    border-bottom-color: var(--dark-border-color);
}

body.dark-mode .info-section p,
body.dark-mode .feature-benefits li {
    color: var(--dark-text-secondary);
}

body.dark-mode .feature-benefits li::before {
    color: var(--dark-primary-color);
}

/* --- Favicon Styles for Popup --- */
.stashy-favicon {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
    border-radius: 2px;
    object-fit: contain;
    background-color: transparent;
    transition: opacity 0.2s ease;
}

.stashy-favicon:hover {
    opacity: 0.8;
}

/* Favicon in popup result URLs */
.result-url .stashy-favicon {
    margin-right: 2px;
}

/* --- Premium Status Styles --- */

/* Premium status container */
#premium-status-container {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

#premium-status-container:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Premium user styling */
.premium-user #premium-status-container {
    background: linear-gradient(135deg, #ffd700, #ffed4e) !important;
    color: #8b5a00 !important;
    border-color: #ffd700;
}

.premium-user #manage-subscription:hover {
    background: rgba(139, 90, 0, 0.3) !important;
}

/* Free user styling */
.free-user #premium-status-container {
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    border-color: #e5e7eb;
}

.free-user #upgrade-to-pro:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Premium indicators in other UI elements */
.premium-user .feature-locked {
    display: none !important;
}

.free-user .premium-only {
    opacity: 0.6;
    pointer-events: none;
}

.free-user .premium-only::after {
    content: "🔒 Pro";
    position: absolute;
    top: 2px;
    right: 2px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 10px;
    z-index: 1000;
}

/* Usage counters for free users */
.usage-counter-popup {
    font-size: 10px;
    color: var(--text-secondary);
    margin-left: 8px;
    padding: 2px 6px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    display: inline-block;
}

.free-user .usage-counter-popup.warning {
    background: #fef3c7;
    color: #d97706;
}

.free-user .usage-counter-popup.limit-reached {
    background: #fee2e2;
    color: #dc2626;
}

/* Premium badge in popup */
.premium-badge-popup {
    display: inline-block;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b5a00;
    font-size: 9px;
    font-weight: 600;
    padding: 2px 5px;
    border-radius: 6px;
    margin-left: 4px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.free-user .premium-badge-popup {
    display: none;
}

/* Upgrade prompts in popup */
.upgrade-prompt-popup {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    text-align: center;
    font-size: 12px;
    color: var(--text-primary);
}

.upgrade-prompt-popup .upgrade-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 8px;
    transition: all 0.2s ease;
}

.upgrade-prompt-popup .upgrade-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.premium-user .upgrade-prompt-popup {
    display: none;
}

/* Premium feature styling for export buttons */
.free-user button[data-premium-feature],
button.premium-locked {
    position: relative;
    opacity: 0.7;
}

.free-user button[data-premium-feature]:hover,
button.premium-locked:hover {
    opacity: 0.8;
}

.free-user button[data-premium-feature]::after,
button.premium-locked::after {
    content: "🔒";
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 10px;
    opacity: 0.8;
}

.premium-user button[data-premium-feature]::after {
    display: none;
}

/* --- Trial System Styles --- */

/* Premium & Trial Section */
.premium-trial-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    transition: all 0.3s ease;
}

.premium-trial-section:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Premium Status Container */
.premium-status-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

/* Premium Badge */
.premium-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.premium-badge.free {
    background: #e5e7eb;
    color: #6b7280;
}

.premium-badge.trial {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    animation: trialPulse 2s infinite;
}

.premium-badge.pro {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b5a00;
}

@keyframes trialPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Trial Countdown */
.trial-countdown {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #059669;
    font-weight: 500;
}

.countdown-icon {
    animation: countdownTick 1s infinite;
}

@keyframes countdownTick {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(10deg); }
}

.trial-countdown.urgent {
    color: #dc2626;
    animation: urgentBlink 1s infinite;
}

@keyframes urgentBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Trial Activation Container */
.trial-activation-container {
    text-align: center;
    margin: 8px 0;
}

.trial-button {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0 auto;
}

.trial-button:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.trial-icon {
    animation: trialBounce 2s infinite;
}

@keyframes trialBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
}

.trial-description {
    font-size: 10px;
    color: #6b7280;
    margin: 4px 0 0 0;
}

/* Usage Counters */
.usage-counters {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
}

.usage-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.usage-label {
    font-size: 9px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-bottom: 2px;
}

.usage-count {
    font-size: 11px;
    font-weight: 600;
    color: #374151;
}

.usage-count.usage-warning {
    color: #dc2626;
    animation: warningPulse 1s infinite;
}

@keyframes warningPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.trial-unlimited,
.premium-unlimited {
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 8px;
}

.trial-unlimited {
    background: #d1fae5;
    color: #059669;
}

.premium-unlimited {
    background: #fef3c7;
    color: #d97706;
}

/* Hidden state */
.hidden {
    display: none !important;
}

/* Premium lock tooltip */
.free-user button[data-premium-feature]:hover::before,
button.premium-locked:hover::before {
    content: "Requires Stashy Pro";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 4px;
}

/* --- END OF FILE popup.css --- */